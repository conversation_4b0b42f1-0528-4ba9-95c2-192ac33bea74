# 🚀 DEPLOYMENT CHECKLIST - Dynamic Field Update Feature

## 📋 Overview

**Feature:** Share Dispute Input Dialog - Dynamic Field Update  
**Goal:** Chỉ update fields mà user đã sửa, không update fields không động đến  
**Impact:** Frontend + Backend + Database  

---

## 🎯 Deployment Order

```
1. Database (Add columns)
   ↓
2. Backend (New API endpoint)
   ↓
3. Frontend (Use new API)
   ↓
4. Testing & Verification
```

---

## 📦 Step 1: Database Changes

### Files Needed:
- `database_add_new_columns.sql`

### Actions:

#### 1.1 Review SQL Script
```bash
cd /root/projects/onepay/iportal/iportal-angular/src/app/module/service-support/ss-dispute-management/list/share-dispute-input-dialog
cat database_add_new_columns.sql
```

#### 1.2 Connect to Database
```bash
sqlplus username/password@database
```

#### 1.3 Run Migration Script
```sql
@database_add_new_columns.sql
```

#### 1.4 Verify Columns Added
```sql
SELECT column_name, data_type, data_length, nullable
FROM user_tab_columns
WHERE table_name = 'TB_DISPUTE'
AND column_name IN ('S_DISPUTE_CRR', 'S_EVIDENCE');
```

**Expected Output:**
```
COLUMN_NAME      DATA_TYPE    DATA_LENGTH  NULLABLE
S_DISPUTE_CRR    VARCHAR2     500          Y
S_EVIDENCE       VARCHAR2     4000         Y
```

#### 1.5 Test Insert/Update
```sql
-- Test insert
UPDATE tb_dispute 
SET s_dispute_crr = 'TEST-CRR', s_evidence = 'TEST-EVIDENCE'
WHERE n_id = 1;

-- Verify
SELECT n_id, s_dispute_crr, s_evidence
FROM tb_dispute
WHERE n_id = 1;

-- Rollback test
ROLLBACK;
```

### ✅ Database Checklist:
- [ ] SQL script reviewed
- [ ] Connected to correct database (DEV/UAT/PROD)
- [ ] Backup taken (if PROD)
- [ ] Script executed successfully
- [ ] Columns exist
- [ ] Columns accept data
- [ ] Indexes created (if applicable)
- [ ] Test data inserted/updated
- [ ] Test data rolled back

---

## 📦 Step 2: Backend Changes

### Files Needed:
- `backend_code_updateFieldsByBatch.java`

### Actions:

#### 2.1 Add Handler Method

**File:** `DisputeHandler.java`

```bash
# Open file
vim /path/to/DisputeHandler.java

# Add method updateDisputeFieldsByBatch()
# (Copy from backend_code_updateFieldsByBatch.java)
```

#### 2.2 Add DAO Method

**File:** `DisputeDao.java`

```bash
# Open file
vim /path/to/DisputeDao.java

# Add method updateDisputeFieldsByBatch()
# (Copy from backend_code_updateFieldsByBatch.java)
```

#### 2.3 Add Imports

```java
import java.sql.Types;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
```

#### 2.4 Register Route

**File:** Router setup file

```java
router.post("/dispute/updateFieldsByBatch")
      .handler(DisputeHandler::updateDisputeFieldsByBatch);
```

#### 2.5 Build & Deploy Backend

```bash
# Build
mvn clean package

# Or gradle
gradle build

# Deploy to server
# (Your deployment process)
```

#### 2.6 Verify Endpoint

```bash
# Check endpoint exists
curl -X POST http://your-server/dispute/updateFieldsByBatch \
  -H "Content-Type: application/json" \
  -d '{
    "disputeIds": [1],
    "updatedFields": {"disputeCode": "10001"},
    "metadata": {"operatorId": 123}
  }'
```

### ✅ Backend Checklist:
- [ ] Handler method added
- [ ] DAO method added
- [ ] Imports added
- [ ] Route registered
- [ ] Code compiled successfully
- [ ] No syntax errors
- [ ] Backend deployed
- [ ] Endpoint accessible
- [ ] Test request successful
- [ ] Logs show correct SQL

---

## 📦 Step 3: Frontend Changes

### Files Changed:
1. `ss-dispute-management.service.ts` ✅ (Already done)
2. `risk-dispute-management-international-component.ts` ✅ (Already done)

### Actions:

#### 3.1 Verify Changes

```bash
cd /root/projects/onepay/iportal/iportal-angular

# Check service method added
grep -n "updateDisputeFieldsByBatch" src/app/service/ss-dispute-management.service.ts

# Check component method updated
grep -n "updateDisputesBatch" src/app/module/service-support/ss-dispute-management/list/risk-dispute-management-international/risk-dispute-management-international-component.ts
```

#### 3.2 Check Linter

```bash
npm run lint
# Or
ng lint
```

#### 3.3 Build Frontend

```bash
# Development build
ng build

# Production build
ng build --prod
```

#### 3.4 Deploy Frontend

```bash
# Copy dist to server
# (Your deployment process)
```

### ✅ Frontend Checklist:
- [ ] Service method added
- [ ] Component method updated
- [ ] Imports correct
- [ ] No linter errors
- [ ] Build successful
- [ ] No console errors
- [ ] Frontend deployed

---

## 📦 Step 4: Testing & Verification

### 4.1 Smoke Test

**Test 1: Dialog Opens**
```
1. Login to application
2. Navigate to Dispute Management
3. Select 1 dispute
4. Click "Share Dispute Input" button
5. ✅ Dialog opens
```

**Test 2: Update Single Field**
```
1. Select 1 dispute
2. Open dialog
3. Change Business Category to Blank
4. Click Update
5. ✅ Success message appears
6. ✅ Verify in database: only s_business_category changed
```

**Test 3: Update Multiple Fields**
```
1. Select 2 disputes
2. Open dialog
3. Change:
   - Business Category → Blank
   - Dispute Code → '99999'
4. Click Update
5. ✅ Success message
6. ✅ Verify in database: only 2 fields changed
```

**Test 4: No Changes**
```
1. Select dispute
2. Open dialog
3. Don't change anything
4. Click Update
5. ✅ Dialog closes
6. ✅ No API call (check network tab)
7. ✅ No database update
```

### 4.2 Integration Test

**Test API Flow:**
```bash
# Open browser console
# Select dispute and open dialog
# Change Business Category
# Click Update
# Check console logs:

Expected logs:
1. "Submit - Current Values (null = not touched, "" = selected blank):"
2. "Updated Fields: { businessCategory: '' }"
3. "API Request Body (only changed fields): { disputeIds: [...], updatedFields: {...} }"
4. "Updated X disputes"
```

### 4.3 Database Verification

```sql
-- Check recent updates
SELECT 
    n_id,
    s_business_category,
    s_dispute_code,
    s_outcome,
    s_updated_by,
    TO_CHAR(d_update, 'YYYY-MM-DD HH24:MI:SS') as last_update
FROM tb_dispute
WHERE d_update > SYSDATE - 1/24  -- Last hour
ORDER BY d_update DESC;

-- Verify:
-- 1. Only updated fields changed
-- 2. s_updated_by = correct operator ID
-- 3. d_update is recent
```

### 4.4 Performance Test

```bash
# Test with 10 disputes
1. Select 10 disputes
2. Update 1 field
3. Measure response time
4. ✅ Should be < 2 seconds

# Test with 5 fields
1. Select 3 disputes
2. Update 5 fields
3. Measure response time
4. ✅ Should be < 1 second
```

### ✅ Testing Checklist:
- [ ] Dialog opens correctly
- [ ] Single field update works
- [ ] Multiple fields update works
- [ ] No changes = no update
- [ ] Confirmation popup works (when existing data)
- [ ] Console logs correct
- [ ] API request correct format
- [ ] Backend SQL correct
- [ ] Database updates correct
- [ ] Audit trail correct
- [ ] Performance acceptable
- [ ] No errors in console
- [ ] No errors in backend logs

---

## 📦 Step 5: Rollback Plan (If Needed)

### 5.1 Frontend Rollback

```bash
# Revert to previous version
git checkout HEAD~1 src/app/service/ss-dispute-management.service.ts
git checkout HEAD~1 src/app/module/service-support/ss-dispute-management/list/risk-dispute-management-international/risk-dispute-management-international-component.ts

# Rebuild and deploy
ng build --prod
```

### 5.2 Backend Rollback

```bash
# Revert code changes
git revert <commit-hash>

# Rebuild and deploy
mvn clean package
```

### 5.3 Database Rollback

```sql
-- Remove new columns (if needed)
DROP INDEX idx_dispute_crr;
ALTER TABLE tb_dispute DROP COLUMN s_dispute_crr;
ALTER TABLE tb_dispute DROP COLUMN s_evidence;
```

**⚠️ WARNING:** Only rollback database if NO data has been written to new columns!

---

## 📊 Monitoring

### After Deployment, Monitor:

#### 1. Application Logs
```bash
# Backend logs
tail -f /path/to/backend/logs/application.log | grep "updateFieldsByBatch"

# Look for:
# - "Dynamic SQL: UPDATE tb_dispute SET ..."
# - "Updated X disputes"
# - Any errors
```

#### 2. Database Performance
```sql
-- Check for slow queries
SELECT sql_text, elapsed_time, executions
FROM v$sql
WHERE sql_text LIKE '%tb_dispute%'
AND elapsed_time > 1000000  -- > 1 second
ORDER BY elapsed_time DESC;
```

#### 3. Error Rate
```bash
# Check for errors
grep "ERROR.*updateFieldsByBatch" /path/to/logs/*.log

# Should be 0 errors
```

#### 4. Usage Statistics
```sql
-- Count updates per day
SELECT 
    TRUNC(d_update) as update_date,
    COUNT(*) as updates_count
FROM tb_dispute
WHERE d_update > SYSDATE - 7  -- Last 7 days
GROUP BY TRUNC(d_update)
ORDER BY update_date DESC;
```

---

## 🎯 Success Criteria

### ✅ Deployment Successful If:

1. **Database:**
   - [ ] New columns exist
   - [ ] Columns accept data
   - [ ] No errors in database logs

2. **Backend:**
   - [ ] New endpoint accessible
   - [ ] Dynamic SQL generates correctly
   - [ ] Updates only changed fields
   - [ ] No errors in backend logs

3. **Frontend:**
   - [ ] Dialog works correctly
   - [ ] API calls use new endpoint
   - [ ] Console logs show correct data
   - [ ] No errors in browser console

4. **Integration:**
   - [ ] End-to-end flow works
   - [ ] Only changed fields updated
   - [ ] Unchanged fields remain same
   - [ ] Performance acceptable (< 2s)

5. **User Experience:**
   - [ ] Dialog responsive
   - [ ] Success/error messages clear
   - [ ] No unexpected behavior
   - [ ] Data updates correctly

---

## 📝 Post-Deployment Tasks

### 1. Documentation
- [ ] Update API documentation
- [ ] Update user guide (if needed)
- [ ] Document any issues encountered

### 2. Communication
- [ ] Notify team of deployment
- [ ] Share testing results
- [ ] Document lessons learned

### 3. Cleanup
- [ ] Remove old code (if any)
- [ ] Archive old API (if deprecating)
- [ ] Clean up test data

---

## 🎉 Deployment Complete!

**Date:** _____________  
**Deployed By:** _____________  
**Environment:** [ ] DEV [ ] UAT [ ] PROD  
**Status:** [ ] SUCCESS [ ] PARTIAL [ ] FAILED  

**Notes:**
_____________________________________________
_____________________________________________
_____________________________________________

**Sign-off:**
- Developer: _____________
- QA: _____________
- DevOps: _____________
- Product Owner: _____________

---

## 📞 Support Contacts

**If issues arise:**

- **Frontend:** [Your Name/Team]
- **Backend:** [Backend Team]
- **Database:** [DBA Team]
- **DevOps:** [DevOps Team]

**Escalation:**
- Level 1: Team Lead
- Level 2: Technical Manager
- Level 3: CTO

---

## 📚 Reference Documents

1. `SOLUTION_DYNAMIC_FIELD_UPDATE.md` - Full technical solution
2. `TESTING_GUIDE_DYNAMIC_UPDATE.md` - Comprehensive testing guide
3. `ANALYSIS_FRONTEND_BACKEND_COMPATIBILITY.md` - Problem analysis
4. `backend_code_updateFieldsByBatch.java` - Backend code template
5. `database_add_new_columns.sql` - Database migration script

---

**Good luck with deployment! 🚀**

