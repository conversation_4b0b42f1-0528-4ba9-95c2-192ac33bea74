# Share Dispute Input Dialog - Implementation Summary

## Tổng quan (Overview)
Component mới `share-dispute-input-dialog` đã được tạo để hỗ trợ cập nhật hàng loạt (batch update) nhiều dispute cùng lúc với đầy đủ các trường thông tin.

## Vị trí (Location)
```
/root/projects/onepay/iportal/iportal-angular/src/app/module/service-support/ss-dispute-management/list/share-dispute-input-dialog/
├── share-dispute-input-dialog.component.ts
├── share-dispute-input-dialog.component.html
├── share-dispute-input-dialog.component.scss
└── USAGE_EXAMPLE.md
```

Component này nằm đồng cấp với component `update-dispute-dialog` theo yêu cầu.

## Các tính năng đã implement (Implemented Features)

### 1. Các trường dữ liệu (Data Fields)

Tất cả các trường được yêu cầu đã được implement:

| Trường | Loại | <PERSON>uồ<PERSON> dữ liệu | Mô tả |
|--------|------|----------------|-------|
| Business Category | Dropdown | Từ detail screen | Danh mục kinh doanh |
| Dispute Amount | Input | Từ detail screen | Số tiền dispute |
| Dispute CRR | Input | Tạm thời | Trường tạm để nhập giá trị |
| Dispute Reason | Dropdown | Từ detail screen | Lý do dispute |
| Dispute Code | Dropdown | Từ detail screen | Mã dispute |
| Dispute Stage | Dropdown | Từ detail screen | Giai đoạn dispute |
| Outcome | Dropdown | Từ detail screen | Kết quả |
| Fraud Investigation | Dropdown | Từ detail screen | Điều tra gian lận |
| Internal Note | Input | Từ detail screen | Ghi chú nội bộ |
| Dispute File from Issuer | Dropdown | Từ detail screen | File dispute từ Issuer |
| Evidence | Input | Tạm thời | Trường tạm để nhập text |

### 2. Logic xử lý (Processing Logic)

#### a. Kiểm tra dữ liệu có sẵn (Existing Data Check)
```typescript
checkExistingData(updatedFields: any): string[]
```
- Kiểm tra xem các dispute được chọn có dữ liệu hiện tại cho các trường được cập nhật không
- Trả về danh sách các trường có dữ liệu hiện tại

#### b. Xác nhận ghi đè (Overwrite Confirmation)
Khi submit:
- Nếu TẤT CẢ dispute đều có data trống cho field đó → Cho phép cập nhật trực tiếp
- Nếu CÓ ÍT NHẤT 1 dispute đã có data cho field đó → Hiển thị popup xác nhận:
  - "Some disputes already have data for: [field names]. Do you want to overwrite the existing data?"
  - User có thể chọn "Cancel" hoặc "Overwrite"

#### c. Cập nhật linh hoạt (Flexible Update)
- User KHÔNG cần điền tất cả các trường
- Chỉ những trường được điền/chọn mới được cập nhật
- Các trường không được chỉnh sửa sẽ được giữ nguyên giá trị cũ

### 3. UI/UX Implementation

#### Layout
- Modal dialog với chiều cao tối đa 80vh, có thể scroll
- Chiều rộng: 600px
- Mỗi trường được bố trí trong row với:
  - Label bên trái (4 columns)
  - Input/Dropdown bên phải (8 columns)
- Margin top giữa các row để dễ đọc

#### Styling
- Sử dụng lại styles từ `update-dispute-dialog`
- PrimeNG dropdown với filter support
- Bootstrap form controls cho inputs
- Responsive design

#### Buttons
- "Cancel": Đóng dialog không lưu
- "Update": Submit và cập nhật disputes

### 4. Module Integration

Component đã được thêm vào module:
```typescript
// ss-dispute-management.module.ts
import { ShareDisputeInputDialogComponent } from './list/share-dispute-input-dialog/share-dispute-input-dialog.component';

@NgModule({
  declarations: [
    // ... existing components
    ShareDisputeInputDialogComponent,
    // ...
  ],
  // ...
})
```

## Cách sử dụng (Usage)

### Bước 1: Import component
```typescript
import { ShareDisputeInputDialogComponent } from '../share-dispute-input-dialog/share-dispute-input-dialog.component';
```

### Bước 2: Mở dialog
```typescript
this.dialogRef = this.dialogService.open(ShareDisputeInputDialogComponent, {
    header: 'Share Dispute Input - Selected Disputes: ' + this.selectedDisputes.length,
    contentStyle: { "max-height": "80vh", "width": "600px", "overflow": "auto"},
    baseZIndex: 10000,
    data: {
        selectedDisputes: this.selectedDisputes,
        business_category_list: this.listBusinessCategory,
        dispute_reason_list: this.listDisputeReason,
        dispute_code_list: this.listDisputeCode,
        dispute_stage_list: this.listDisputeStage,
        outcome_list: this.listOutcome,
        fraud_investigation_list: this.listFraudInves,
        dispute_file_from_issuer_list: this.listIssuersFromFile
    }
});
```

### Bước 3: Xử lý kết quả
```typescript
this.dialogRef.onClose.subscribe(result => {
    if (result) {
        // result chứa các trường đã được cập nhật
        this.updateDisputesBatch(result);
    }
});
```

Chi tiết đầy đủ xem tại: `USAGE_EXAMPLE.md`

## API Integration

Component sử dụng API có sẵn:
```typescript
updateByBatch(body: any): Observable<any>
```

### Request format:
```json
{
    "data": [
        {
            "id": 123,
            "businessCategory": "value",
            "disputeAmount": "1000",
            // ... other fields (chỉ những field được update)
        }
        // ... more disputes
    ]
}
```

## Testing Recommendations

1. **Test cập nhật đơn lẻ**: Chọn 1 field để cập nhật
2. **Test cập nhật nhiều field**: Chọn nhiều fields cùng lúc
3. **Test overwrite confirmation**: 
   - Chọn disputes đã có data
   - Cập nhật các field đã có data
   - Verify popup confirmation xuất hiện
4. **Test partial update**: 
   - Chỉ điền 1-2 fields
   - Verify chỉ những field đó được update
5. **Test cancel**: Verify không có thay đổi khi cancel
6. **Test với nhiều disputes**: Test với 5-10 disputes cùng lúc

## Dependencies

Component không cần cài đặt thêm thư viện nào. Sử dụng:
- PrimeNG (đã có sẵn)
- Angular Material (đã có sẵn)
- RxJS (đã có sẵn)
- Existing services: `SSDisputeManagementService`, `ConfirmService`

## Notes & Future Improvements

### Trường tạm thời (Temporary Fields)
- `disputeCrr`: Hiện tại là input text, có thể cần thêm vào database model sau
- `evidence`: Hiện tại là input text, có thể cần thêm vào database model sau

### Potential Enhancements
1. Add validation rules cho các trường input
2. Add field dependencies (e.g., dispute code phụ thuộc vào dispute stage)
3. Add more sophisticated conflict detection
4. Add preview before update
5. Add undo functionality
6. Add export/import functionality cho batch data

## Known Limitations

1. Temporary fields (`disputeCrr`, `evidence`) cần được thêm vào database schema và API model để persist
2. Dropdown data phải được load trước khi mở dialog
3. Large number of disputes (>100) có thể ảnh hưởng performance

## Support

Để hỗ trợ hoặc báo lỗi, vui lòng tham khảo:
- Implementation code: `/src/app/module/service-support/ss-dispute-management/list/share-dispute-input-dialog/`
- Usage examples: `USAGE_EXAMPLE.md`
- Module declaration: `ss-dispute-management.module.ts`

