# 🔍 ANALYSIS - Frontend vs Backend Compatibility

## 📋 Current Implementation

### Frontend (Angular)
```typescript
// Dialog returns updatedFields with null vs '' logic
{
    businessCategory: '',           // User selected Blank
    disputeCode: '10001',          // User selected value
    // disputeReason: NOT included  // User didn't touch (null)
}
```

### Backend (Vert.x Java)

#### API Endpoint
```java
public static void updateByBatch(RoutingContext ctx) {
    JsonArray disputeList = body.getJsonArray("data");
    List<SendDisputeParam> data = gson.fromJson(disputeList.encode(), listDisputes);
    sendResponse(ctx, 200, DisputeDao.updateDisputeByBatch(data));
}
```

#### DAO Method
```java
public static JsonObject updateDisputeByBatch(List<SendDisputeParam> listDisputes) {
    // Sets ALL fields for EACH dispute
    ps.setString(6, item.getBusinessCategory());      // Line 65
    ps.setString(8, item.getDisputeStage().trim());   // Line 67
    ps.setString(9, item.getDisputeReason());         // Line 68
    ps.setString(10, item.getDisputeCode().trim());   // Line 69
    // ... more fields
}
```

## ⚠️ CRITICAL ISSUES

### Issue 1: Backend ALWAYS Updates ALL Fields

**Problem:**
- Backend DAO `updateDisputeByBatch()` sets **ALL fields** for every dispute in the batch
- Frontend sends **FULL dispute objects** (lines 1729-1760 in risk-dispute-management-international-component.ts)
- This means: **Fields user didn't touch STILL get updated!**

**Example:**
```typescript
// Frontend builds this (lines 1729-1760):
let dispute = {
    "id": row.id,
    "businessCategory": row.businessCategory || '',  ← Always sent!
    "disputeReason": row.disputeReason,              ← Always sent!
    "disputeCode": row.disputeCode,                  ← Always sent!
    // ... ALL 18+ fields always sent
};
```

**Result:**
```
User ONLY selects Business Category = ''
    ↓
updatedFields = { businessCategory: '' }  ← Dialog returns this
    ↓
updateDisputesBatch() updates local data ✓
    ↓
Then builds FULL dispute object with ALL fields ✗
    ↓
Backend receives ALL fields and updates ALL fields ✗
    ↓
Fields user didn't touch get overwritten!
```

### Issue 2: Database Procedure Updates Fixed Fields Only

**Oracle Procedure (lines 225-237):**
```sql
UPDATE tb_dispute
SET
    s_dispute_code = p_dispute_code,           -- Fixed field 1
    s_outcome = p_outcome,                      -- Fixed field 2
    s_onepay_pic = p_onepay_pic,               -- Fixed field 3
    d_due_date = p_due_date,                   -- Fixed field 4
    n_disputed_amount = p_disputed_amount,      -- Fixed field 5
    s_dispute_currency = p_dispute_currency,    -- Fixed field 6
    s_updated_by = p_operator_id,
    d_update = sysdate
WHERE
        n_id = p_dispute_id
    AND n_parent_id IS NULL;
```

**Problem:**
- Procedure ONLY updates 6 fields: `disputeCode`, `outcome`, `onepayPic`, `dueDate`, `disputedAmount`, `disputeCurrency`
- But frontend is trying to update 11+ fields!
- Fields like `businessCategory`, `disputeReason`, `disputeStage`, `fraudInvestigation`, etc. are **NOT updated** by the procedure!

### Issue 3: Field Mapping Mismatch

**Frontend → Backend → Database:**
```
Frontend Field            | Backend Param          | DB Column          | Updated?
--------------------------|------------------------|--------------------|---------
businessCategory          | businessCategory       | s_business_category| ❌ NO
disputeAmount             | disputedAmount         | n_disputed_amount  | ✅ YES
disputeCrr                | ??? (not exists)       | ???                | ❌ NO
disputeReason             | disputeReason          | s_dispute_reason   | ❌ NO
disputeCode               | disputeCode            | s_dispute_code     | ✅ YES
disputeStage              | disputeStage           | s_dispute_stage    | ❌ NO
outcome                   | outcome                | s_outcome          | ✅ YES
fraudInvestigation        | fraudInves             | s_fraud_investigation| ❌ NO
internalNote              | note                   | s_note             | ❌ NO
disputeFileFromIssuer     | sftpAppleFileFromIssuers| ???               | ❌ NO
evidence                  | ??? (not exists)       | ???                | ❌ NO
```

**Only 3 fields are actually updated:**
1. ✅ `disputeCode`
2. ✅ `outcome`
3. ✅ `disputeAmount` (+ currency, dueDate, onepayPic)

**8+ fields are NOT updated despite frontend implementation!**

---

## 🛠️ REQUIRED FIXES

### Option A: Update Database Procedure (RECOMMENDED)

**Modify Oracle procedure to support dynamic field updates:**

```sql
PROCEDURE update_dispute_by_batch (
    p_dispute_id        IN NUMBER,
    -- ... all current params ...
    p_business_category IN VARCHAR2,
    p_dispute_reason    IN VARCHAR2,
    p_dispute_stage     IN VARCHAR2,
    p_fraud_inves       IN VARCHAR2,
    p_note              IN VARCHAR2,
    -- ... more fields
) AS
BEGIN
    UPDATE tb_dispute
    SET
        s_dispute_code = NVL(p_dispute_code, s_dispute_code),
        s_outcome = NVL(p_outcome, s_outcome),
        s_business_category = NVL(p_business_category, s_business_category),
        s_dispute_reason = NVL(p_dispute_reason, s_dispute_reason),
        s_dispute_stage = NVL(p_dispute_stage, s_dispute_stage),
        s_fraud_investigation = NVL(p_fraud_inves, s_fraud_investigation),
        s_note = NVL(p_note, s_note),
        -- ... all fields user can update
        s_updated_by = p_operator_id,
        d_update = sysdate
    WHERE n_id = p_dispute_id AND n_parent_id IS NULL;
END;
```

**Pros:**
- ✅ Supports all fields
- ✅ Backward compatible
- ✅ Clean architecture

**Cons:**
- ❌ Requires DB change
- ❌ Need DBA/DevOps approval
- ❌ May take time to deploy

### Option B: Build Dynamic Dispute Object (QUICK FIX)

**Modify `updateDisputesBatch()` to only send changed fields:**

```typescript
updateDisputesBatch(updatedFields: any) {
    console.log('Updating disputes with fields:', updatedFields);
    
    // Update local data (existing code)
    this.selectedDisputes.forEach(dispute => {
        // ... existing update logic ...
    });

    // NEW: Build API request with ONLY changed fields
    let updateList = [];
    this.selectedDisputes.forEach(row => {
        // Start with minimal required fields
        let dispute: any = {
            "id": row.id,
            "operatorId": this.global.activeProfile.n_id,
            "operatorName": row.operatorName,
        };

        // Add ONLY fields that were updated
        if (updatedFields.businessCategory !== undefined) {
            dispute.businessCategory = row.businessCategory || '';
        }
        if (updatedFields.disputeAmount !== undefined) {
            dispute.disputedAmount = row.disputeAmount;
            dispute.disputeCurrency = row.disputeCurrency;
        }
        if (updatedFields.disputeCode !== undefined) {
            dispute.disputeCode = row.disputeCode;
        }
        if (updatedFields.disputeStage !== undefined) {
            dispute.disputeStage = row.disputeStage || '';
        }
        if (updatedFields.disputeReason !== undefined) {
            dispute.disputeReason = row.disputeReason;
        }
        if (updatedFields.outcome !== undefined) {
            dispute.outcome = row.outcome;
        }
        if (updatedFields.fraudInvestigation !== undefined) {
            dispute.fraudInves = row.fraudInves;
        }
        if (updatedFields.internalNote !== undefined) {
            dispute.note = row.note;
        }
        if (updatedFields.disputeFileFromIssuer !== undefined) {
            dispute.sftpAppleFileFromIssuers = row.sftpAppleFileFromIssuers;
        }
        
        // Always include required fields for backend
        if (!dispute.disputeSender) dispute.disputeSender = row.disputeSender;
        if (!dispute.onepayPic) dispute.onepayPic = row.onepayPic;
        if (!dispute.sendDisputeTo) dispute.sendDisputeTo = row.sendDisputeTo || '';
        if (!dispute.merchantRespond) dispute.merchantRespond = row.merchantRespond;
        if (!dispute.merchantGroup) dispute.merchantGroup = row.merchantGroup;
        if (!dispute.refNumber) dispute.refNumber = row.refNumber;
        
        // Date handling
        if (!row.dueDate) {
            var disputeDate = new Date(row.disputeDate);
            row.dueDate = new Date(disputeDate.setDate(disputeDate.getDate() + 5));
        }
        dispute.dueDate = this.formatDate(row.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss');
        dispute.disputeDate = this.formatDate(row.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss');

        updateList.push(dispute);
    });

    // Call API
    let body = { 'data': updateList };
    this.sSDisputeManagementService.updateByBatch(body).subscribe(...);
}
```

**Pros:**
- ✅ No backend changes needed
- ✅ Quick implementation
- ✅ Only sends changed fields

**Cons:**
- ❌ Still limited by backend procedure (only 6 fields work)
- ❌ Doesn't fix field mapping issue
- ❌ Backend still receives/processes all fields

### Option C: Create New API Endpoint (BEST PRACTICE)

**Create new `updateDisputeFieldsByBatch` endpoint:**

```java
// Backend
public static void updateDisputeFieldsByBatch(RoutingContext ctx) {
    JsonObject body = ctx.getBodyAsJson();
    JsonArray disputeIds = body.getJsonArray("disputeIds");
    JsonObject updatedFields = body.getJsonObject("updatedFields");
    
    // Build dynamic SQL based on updatedFields
    StringBuilder sql = new StringBuilder("UPDATE tb_dispute SET ");
    List<String> updates = new ArrayList<>();
    
    if (updatedFields.containsKey("businessCategory")) {
        updates.add("s_business_category = ?");
    }
    if (updatedFields.containsKey("disputeCode")) {
        updates.add("s_dispute_code = ?");
    }
    // ... for each possible field
    
    sql.append(String.join(", ", updates));
    sql.append(", d_update = sysdate WHERE n_id IN (");
    sql.append(disputeIds.stream().map(id -> "?").collect(Collectors.joining(",")));
    sql.append(")");
    
    // Execute with prepared statement
    // ...
}

// Frontend
{
    "disputeIds": [1, 2, 3],
    "updatedFields": {
        "businessCategory": "",
        "disputeCode": "10001"
    },
    "operatorId": 123,
    "operatorName": "John Doe"
}
```

**Pros:**
- ✅ Clean separation of concerns
- ✅ Supports dynamic field updates
- ✅ Only updates what user changed
- ✅ Efficient (single query for all disputes)
- ✅ Future-proof

**Cons:**
- ❌ Requires backend changes
- ❌ More work
- ❌ Need testing

---

## 🎯 RECOMMENDATION

### For Production: **Option C** (New API Endpoint)

**Why:**
1. ✅ Proper architecture
2. ✅ Solves null vs '' logic correctly
3. ✅ Supports all 11 fields
4. ✅ Efficient and scalable
5. ✅ Future-proof

### For Quick Fix: **Option B** (Frontend Only)

**Why:**
1. ✅ No backend changes needed
2. ✅ Works with current 6 fields that procedure supports
3. ❌ Limitations: Only `disputeCode`, `outcome`, `disputedAmount`, `onepayPic`, `dueDate`, `disputeCurrency` will actually update

**Implementation needed:**
- Modify `updateDisputesBatch()` to build dynamic dispute object
- Only include fields from `updatedFields`

---

## 📝 SUMMARY OF ISSUES

### Critical Issues:
1. ❌ **Backend procedure only updates 6 fields, not 11**
2. ❌ **Frontend sends ALL fields regardless of user changes**
3. ❌ **Fields like `businessCategory`, `disputeReason`, `disputeStage` are NOT updated in DB**
4. ❌ **New fields `disputeCrr` and `evidence` don't exist in backend**

### What Works:
1. ✅ Frontend null vs '' logic (correct)
2. ✅ Dialog UI and field collection (correct)
3. ✅ Local data update in UI (correct)

### What Doesn't Work:
1. ❌ API call sends ALL fields (should only send changed)
2. ❌ Backend procedure doesn't update most fields
3. ❌ Field mapping incomplete

### Impact:
- **User Experience:** ⚠️ **8 out of 11 fields don't actually update in database!**
- **Data Integrity:** ⚠️ **Fields not updated by procedure remain stale**
- **Functionality:** ⚠️ **Only disputeCode, outcome, and disputedAmount work as expected**

---

## ✅ ACTION REQUIRED

**YOU NEED TO:**

1. **Decide which option:**
   - Option A: Update DB procedure ← Need backend/DBA
   - Option B: Frontend quick fix ← Limited to 6 fields
   - Option C: New API endpoint ← Best solution, needs backend

2. **For Option B (Quick Fix), I can implement now:**
   - Modify `updateDisputesBatch()` to only send changed fields
   - Document which fields actually work (6 fields)
   - Add console warnings for unsupported fields

3. **For Option C (Proper Solution), you need:**
   - Backend developer to create new endpoint
   - DBA to verify/create DB procedure
   - QA to test all fields

**Which option would you like?**

