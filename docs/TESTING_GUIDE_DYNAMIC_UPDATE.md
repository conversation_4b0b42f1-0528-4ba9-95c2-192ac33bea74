# 🧪 TESTING GUIDE - Dynamic Field Update

## 📋 Test Checklist

### ✅ Pre-Deployment Testing

#### 1. Frontend Only (Before Backend Ready)
- [ ] Dialog opens correctly
- [ ] All fields display properly
- [ ] Null vs '' logic works in dialog
- [ ] Console logs show correct values
- [ ] `updatedFields` object built correctly

#### 2. Backend API Testing (Postman/cURL)
- [ ] Endpoint `/dispute/updateFieldsByBatch` exists
- [ ] Request validation works
- [ ] Dynamic SQL generated correctly
- [ ] Database updates correctly
- [ ] Response format correct

#### 3. Database Testing
- [ ] New columns exist (`s_dispute_crr`, `s_evidence`)
- [ ] Columns accept data correctly
- [ ] Indexes created (if applicable)

#### 4. Integration Testing
- [ ] Frontend → Backend → Database flow works
- [ ] Only changed fields updated
- [ ] Unchanged fields remain same
- [ ] Audit fields updated (`d_update`, `s_updated_by`)

---

## 🧪 Test Cases

### Test Case 1: Update Single Field

**Setup:**
```sql
-- Create test disputes
INSERT INTO tb_dispute (n_id, s_business_category, s_dispute_code, s_outcome, n_parent_id)
VALUES (1, '5', '10001', NULL, NULL);

INSERT INTO tb_dispute (n_id, s_business_category, s_dispute_code, s_outcome, n_parent_id)
VALUES (2, '3', '10002', NULL, NULL);

INSERT INTO tb_dispute (n_id, s_business_category, s_dispute_code, s_outcome, n_parent_id)
VALUES (3, '', '10003', NULL, NULL);
```

**User Action:**
1. Select all 3 disputes
2. Open Share Dispute Input Dialog
3. Select Business Category = Blank (value = '')
4. Click Update

**Expected Frontend Console:**
```javascript
Submit - Current Values (null = not touched, "" = selected blank):
businessCategory: '' string
disputeCode: null object
disputeReason: null object
// ... all other fields null

Updated Fields: { businessCategory: '' }

API Request Body (only changed fields): {
  disputeIds: [1, 2, 3],
  updatedFields: { businessCategory: '' },
  metadata: { operatorId: 123, operatorName: 'John Doe' }
}
```

**Expected Backend SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_business_category = '',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE 
    n_id IN (1, 2, 3) 
    AND n_parent_id IS NULL;
```

**Expected Database Result:**
```sql
SELECT n_id, s_business_category, s_dispute_code, s_outcome
FROM tb_dispute
WHERE n_id IN (1, 2, 3);

-- Result:
-- n_id | s_business_category | s_dispute_code | s_outcome
-- 1    | ''                  | '10001'        | NULL       ← Changed
-- 2    | ''                  | '10002'        | NULL       ← Changed
-- 3    | ''                  | '10003'        | NULL       ← Already blank
```

**Verify:**
- ✅ Only `s_business_category` updated
- ✅ `s_dispute_code` unchanged
- ✅ `s_outcome` unchanged
- ✅ `d_update` updated
- ✅ `s_updated_by` = 123

---

### Test Case 2: Update Multiple Fields

**Setup:**
```sql
-- Reset test data
UPDATE tb_dispute 
SET s_business_category = '5', s_dispute_code = '10001', s_outcome = NULL
WHERE n_id = 1;

UPDATE tb_dispute 
SET s_business_category = '3', s_dispute_code = '10002', s_outcome = NULL
WHERE n_id = 2;
```

**User Action:**
1. Select disputes 1 and 2
2. Open dialog
3. Select Business Category = Blank
4. Select Dispute Code = '99999'
5. Select Outcome = 'won'
6. Click Update

**Expected Frontend Console:**
```javascript
Updated Fields: {
  businessCategory: '',
  disputeCode: '99999',
  outcome: 'won'
}

API Request Body: {
  disputeIds: [1, 2],
  updatedFields: {
    businessCategory: '',
    disputeCode: '99999',
    outcome: 'won'
  },
  metadata: { operatorId: 123, operatorName: 'John Doe' }
}
```

**Expected Backend SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_business_category = '',
    s_dispute_code = '99999',
    s_outcome = 'won',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE 
    n_id IN (1, 2) 
    AND n_parent_id IS NULL;
```

**Expected Database Result:**
```sql
-- n_id | s_business_category | s_dispute_code | s_outcome
-- 1    | ''                  | '99999'        | 'won'      ← All 3 changed
-- 2    | ''                  | '99999'        | 'won'      ← All 3 changed
```

**Verify:**
- ✅ 3 fields updated
- ✅ Other fields unchanged

---

### Test Case 3: No Fields Changed

**User Action:**
1. Select disputes
2. Open dialog
3. Don't select/change anything
4. Click Update

**Expected Frontend Console:**
```javascript
Updated Fields: {}
hasChanges: false

Dialog closes without API call
```

**Expected Backend:**
- ❌ No API call made

**Expected Database:**
- ❌ No changes

**Verify:**
- ✅ Dialog closes immediately
- ✅ No API call
- ✅ No database update

---

### Test Case 4: Clear Field Then Don't Submit

**User Action:**
1. Select dispute
2. Open dialog
3. Select Business Category = '7'
4. Click Clear button (X)
5. Click Update

**Expected Frontend Console:**
```javascript
businessCategory: null object  ← Back to null after clear

Updated Fields: {}
hasChanges: false
```

**Expected:**
- ✅ Dialog closes
- ✅ No API call
- ✅ Field not updated

---

### Test Case 5: Mix Clear and Update

**Setup:**
```sql
UPDATE tb_dispute 
SET s_business_category = '5', s_dispute_code = '10001', s_dispute_reason = 'Fraud'
WHERE n_id = 1;
```

**User Action:**
1. Select dispute 1
2. Open dialog
3. Select Business Category = Blank (clear to '')
4. Don't touch Dispute Code
5. Select Dispute Reason = '4855'
6. Click Update

**Expected Frontend Console:**
```javascript
Updated Fields: {
  businessCategory: '',
  disputeReason: '4855'
}
```

**Expected Backend SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_business_category = '',
    s_dispute_reason = '4855',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE n_id = 1 AND n_parent_id IS NULL;
```

**Expected Database Result:**
```sql
-- n_id | s_business_category | s_dispute_code | s_dispute_reason
-- 1    | ''                  | '10001'        | '4855'
--       ↑ Cleared            ↑ Unchanged     ↑ Updated
```

**Verify:**
- ✅ `s_business_category` cleared to ''
- ✅ `s_dispute_code` unchanged (10001)
- ✅ `s_dispute_reason` updated to 4855

---

### Test Case 6: New Fields (disputeCrr, evidence)

**User Action:**
1. Select dispute
2. Open dialog
3. Enter Dispute CRR = 'CRR-12345'
4. Enter Evidence = 'Customer provided receipt'
5. Click Update

**Expected Frontend Console:**
```javascript
Updated Fields: {
  disputeCrr: 'CRR-12345',
  evidence: 'Customer provided receipt'
}

API Request Body: {
  disputeIds: [1],
  updatedFields: {
    disputeCrr: 'CRR-12345',
    evidence: 'Customer provided receipt'
  },
  metadata: { ... }
}
```

**Expected Backend SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_dispute_crr = 'CRR-12345',
    s_evidence = 'Customer provided receipt',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE n_id = 1 AND n_parent_id IS NULL;
```

**Verify:**
- ✅ New columns accept data
- ✅ Data saved correctly

---

### Test Case 7: Confirmation Popup (Existing Data)

**Setup:**
```sql
UPDATE tb_dispute 
SET s_business_category = '5', s_dispute_code = '10001'
WHERE n_id = 1;

UPDATE tb_dispute 
SET s_business_category = '', s_dispute_code = '10002'
WHERE n_id = 2;
```

**User Action:**
1. Select disputes 1 and 2
2. Open dialog
3. Select Business Category = '7'
4. Click Update

**Expected:**
1. ✅ Confirmation popup appears:
   ```
   The following fields already have data in some disputes:
   - Business Category (1 dispute(s))
   
   Do you want to overwrite existing data?
   ```

2. User clicks "Yes"
3. ✅ API call proceeds
4. ✅ Both disputes updated

**Verify:**
- ✅ Popup shows for dispute 1 (has data '5')
- ✅ Popup doesn't count dispute 2 (already blank)
- ✅ After confirm, both updated to '7'

---

## 🔍 Backend API Testing (Postman)

### Request 1: Update Single Field

**Endpoint:** `POST /dispute/updateFieldsByBatch`

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token>
```

**Body:**
```json
{
  "disputeIds": [1, 2, 3],
  "updatedFields": {
    "businessCategory": ""
  },
  "metadata": {
    "operatorId": 123,
    "operatorName": "John Doe"
  }
}
```

**Expected Response:**
```json
{
  "status": 200,
  "rowsUpdated": 3,
  "message": "Updated 3 disputes successfully"
}
```

---

### Request 2: Update Multiple Fields

**Body:**
```json
{
  "disputeIds": [1, 2],
  "updatedFields": {
    "disputeCode": "99999",
    "outcome": "won",
    "note": "Test note"
  },
  "metadata": {
    "operatorId": 123,
    "operatorName": "John Doe"
  }
}
```

**Expected Response:**
```json
{
  "status": 200,
  "rowsUpdated": 2,
  "message": "Updated 2 disputes successfully"
}
```

---

### Request 3: Invalid Request (No Fields)

**Body:**
```json
{
  "disputeIds": [1],
  "updatedFields": {},
  "metadata": {}
}
```

**Expected Response:**
```json
{
  "status": 400,
  "error": "No valid fields to update"
}
```

---

### Request 4: Invalid Request (No IDs)

**Body:**
```json
{
  "disputeIds": [],
  "updatedFields": {
    "disputeCode": "10001"
  }
}
```

**Expected Response:**
```json
{
  "status": 400,
  "error": "Validation error"
}
```

---

## 📊 Database Verification Queries

### Query 1: Check Field Updates

```sql
-- Before update
SELECT n_id, s_business_category, s_dispute_code, s_outcome, d_update
FROM tb_dispute
WHERE n_id IN (1, 2, 3);

-- Run update via API

-- After update
SELECT n_id, s_business_category, s_dispute_code, s_outcome, d_update
FROM tb_dispute
WHERE n_id IN (1, 2, 3);

-- Verify:
-- 1. Only updated fields changed
-- 2. d_update is recent
-- 3. Other fields unchanged
```

---

### Query 2: Check Audit Trail

```sql
SELECT 
    n_id,
    s_updated_by,
    d_update,
    TO_CHAR(d_update, 'YYYY-MM-DD HH24:MI:SS') as update_time
FROM tb_dispute
WHERE n_id IN (1, 2, 3)
ORDER BY d_update DESC;

-- Verify:
-- 1. s_updated_by = operatorId from request
-- 2. d_update is recent (within last minute)
```

---

### Query 3: Check New Columns

```sql
SELECT n_id, s_dispute_crr, s_evidence
FROM tb_dispute
WHERE s_dispute_crr IS NOT NULL OR s_evidence IS NOT NULL;

-- Verify new fields accept data
```

---

## 🎯 Regression Testing

### Test Old API Still Works (Backward Compatibility)

**Endpoint:** `POST /dispute/updateByBatch`

**Body:**
```json
{
  "data": [
    {
      "id": 1,
      "disputeCode": "10001",
      "outcome": "won",
      // ... all other fields
    }
  ]
}
```

**Expected:**
- ✅ Old API still works
- ✅ No breaking changes

---

## 🚀 Performance Testing

### Test 1: Update 100 Disputes

```javascript
{
  "disputeIds": [1, 2, 3, ..., 100],  // 100 IDs
  "updatedFields": {
    "businessCategory": ""
  },
  "metadata": { ... }
}
```

**Measure:**
- Response time < 2 seconds
- Database query time < 500ms

---

### Test 2: Update 10 Fields

```javascript
{
  "disputeIds": [1, 2, 3],
  "updatedFields": {
    "businessCategory": "",
    "disputeCode": "10001",
    "disputeReason": "4855",
    "disputeStage": "chargeback",
    "outcome": "won",
    "fraudInves": "yes",
    "note": "Test",
    "disputeCrr": "CRR-123",
    "evidence": "Receipt",
    "disputedAmount": 100.50
  },
  "metadata": { ... }
}
```

**Measure:**
- Response time < 1 second
- SQL generated correctly with 10 SET clauses

---

## ✅ Sign-Off Checklist

### Frontend
- [ ] Dialog UI works correctly
- [ ] Null vs '' logic correct
- [ ] API request built correctly
- [ ] Field mapping correct
- [ ] Console logs helpful
- [ ] Error handling works
- [ ] Success message shows
- [ ] Local data updates
- [ ] No linter errors

### Backend
- [ ] Endpoint registered
- [ ] Request validation works
- [ ] Dynamic SQL generation correct
- [ ] Field mapping complete
- [ ] Error handling robust
- [ ] Logging adequate
- [ ] Response format correct
- [ ] Transaction handling (commit/rollback)

### Database
- [ ] New columns added
- [ ] Indexes created (if needed)
- [ ] Columns accept data
- [ ] Audit fields update
- [ ] No orphan data

### Integration
- [ ] Frontend → Backend → Database flow works
- [ ] Only changed fields updated
- [ ] Unchanged fields remain same
- [ ] Confirmation popup works
- [ ] Performance acceptable
- [ ] No breaking changes

---

## 🎉 Final Acceptance Test

**Scenario:** Real-world usage

1. ✅ User selects 5 disputes with mixed data
2. ✅ Opens Share Dispute Input Dialog
3. ✅ Changes 3 fields:
   - Business Category → Blank
   - Dispute Code → '99999'
   - Outcome → 'won'
4. ✅ Doesn't touch other 8 fields
5. ✅ Clicks Update
6. ✅ Confirmation popup shows (if needed)
7. ✅ User confirms
8. ✅ Success message appears
9. ✅ Verify in database:
   - 3 fields updated for all 5 disputes
   - 8 fields unchanged
   - Audit trail correct

**Result:** ✅ PASS → Ready for Production!

