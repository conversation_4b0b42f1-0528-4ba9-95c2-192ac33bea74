# 🎯 SOLUTION - Dynamic Field Update (Chỉ Update Fields User Đã Sửa)

## 📋 Architecture Overview

```
Frontend (Angular)                Backend (Vert.x Java)              Database (Oracle)
─────────────────                ───────────────────                ──────────────────
User selects:                    Receives:                          Updates:
- disputeCode: '10001'     →     {                            →     UPDATE tb_dispute SET
- businessCategory: ''            "disputeIds": [1, 2, 3],            s_dispute_code = '10001',
(NOT touched disputeReason)       "updatedFields": {                  s_business_category = ''
                                    "disputeCode": "10001",         WHERE id IN (1,2,3)
                                    "businessCategory": ""
                                  }                                (disputeReason NOT updated!)
                                }
```

---

## 🔧 IMPLEMENTATION

### 1️⃣ FRONTEND - Angular (Modify Existing Code)

#### File: `risk-dispute-management-international-component.ts`

**Replace method `updateDisputesBatch()` (lines 1670-1780):**

```typescript
updateDisputesBatch(updatedFields: any) {
    console.log('Updating disputes with fields:', updatedFields);
    
    // Update local data (keep existing logic)
    this.selectedDisputes.forEach(dispute => {
        if (updatedFields.businessCategory !== undefined) {
            dispute.businessCategory = updatedFields.businessCategory;
        }
        if (updatedFields.disputeAmount !== undefined) {
            dispute.disputeAmount = updatedFields.disputeAmount;
        }
        if (updatedFields.disputeCrr !== undefined) {
            dispute.disputeCrr = updatedFields.disputeCrr;
        }
        if (updatedFields.disputeReason !== undefined) {
            dispute.disputeReason = updatedFields.disputeReason;
        }
        if (updatedFields.disputeCode !== undefined) {
            dispute.disputeCode = updatedFields.disputeCode;
        }
        if (updatedFields.disputeStage !== undefined) {
            dispute.disputeStage = updatedFields.disputeStage;
        }
        if (updatedFields.outcome !== undefined) {
            dispute.outcome = updatedFields.outcome;
            if (dispute.outcome) {
                dispute.disputeStatus = 'resolved';
            }
        }
        if (updatedFields.fraudInvestigation !== undefined) {
            dispute.fraudInves = updatedFields.fraudInvestigation;
        }
        if (updatedFields.internalNote !== undefined) {
            dispute.note = updatedFields.internalNote;
        }
        if (updatedFields.disputeFileFromIssuer !== undefined) {
            dispute.sftpAppleFileFromIssuers = updatedFields.disputeFileFromIssuer;
        }
        if (updatedFields.evidence !== undefined) {
            dispute.evidence = updatedFields.evidence;
        }

        // Update data array for UI
        this.data.forEach(item => {
            if (item.id === dispute.id) {
                Object.assign(item, dispute);
            }
        });
    });

    // ✅ NEW: Build API request with ONLY changed fields + metadata
    const disputeIds = this.selectedDisputes.map(d => d.id);
    
    // Map frontend field names to backend field names
    const fieldMapping: any = {
        businessCategory: 'businessCategory',
        disputeAmount: 'disputedAmount',
        disputeCrr: 'disputeCrr',  // New field
        disputeReason: 'disputeReason',
        disputeCode: 'disputeCode',
        disputeStage: 'disputeStage',
        outcome: 'outcome',
        fraudInvestigation: 'fraudInves',
        internalNote: 'note',
        disputeFileFromIssuer: 'sftpAppleFileFromIssuers',
        evidence: 'evidence'  // New field
    };

    // Build updatedFieldsForBackend with mapped names
    const updatedFieldsForBackend: any = {};
    Object.keys(updatedFields).forEach(key => {
        const backendKey = fieldMapping[key];
        if (backendKey) {
            updatedFieldsForBackend[backendKey] = updatedFields[key];
        }
    });

    // Get first dispute for required metadata
    const firstDispute = this.selectedDisputes[0];
    
    // Build request body
    const body = {
        disputeIds: disputeIds,
        updatedFields: updatedFieldsForBackend,
        metadata: {
            operatorId: this.global.activeProfile.n_id,
            operatorName: firstDispute.operatorName || this.global.activeProfile.s_name,
            // Include disputeCurrency if disputedAmount is being updated
            ...(updatedFields.disputeAmount !== undefined && {
                disputeCurrency: firstDispute.disputeCurrency
            })
        }
    };

    console.log('API Request Body:', body);

    // Call API to update disputes
    this.sSDisputeManagementService.updateDisputeFieldsByBatch(body).subscribe(res => {
        if (res && res.status == 200) {
            this.selectedDisputes = [];
            this.showUpdateDispute = false;
            this.toastr.success("Update disputes successfully");
            // Optionally reload data to sync with DB
            // this.loadLazy(this.paginator.first);
        } else {
            this.toastr.error("Failed to update disputes");
        }
    }, error => {
        console.error('Error updating disputes:', error);
        this.toastr.error("Error updating disputes");
    });
}
```

---

### 2️⃣ FRONTEND - Service (Add New Method)

#### File: `ss-dispute-management.service.ts`

**Add new method:**

```typescript
// Add this method to SSDisputeManagementService class

/**
 * Update specific fields for multiple disputes
 * @param body { disputeIds: number[], updatedFields: object, metadata: object }
 */
updateDisputeFieldsByBatch(body: any): Observable<any> {
    return this.http.post(
        `${this.serverUrl}/dispute/updateFieldsByBatch`,
        body,
        { headers: this.getHeader() }
    ).pipe(
        map((res: any) => res),
        catchError(error => {
            console.error('Error in updateDisputeFieldsByBatch:', error);
            return throwError(error);
        })
    );
}
```

**Keep existing `updateByBatch()` for backward compatibility if needed.**

---

### 3️⃣ BACKEND - Vert.x Java (New Handler)

#### File: `DisputeHandler.java` (or wherever your handlers are)

**Add new handler:**

```java
/**
 * Update specific fields for multiple disputes (dynamic update)
 * Request body:
 * {
 *   "disputeIds": [1, 2, 3],
 *   "updatedFields": {
 *     "disputeCode": "10001",
 *     "businessCategory": "",
 *     "outcome": "won"
 *   },
 *   "metadata": {
 *     "operatorId": 123,
 *     "operatorName": "John Doe",
 *     "disputeCurrency": "USD"
 *   }
 * }
 */
public static void updateDisputeFieldsByBatch(RoutingContext ctx) {
    ctx.vertx().executeBlocking(future -> {
        try {
            JsonObject body = ctx.getBodyAsJson();
            
            // Validate request
            if (!body.containsKey("disputeIds") || !body.containsKey("updatedFields")) {
                throw IErrors.VALIDATION_ERROR;
            }

            JsonArray disputeIds = body.getJsonArray("disputeIds");
            JsonObject updatedFields = body.getJsonObject("updatedFields");
            JsonObject metadata = body.getJsonObject("metadata", new JsonObject());

            if (disputeIds.isEmpty() || updatedFields.isEmpty()) {
                throw IErrors.VALIDATION_ERROR;
            }

            // Call DAO method
            JsonObject result = DisputeDao.updateDisputeFieldsByBatch(
                disputeIds,
                updatedFields,
                metadata
            );
            
            sendResponse(ctx, 200, result);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "[ERROR] updateDisputeFieldsByBatch", ex);
            ctx.fail(ex);
        }
    }, false, null);
}
```

**Register route:**

```java
// In your router setup
router.post("/dispute/updateFieldsByBatch")
      .handler(DisputeHandler::updateDisputeFieldsByBatch);
```

---

### 4️⃣ BACKEND - DAO Method

#### File: `DisputeDao.java`

**Add new DAO method:**

```java
/**
 * Update specific fields for multiple disputes dynamically
 */
public static JsonObject updateDisputeFieldsByBatch(
    JsonArray disputeIds,
    JsonObject updatedFields,
    JsonObject metadata
) throws Exception {
    JsonObject result = new JsonObject();
    Exception exception = null;
    PreparedStatement ps = null;
    Connection conn = null;
    
    try {
        conn = getDisputeConnection118();
        conn.setAutoCommit(false);

        // Build dynamic SQL
        StringBuilder sql = new StringBuilder("UPDATE tb_dispute SET ");
        List<String> setClauses = new ArrayList<>();
        List<Object> parameters = new ArrayList<>();

        // Field mapping: JSON key → DB column
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("businessCategory", "s_business_category");
        fieldMapping.put("disputedAmount", "n_disputed_amount");
        fieldMapping.put("disputeCrr", "s_dispute_crr");  // New field (need to add column)
        fieldMapping.put("disputeReason", "s_dispute_reason");
        fieldMapping.put("disputeCode", "s_dispute_code");
        fieldMapping.put("disputeStage", "s_dispute_stage");
        fieldMapping.put("outcome", "s_outcome");
        fieldMapping.put("fraudInves", "s_fraud_investigation");
        fieldMapping.put("note", "s_note");
        fieldMapping.put("sftpAppleFileFromIssuers", "s_sftp_apple_file_from_issuers");
        fieldMapping.put("evidence", "s_evidence");  // New field (need to add column)
        fieldMapping.put("disputeCurrency", "s_dispute_currency");

        // Build SET clauses dynamically
        for (String key : updatedFields.fieldNames()) {
            String column = fieldMapping.get(key);
            if (column != null) {
                setClauses.add(column + " = ?");
                
                // Get value with proper type
                Object value = updatedFields.getValue(key);
                if (value instanceof String && ((String) value).isEmpty()) {
                    parameters.add("");  // Empty string for blank
                } else {
                    parameters.add(value);
                }
            }
        }

        if (setClauses.isEmpty()) {
            throw new Exception("No valid fields to update");
        }

        // Add audit fields
        setClauses.add("d_update = SYSDATE");
        if (metadata.containsKey("operatorId")) {
            setClauses.add("s_updated_by = ?");
            parameters.add(metadata.getInteger("operatorId"));
        }

        sql.append(String.join(", ", setClauses));

        // Add WHERE clause
        sql.append(" WHERE n_id IN (");
        for (int i = 0; i < disputeIds.size(); i++) {
            sql.append(i == 0 ? "?" : ", ?");
        }
        sql.append(") AND n_parent_id IS NULL");

        logger.log(Level.INFO, "Dynamic SQL: " + sql.toString());

        // Prepare statement
        ps = conn.prepareStatement(sql.toString());

        // Set parameters
        int paramIndex = 1;
        
        // Set field values
        for (Object param : parameters) {
            if (param instanceof String) {
                ps.setString(paramIndex++, (String) param);
            } else if (param instanceof Number) {
                ps.setDouble(paramIndex++, ((Number) param).doubleValue());
            } else if (param == null) {
                ps.setNull(paramIndex++, Types.VARCHAR);
            }
        }
        
        // Set dispute IDs
        for (Object id : disputeIds) {
            ps.setInt(paramIndex++, (Integer) id);
        }

        // Execute update
        int rowsUpdated = ps.executeUpdate();
        conn.commit();

        result.put("status", 200);
        result.put("rowsUpdated", rowsUpdated);
        result.put("message", "Updated " + rowsUpdated + " disputes successfully");

        logger.log(Level.INFO, "Updated " + rowsUpdated + " disputes");

    } catch (Exception ex) {
        logger.log(Level.SEVERE, "Error updating disputes", ex);
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException e) {
                logger.log(Level.SEVERE, "Error rolling back", e);
            }
        }
        exception = ex;
    } finally {
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
            } catch (SQLException e) {
                logger.log(Level.SEVERE, "Error resetting autoCommit", e);
            }
        }
        closeConnectionDB(null, ps, null, conn);
    }
    
    if (exception != null) {
        throw exception;
    }
    
    return result;
}
```

---

### 5️⃣ DATABASE - Add New Columns (If Needed)

#### SQL Script: `add_new_dispute_columns.sql`

**For new fields `disputeCrr` and `evidence`:**

```sql
-- Add disputeCrr column
ALTER TABLE tb_dispute 
ADD s_dispute_crr VARCHAR2(500);

COMMENT ON COLUMN tb_dispute.s_dispute_crr IS 'Dispute CRR (Customer Reference Record)';

-- Add evidence column
ALTER TABLE tb_dispute 
ADD s_evidence VARCHAR2(4000);

COMMENT ON COLUMN tb_dispute.s_evidence IS 'Evidence documents/notes';

-- Add index if needed
CREATE INDEX idx_dispute_crr ON tb_dispute(s_dispute_crr);
```

**Run this script in your database.**

---

## 📊 Request/Response Flow

### Frontend → Backend

**Request:**
```json
{
  "disputeIds": [1, 2, 3],
  "updatedFields": {
    "disputeCode": "10001",
    "businessCategory": "",
    "outcome": "won"
  },
  "metadata": {
    "operatorId": 123,
    "operatorName": "John Doe"
  }
}
```

**Generated SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_dispute_code = '10001',
    s_business_category = '',
    s_outcome = 'won',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE 
    n_id IN (1, 2, 3) 
    AND n_parent_id IS NULL;
```

**Response:**
```json
{
  "status": 200,
  "rowsUpdated": 3,
  "message": "Updated 3 disputes successfully"
}
```

---

## ✅ Benefits of This Approach

### 1. **True Partial Updates**
```
User only changes disputeCode
    ↓
Only disputeCode is sent to backend
    ↓
Only s_dispute_code is updated in DB
    ↓
Other fields remain unchanged ✅
```

### 2. **Supports Null vs Empty String**
```
Frontend: disputeCode = ''  (selected Blank)
    ↓
Backend: receives ""
    ↓
DB: UPDATE s_dispute_code = '' ✅
```

### 3. **Efficient & Scalable**
- Small payload (only changed fields)
- Single SQL query for all disputes
- No unnecessary data transfer

### 4. **Future-Proof**
- Easy to add new fields (just add to mapping)
- No need to modify SQL for each field
- Dynamic and flexible

### 5. **Maintains Audit Trail**
- `d_update` updated with SYSDATE
- `s_updated_by` tracks operator
- Can add more audit fields easily

---

## 🧪 Testing Examples

### Test 1: Update Single Field

**User Action:**
- Select 3 disputes
- Change Business Category to Blank
- Click Update

**Request:**
```json
{
  "disputeIds": [1, 2, 3],
  "updatedFields": {
    "businessCategory": ""
  },
  "metadata": { "operatorId": 123 }
}
```

**SQL:**
```sql
UPDATE tb_dispute 
SET s_business_category = '', d_update = SYSDATE, s_updated_by = 123
WHERE n_id IN (1, 2, 3) AND n_parent_id IS NULL;
```

**Result:** ✅ Only `s_business_category` updated

---

### Test 2: Update Multiple Fields

**User Action:**
- Select 2 disputes
- Change Dispute Code to '10001'
- Change Outcome to 'won'
- Change Internal Note to 'Test note'
- Click Update

**Request:**
```json
{
  "disputeIds": [5, 6],
  "updatedFields": {
    "disputeCode": "10001",
    "outcome": "won",
    "note": "Test note"
  },
  "metadata": { "operatorId": 123 }
}
```

**SQL:**
```sql
UPDATE tb_dispute 
SET 
    s_dispute_code = '10001',
    s_outcome = 'won',
    s_note = 'Test note',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE n_id IN (5, 6) AND n_parent_id IS NULL;
```

**Result:** ✅ 3 fields updated, others unchanged

---

### Test 3: Clear Field to Blank

**User Action:**
- Select 1 dispute with disputeReason = 'Fraud'
- Change Dispute Reason to Blank
- Click Update

**Before:**
```
Dispute #1: { disputeReason: 'Fraud', disputeCode: '10001' }
```

**Request:**
```json
{
  "disputeIds": [1],
  "updatedFields": {
    "disputeReason": ""
  },
  "metadata": { "operatorId": 123 }
}
```

**After:**
```
Dispute #1: { disputeReason: '', disputeCode: '10001' }
```

**Result:** ✅ disputeReason cleared, disputeCode unchanged

---

## 🎯 Migration Path

### Step 1: Deploy Backend (No Breaking Changes)
1. Add new handler `updateDisputeFieldsByBatch`
2. Add new DAO method
3. Keep old `updateByBatch` for backward compatibility
4. Deploy backend

### Step 2: Deploy Database (Add Columns)
1. Run SQL script to add `s_dispute_crr` and `s_evidence` columns
2. Test columns exist

### Step 3: Deploy Frontend
1. Add new service method `updateDisputeFieldsByBatch`
2. Modify `updateDisputesBatch()` to use new API
3. Deploy frontend

### Step 4: Test & Verify
1. Test single field update
2. Test multiple fields update
3. Test clear to blank
4. Test null vs '' logic
5. Verify audit trail

### Step 5: Cleanup (Optional)
1. Monitor old `updateByBatch` usage
2. If not used, can deprecate/remove

---

## 📝 Code Changes Summary

### Frontend Changes:
1. ✅ `risk-dispute-management-international-component.ts` → Modify `updateDisputesBatch()` (1 method)
2. ✅ `ss-dispute-management.service.ts` → Add `updateDisputeFieldsByBatch()` (1 method)

### Backend Changes:
1. ✅ `DisputeHandler.java` → Add `updateDisputeFieldsByBatch()` handler (1 method)
2. ✅ `DisputeDao.java` → Add `updateDisputeFieldsByBatch()` DAO method (1 method)
3. ✅ Router → Add new route (1 line)

### Database Changes:
1. ✅ Add 2 columns: `s_dispute_crr`, `s_evidence` (1 SQL script)

**Total: ~200 lines of new code, no breaking changes!**

---

## 🎉 Final Result

### User Experience:
```
User selects 3 disputes
    ↓
Opens Share Dispute Input Dialog
    ↓
Changes ONLY Business Category to Blank
    ↓
Clicks Update
    ↓
Frontend sends:
{
  "disputeIds": [1, 2, 3],
  "updatedFields": { "businessCategory": "" }
}
    ↓
Backend generates:
UPDATE tb_dispute 
SET s_business_category = ''
WHERE n_id IN (1, 2, 3)
    ↓
Database updates ONLY s_business_category
    ↓
All other fields remain UNCHANGED ✅
```

**Perfect! Exactly what you wanted! 🎯**

