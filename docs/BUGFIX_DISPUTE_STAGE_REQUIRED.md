# 🔧 BUGFIX - Add Dispute Stage as Required Field

## 📋 Issue

**Vấn đề:** 
- User ph<PERSON>i chọn **Dispute Stage** trước mới có thể chọn **Dispute Code**
- Nhưng **Dispute Stage** không được set required
- **Dispute Code** là required
- → Inconsistent: Dependent field (Code) required nhưng parent field (Stage) không required!

**User Experience:**
```
User mở form
    ↓
Error hiện: "Dispute Code is required" 🔴
    ↓
User muốn chọn Dispute Code
    ↓
Dropdown trống hoặc không có options ❌
    ↓
User bối rối: Tại sao không có options?
    ↓
Phải chọn Dispute Stage trước
    ↓
Mới có Dispute Code options
```

**Logic Dependencies:**

```
Dispute Stage (Parent)
    ↓ (filters)
Dispute Code (Child)
```

**Dispute Code phụ thuộc vào Dispute Stage:**
- Stage chưa chọn → Code list = empty
- Stage đã chọn → Code list = filtered by stage

---

## ✅ Solution

### Change: Set Both as Required

**File:** `dispute-detail-component.ts`

**Line 450-455 - BEFORE:**
```typescript
if (this.isINT) {
    if (!this.listDisputeReason) {
        this.listDisputeReason = this.InterReason;
    }
    this.disputeDetailForm.controls.disputeCode.setValidators(Validators.required);
    // ↑ Only disputeCode required
}
```

**Line 450-456 - AFTER:**
```typescript
if (this.isINT) {
    if (!this.listDisputeReason) {
        this.listDisputeReason = this.InterReason;
    }
    // Set required for International disputes
    this.disputeDetailForm.controls.disputeStage.setValidators(Validators.required);
    this.disputeDetailForm.controls.disputeCode.setValidators(Validators.required);
    // ↑ Both disputeStage AND disputeCode required
}
```

---

## 📊 Logic Flow

### Before Fix

```
Risk International Dispute loads
    ↓
isINT = true
    ↓
disputeCode.setValidators(Validators.required) ✅
    ↓
triggerInitialValidation()
    ↓
Errors show:
- "Dispute Code is required" 🔴
    ↓
User confused:
- Stage không có error ❌
- Code có error ✅
- Nhưng phải chọn Stage trước!
```

---

### After Fix

```
Risk International Dispute loads
    ↓
isINT = true
    ↓
disputeStage.setValidators(Validators.required) ✅
disputeCode.setValidators(Validators.required) ✅
    ↓
triggerInitialValidation()
    ↓
Errors show:
- "Dispute Stage is required" 🔴
- "Dispute Code is required" 🔴
    ↓
User understands:
- Phải chọn Stage trước ✅
- Sau đó chọn Code ✅
- Logic rõ ràng!
```

---

## 🎯 Benefits

### 1. Consistent Validation Logic

```
Before:
Parent (Stage): Not required ❌
Child (Code): Required ✅
→ Inconsistent!

After:
Parent (Stage): Required ✅
Child (Code): Required ✅
→ Consistent!
```

---

### 2. Better UX

```
User sees both errors immediately:
- "Dispute Stage is required"
- "Dispute Code is required"

User knows: Chọn Stage trước, Code sau ✅
```

---

### 3. Logical Dependencies

```
Stage (Required) → Filters → Code (Required)
    ↓                          ↓
Must select first        Can select after stage
```

**Makes sense!** Parent field required → Child field required

---

## 🧪 Testing

### Test Case 1: Load International Dispute

**Before Fix:**
```
Load dispute
    ↓
Errors show:
- Dispute Code is required 🔴
- Dispute Stage: No error ❌
```

**After Fix:**
```
Load dispute
    ↓
Errors show:
- Dispute Stage is required 🔴
- Dispute Code is required 🔴
```

---

### Test Case 2: Select Stage

**Behavior (Same before & after):**
```
User selects Stage = "First Chargeback"
    ↓
Dispute Stage error clears ✅
    ↓
Dispute Code dropdown shows filtered options
    ↓
User can now select Dispute Code
    ↓
Dispute Code error clears ✅
```

---

### Test Case 3: Change Stage

**Behavior (Same before & after):**
```
User has:
- Stage = "First Chargeback"
- Code = "4570"

User changes Stage = "Arbitration"
    ↓
OnSelected() triggered
    ↓
Dispute Code cleared to null
    ↓
Dispute Code error shows again 🔴
    ↓
User must select new code from new filtered list
```

---

## 📝 Related Logic

### Why Both Need to Be Required?

**Dependency Chain:**

```
1. Dispute Stage selected
       ↓
2. filterListDisputeCodeRisk() called
       ↓
3. Filter codes by cardType + stage
       ↓
4. Dropdown shows filtered codes
       ↓
5. User can select Dispute Code
```

**If Stage not selected:**
```
1. Dispute Stage = null
       ↓
2. Filter codes by cardType + null
       ↓
3. No codes match (empty list)
       ↓
4. Dropdown shows nothing ❌
       ↓
5. User cannot select Code ❌
```

**Conclusion:** Stage MUST be selected first → Stage should be required!

---

## 🎯 Other Fields With Similar Logic

### Current Required Fields (Risk + International)

**Always Required (from form init):**
- ✅ OnePay PIC (line 188)
- ✅ Dispute Amount (line 195)
- ✅ Dispute Currency (line 196)
- ✅ Due Date (line 197)
- ✅ Dispute Date (line 202)

**Dynamically Required (only for International):**
- ✅ Dispute Stage (line 455) ← **ADDED**
- ✅ Dispute Code (line 456)

**Not Required:**
- ❌ Business Category
- ❌ Dispute Reason
- ❌ Outcome
- ❌ Fraud Investigation
- ❌ Internal Note
- ❌ Dispute File from Issuer

---

## 💡 Why Labels Don't Show Asterisk?

**Line 631-633:**
```typescript
getDropdownLabel(fieldName: string, label: string): string {
    return (this.isRISK && this.isINT) ? label : `${label} *`;
    //     ↑ Risk + International → No asterisk
}
```

**Design Decision:**
- Risk + International: "Flexible" form appearance
- Validation still enforced
- Errors show when needed
- But UI doesn't look "intimidating" with many asterisks

**This creates the confusion you noticed!**

---

## 🎉 Summary

### What Changed:
- ✅ Added `disputeStage.setValidators(Validators.required)` for International disputes
- ✅ Now both Stage and Code are required
- ✅ Consistent with dependency logic

### Result:
- ✅ User sees both errors on load
- ✅ User knows: Select Stage → Then Code
- ✅ Logic clear and consistent

### Files Modified:
- `dispute-detail-component.ts` (Line 455)

### Lines Changed: 1 line added

---

**Bây giờ Dispute Stage cũng required cho International disputes! ✅**

**Flow:**
```
Load → Stage error 🔴 + Code error 🔴
    ↓
Select Stage → Stage error cleared ✅
    ↓
Select Code → Code error cleared ✅
    ↓
Both fields valid → Can save ✅
```

**Perfect logic! 🎯**

