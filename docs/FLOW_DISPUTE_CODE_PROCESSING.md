# 🔄 FLOW: Dispute Code Processing

## 📋 Overview

Document này mô tả chi tiết luồng xử lý Dispute Code từ API → Display trong ứng dụng.

**Example Code Used:** `4570: Invalid Representment` (UnionPay)

---

## 🎯 Flow Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    STEP 1: API CALL                         │
│  Component: dispute-detail-component.ts (ngOnInit)          │
└──────────────────────────┬──────────────────────────────────┘
                           ↓
┌─────────────────────────────────────────────────────────────┐
│                API: getDisputeCode()                        │
│  Endpoint: /dispute/dispute-code                            │
│  Method: GET                                                │
└──────────────────────────┬──────────────────────────────────┘
                           ↓
┌─────────────────────────────────────────────────────────────┐
│               STEP 2: API RESPONSE                          │
│  {                                                          │
│    "list": [                                                │
│      {                                                      │
│        "N_ID": 4570,                                        │
│        "NAME": "4570: Invalid Representment",               │
│        "GROUP": "UnionPay - CHARGEBACK, PRE-ARBITRATION,   │
│                 ARBITRATION, FRAUD REPORT"                  │
│      },                                                     │
│      ... more codes ...                                     │
│    ]                                                        │
│  }                                                          │
└──────────────────────────┬──────────────────────────────────┘
                           ↓
┌─────────────────────────────────────────────────────────────┐
│         STEP 3: FORMAT & PARSE                              │
│  Function: formatListDisputeCodeRisk()                      │
│  Location: dispute-utils.ts                                 │
│                                                             │
│  Process:                                                   │
│  1. Loop through each code from API                         │
│  2. Loop through card types (VISA, MC, JCB, UNIONPAY...)   │
│  3. Check if GROUP contains card type                       │
│  4. Loop through stages (1-7)                               │
│  5. Check if GROUP contains stage keywords                  │
│  6. Create item for each valid combination                  │
└──────────────────────────┬──────────────────────────────────┘
                           ↓
┌─────────────────────────────────────────────────────────────┐
│              STEP 4: PARSED RESULT                          │
│  listDisputeCodeRiskTotal = [                               │
│    // Code 4570 creates 6 items:                            │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"2"},  // Pre-Arbitration                         │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"3"},  // First Chargeback                        │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"4"},  // Arbitration                             │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"5"},  // Second Chargeback                       │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"6"},  // Pre-Compliance                          │
│    {value:"4570",label:"4570: Invalid...",card:"UNIONPAY",  │
│     stage:"7"},  // Fraud Report                            │
│    ... + 500+ other codes ...                               │
│  ]                                                          │
└──────────────────────────┬──────────────────────────────────┘
                           ↓
              ┌────────────┴────────────┐
              │   Check Dispute Type     │
              └────────────┬────────────┘
                ┌──────────┴──────────┐
                ↓                     ↓
┌─────────────────────────┐  ┌─────────────────────────────────┐
│   SS DISPUTE (isSS)     │  │   RISK DISPUTE (isRISK)         │
│                         │  │                                 │
│  Use Hardcoded List     │  │  STEP 5: FILTER BY CARD & STAGE │
│  getListCodeSS()        │  │  filterListDisputeCodeRisk()    │
│                         │  │                                 │
│  Filter by:             │  │  Filter by:                     │
│  - transactionType      │  │  - disputeDetail.cardType       │
│  - transactionStatus    │  │  - disputeDetail.disputeStage   │
│                         │  │                                 │
│  Result: 3-5 codes      │  │  Example:                       │
│  (10001, 10002, etc.)   │  │  cardType = "UNIONPAY"          │
│                         │  │  disputeStage = "3"             │
│                         │  │                                 │
│                         │  │  Result: 20-50 codes            │
│                         │  │  (only UNIONPAY + stage 3)      │
└──────────┬──────────────┘  └────────────┬────────────────────┘
           │                              │
           └──────────┬───────────────────┘
                      ↓
┌─────────────────────────────────────────────────────────────┐
│            STEP 6: DISPLAY IN DROPDOWN                      │
│  Component: dispute-detail-component.html                   │
│  <p-dropdown [options]="listDisputeCodeRiskFiltered">       │
│                                                             │
│  User sees:                                                 │
│  ┌────────────────────────────────────────┐                │
│  │ Select Dispute Code                  ▼ │                │
│  ├────────────────────────────────────────┤                │
│  │ Blank                                  │                │
│  │ 4570: Invalid Representment            │ ← Shows here   │
│  │ 4571: Processing Error                 │                │
│  │ ...                                    │                │
│  └────────────────────────────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

---

## 📊 Detailed Example: Code 4570

### Input: API Response

```json
{
  "list": [
    {
      "N_ID": 4570,
      "NAME": "4570: Invalid Representment",
      "GROUP": "UnionPay - CHARGEBACK, PRE-ARBITRATION, ARBITRATION, FRAUD REPORT"
    }
  ]
}
```

### Processing: formatListDisputeCodeRisk()

**File:** `dispute-utils.ts` (Lines 134-161)

```typescript
formatListDisputeCodeRisk(apiResponse) {
    let list = [];
    let listCard = ["VISA", "MASTERCARD", "JCB", "UNIONPAY", "AMEX"];
    let listStage = ['1', '2', '3', '4', '5', '6', '7'];  // From constants
    
    apiResponse.list.forEach(code => {
        // STEP 1: Loop through card types
        listCard.forEach(card => {
            // STEP 2: Check if GROUP contains this card
            if (this.checkDisputeCodeGroupWithStage(code.GROUP, card)) {
                // STEP 3: Loop through stages
                listStage.forEach(stage => {
                    // STEP 4: Check if GROUP contains this stage
                    if (this.checkDisputeCodeGroupWithStage(code.GROUP, stage)) {
                        // STEP 5: Create item
                        let newItem = {
                            value: code.N_ID.toString(),
                            label: code.NAME,
                            card: card,
                            stage: stage
                        };
                        list.push(newItem);
                    }
                });
            }
        });
    });
    
    return list;
}
```

### Execution Trace

```
INPUT: Code 4570
GROUP: "UnionPay - CHARGEBACK, PRE-ARBITRATION, ARBITRATION, FRAUD REPORT"

════════════════════════════════════════════════════════════════

CARD LOOP:
──────────────────────────────────────────────────────────────

Card: "VISA"
  ├─ Check: GROUP.match(/VISA/i) → null ❌
  └─ SKIP VISA

Card: "MASTERCARD"
  ├─ Check: GROUP.match(/MASTERCARD/i) → null ❌
  └─ SKIP MASTERCARD

Card: "JCB"
  ├─ Check: GROUP.match(/JCB/i) → null ❌
  └─ SKIP JCB

Card: "UNIONPAY" ✓
  ├─ Check: GROUP.match(/UNIONPAY/i) → Match! ✅
  └─ PROCESS STAGES:

  STAGE LOOP:
  ──────────────────────────────────────────────────────────

  Stage: "1" (Retrieval Request)
    ├─ Pattern: /RETRIEVAL REQUEST/
    ├─ Check: GROUP.match(/RETRIEVAL REQUEST/)
    ├─ Result: null ❌
    └─ SKIP Stage 1

  Stage: "2" (Pre-Arbitration) ✓
    ├─ Pattern: /PRE-ARBITRATION/
    ├─ Check: GROUP.match(/PRE-ARBITRATION/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "2"
        }
        ✓ Added to list

  Stage: "3" (First Chargeback) ✓
    ├─ Pattern: /CHARGEBACK/
    ├─ Check: GROUP.match(/CHARGEBACK/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "3"
        }
        ✓ Added to list

  Stage: "4" (Arbitration) ✓
    ├─ Pattern: / ARBITRATION/  (with leading space)
    ├─ Check: GROUP.match(/ ARBITRATION/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "4"
        }
        ✓ Added to list

  Stage: "5" (Second Chargeback) ✓
    ├─ Pattern: /CHARGEBACK/
    ├─ Check: GROUP.match(/CHARGEBACK/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "5"
        }
        ✓ Added to list

  Stage: "6" (Pre-Compliance) ✓
    ├─ Pattern: /CHARGEBACK/
    ├─ Check: GROUP.match(/CHARGEBACK/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "6"
        }
        ✓ Added to list

  Stage: "7" (Fraud Report) ✓
    ├─ Pattern: /FRAUD REPORT/
    ├─ Check: GROUP.match(/FRAUD REPORT/)
    ├─ Result: Match! ✅
    └─ CREATE ITEM:
        {
          value: "4570",
          label: "4570: Invalid Representment",
          card: "UNIONPAY",
          stage: "7"
        }
        ✓ Added to list

Card: "AMEX"
  ├─ Check: GROUP.match(/AMEX/i) → null ❌
  └─ SKIP AMEX

════════════════════════════════════════════════════════════════

RESULT: 6 items created from 1 API record
```

### Output: 6 Items Created

```javascript
[
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "2"  // Pre-Arbitration
  },
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "3"  // First Chargeback
  },
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "4"  // Arbitration
  },
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "5"  // Second Chargeback
  },
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "6"  // Pre-Compliance
  },
  {
    value: "4570",
    label: "4570: Invalid Representment",
    card: "UNIONPAY",
    stage: "7"  // Fraud Report
  }
]
```

---

## 🎨 Pattern Matching Logic

### Card Type Matching

**Function:** `checkDisputeCodeGroupWithStage()` (Line 179-182)

```typescript
checkDisputeCodeGroupWithCard(disputeCodeGroup: string, card: string) {
    let pattern = new RegExp(card, 'i');  // Case-insensitive
    return disputeCodeGroup.match(pattern);
}
```

**Examples:**

| Card Type | Pattern | GROUP | Match? |
|-----------|---------|-------|--------|
| VISA | `/VISA/i` | "UnionPay - CHARGEBACK..." | ❌ null |
| MASTERCARD | `/MASTERCARD/i` | "UnionPay - CHARGEBACK..." | ❌ null |
| UNIONPAY | `/UNIONPAY/i` | "UnionPay - CHARGEBACK..." | ✅ Match |
| unionpay | `/unionpay/i` | "UnionPay - CHARGEBACK..." | ✅ Match (case-insensitive) |

---

### Stage Keyword Matching

**Function:** `checkDisputeCodeGroupWithStage()` (Line 163-177)

```typescript
private checkDisputeCodeGroupWithStage(disputeCodeGroup: string, stage: string) {
    let pattern = /./;  // Default: match anything
    
    if (stage == '1') {
        pattern = /RETRIEVAL REQUEST/;
    } else if (stage == '2') {
        pattern = /PRE-ARBITRATION/;
    } else if (stage == '3' || stage == '5' || stage == '6') {
        pattern = /CHARGEBACK/;  // 3 stages use same keyword!
    } else if (stage == '4') {
        pattern = / ARBITRATION/;  // Note: space before
    } else if (stage == '7') {
        pattern = /FRAUD REPORT/;
    }
    
    return disputeCodeGroup.match(pattern);
}
```

**Stage Mapping:**

| Stage ID | Stage Name | Pattern | Example GROUP |
|----------|------------|---------|---------------|
| 1 | Retrieval Request | `/RETRIEVAL REQUEST/` | "VISA - RETRIEVAL REQUEST, CHARGEBACK" |
| 2 | Pre-Arbitration | `/PRE-ARBITRATION/` | "MC - PRE-ARBITRATION" |
| 3 | First Chargeback | `/CHARGEBACK/` | "VISA - CHARGEBACK" |
| 4 | Arbitration | `/ ARBITRATION/` | "JCB - ARBITRATION" |
| 5 | Second Chargeback | `/CHARGEBACK/` | "VISA - CHARGEBACK" |
| 6 | Pre-Compliance | `/CHARGEBACK/` | "VISA - CHARGEBACK" |
| 7 | Fraud Report | `/FRAUD REPORT/` | "UNIONPAY - FRAUD REPORT" |

**⚠️ Important Notes:**

1. **Stages 3, 5, 6 share same pattern** `/CHARGEBACK/`
   - This means a code with "CHARGEBACK" in GROUP will appear in all 3 stages!

2. **Stage 4 has space before ARBITRATION** `/ ARBITRATION/`
   - Prevents matching "PRE-ARBITRATION"
   - Only matches standalone "ARBITRATION"

3. **Case-sensitive matching**
   - GROUP must have exact keyword (UPPERCASE)
   - "Chargeback" ≠ "CHARGEBACK"

---

## 🔍 Filtering in Dispute Detail

### When User Opens Dispute Detail

**File:** `dispute-detail-component.ts` (Lines 291-299)

```typescript
filterListDisputeCodeRisk() {
    // Filter by BOTH card type AND dispute stage
    this.listDisputeCodeRiskFiltered = this.listDisputeCodeRiskTotal
        .filter(e => e.card == this.disputeDetail.cardType)
        .filter(e => e.stage == this.disputeDetail.disputeStage);
}
```

### Example Scenarios

#### Scenario 1: UnionPay + First Chargeback

```javascript
// Dispute data
this.disputeDetail.cardType = "UNIONPAY";
this.disputeDetail.disputeStage = "3";

// Filter
listDisputeCodeRiskTotal (600+ items)
    ↓
    .filter(e => e.card == "UNIONPAY")  // Keep ~80 items
    ↓
    .filter(e => e.stage == "3")        // Keep ~25 items
    ↓
listDisputeCodeRiskFiltered = [
    { value: "4570", label: "4570: Invalid Representment", ... },
    { value: "4540", label: "4540: Cancelled Merchandise", ... },
    // ... ~25 codes for UNIONPAY + First Chargeback
]
```

**Dropdown shows:** 25 relevant codes ✓

---

#### Scenario 2: VISA + Pre-Arbitration

```javascript
// Dispute data
this.disputeDetail.cardType = "VISA";
this.disputeDetail.disputeStage = "2";

// Filter
listDisputeCodeRiskTotal (600+ items)
    ↓
    .filter(e => e.card == "VISA")      // Keep ~150 items
    ↓
    .filter(e => e.stage == "2")        // Keep ~30 items
    ↓
listDisputeCodeRiskFiltered = [
    { value: "4540", label: "4540: ...", card: "VISA", stage: "2" },
    // ... ~30 codes for VISA + Pre-Arbitration
]

// Code 4570 NOT included because it's UNIONPAY only ❌
```

**Dropdown shows:** 30 relevant codes ✓

---

#### Scenario 3: User Changes Stage

**File:** `dispute-detail-component.ts` (Lines 727-732)

```typescript
OnSelected(fieldName: string, value: any) {
    if (fieldName === 'disputeStage') {
        // Clear dispute code when stage changes
        this.disputeDetailForm.get('disputeCode')?.setValue(null);
        this.disputeDetail.disputeCode = null;
        
        // Re-filter codes for new stage
        this.filterListDisputeCodeRisk();
    }
}
```

**Flow:**

```
User selects disputeStage = "3" (First Chargeback)
    ↓
    filterListDisputeCodeRisk() called
    ↓
    Dropdown shows codes for stage 3 (e.g., 25 codes)
    ↓
User changes disputeStage = "4" (Arbitration)
    ↓
    OnSelected() triggered
    ↓
    Clear disputeCode (reset to null)
    ↓
    filterListDisputeCodeRisk() called again
    ↓
    Dropdown shows codes for stage 4 (e.g., 18 codes)
```

**Why clear disputeCode?**
- Old code may not be valid for new stage
- Forces user to select code again from new list
- Prevents invalid combinations

---

## 📈 Data Volume Analysis

### API Response (Estimate)

```
Total Dispute Codes: ~150 unique codes
Card Types: 5 (VISA, MC, JCB, UNIONPAY, AMEX)
Stages: 7 (1-7)

Maximum possible items: 150 × 5 × 7 = 5,250 items
Actual items: ~600-800 items (after filtering invalid combinations)
```

### After Filtering

```
Single Dispute View:
- Card Type: 1 (e.g., UNIONPAY)
- Stage: 1 (e.g., First Chargeback)

Filtered Result: ~20-50 relevant codes ✓
Reduction: From 600+ to 20-50 (95%+ reduction!)
```

**Benefits:**
- ✅ User only sees relevant codes
- ✅ Faster dropdown loading
- ✅ Better UX (less clutter)
- ✅ Prevents invalid selections

---

## 🎯 Special Cases

### Case 1: Code Valid for Multiple Cards

```json
{
  "N_ID": 1000,
  "NAME": "1000: Generic Fraud",
  "GROUP": "VISA, MASTERCARD, JCB - CHARGEBACK, FRAUD REPORT"
}
```

**Result:** Creates items for:
- VISA + CHARGEBACK (stages 3, 5, 6)
- VISA + FRAUD REPORT (stage 7)
- MASTERCARD + CHARGEBACK (stages 3, 5, 6)
- MASTERCARD + FRAUD REPORT (stage 7)
- JCB + CHARGEBACK (stages 3, 5, 6)
- JCB + FRAUD REPORT (stage 7)

**Total:** 18 items from 1 API record!

---

### Case 2: Code Valid for All Stages

```json
{
  "N_ID": 9999,
  "NAME": "9999: All Stages Code",
  "GROUP": "VISA - RETRIEVAL REQUEST, CHARGEBACK, PRE-ARBITRATION, ARBITRATION, FRAUD REPORT"
}
```

**Result:** Creates items for VISA + all 7 stages

---

### Case 3: SS Dispute (Different Logic)

**File:** `dispute-detail-component.ts` (Lines 273-289)

```typescript
getListCodeSS() {
    // Use hardcoded list, not API
    this.listDisputeCodeSS = listDisputeCodeSS;
    
    // Filter by transaction type + status
    if (transactionType == "Purchase" && transactionStatus == "Successful") {
        this.listDisputeCodeSS = this.listDisputeCodeSS
            .filter(item => ['', '10001', '10002'].includes(item.value));
    }
    // ...
}
```

**Key Differences:**
- ❌ No API call for SS disputes
- ❌ No card type filtering
- ❌ No stage filtering
- ✅ Use hardcoded list from constants
- ✅ Filter by transaction type/status only

---

## 📝 Summary

### Key Points

1. **One-to-Many Relationship**
   - 1 API record → Multiple dropdown items
   - Average: 1 code → 4-6 items

2. **Smart Filtering**
   - API: 150 codes → Parse: 600+ items
   - Filter by card + stage → Display: 20-50 items

3. **Pattern Matching**
   - Card: Case-insensitive regex
   - Stage: Keyword-based matching
   - Flexible: Same code can appear in multiple contexts

4. **Dynamic Updates**
   - Change stage → Clear code → Re-filter list
   - Always shows relevant codes only

5. **Two Modes**
   - **Risk Dispute:** API-based, card+stage filtering
   - **SS Dispute:** Hardcoded, transaction-based filtering

---

## 🔗 Related Files

| File | Purpose | Lines |
|------|---------|-------|
| `dispute-detail-component.ts` | Main component | 225-299 |
| `dispute-utils.ts` | Format & parse logic | 134-182 |
| `dispute-management-constants.ts` | Hardcoded SS codes | - |
| `risk-dispute-management-international-component.ts` | List view (similar logic) | 1030-1118 |

---

## 🎓 Learning Points

### For Developers

1. **Understand the GROUP field structure**
   - Card types separated by commas
   - Stage keywords in UPPERCASE
   - Order doesn't matter

2. **Pattern matching is flexible**
   - Regex allows partial matches
   - Case-insensitive for card types
   - Case-sensitive for stage keywords

3. **Filtering is crucial**
   - Without filter: 600+ codes (overwhelming)
   - With filter: 20-50 codes (manageable)

4. **Stage changes require re-filtering**
   - Old code may not be valid
   - Always clear and re-filter

### For Testers

1. **Test all card types**
   - VISA, MASTERCARD, JCB, UNIONPAY, AMEX

2. **Test all stages**
   - 7 stages, each should show different codes

3. **Test stage changes**
   - Code should be cleared
   - New list should appear

4. **Test edge cases**
   - No codes for combination (empty dropdown)
   - Single code only
   - Many codes (scroll performance)

---

**Version:** 1.0  
**Last Updated:** December 2025  
**Author:** Development Team  
**Status:** ✅ Production Ready

