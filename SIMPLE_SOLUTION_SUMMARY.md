# ✅ GIẢI PHÁP ĐƠN GIẢN - Chỉ Sửa Backend

## 🎯 Overview

**<PERSON><PERSON><PERSON> cầu:** User không sửa field → Field không được update

**Giải pháp:** Giữ nguyên frontend format, chỉ sửa backend để check null và skip

---

## ✅ Đã Làm

### 1. Frontend - Reverted về format cũ ✅

**Files changed:**
- `ss-dispute-management.service.ts` - Removed new method
- `risk-dispute-management-international-component.ts` - Reverted to original
- `share-dispute-input-dialog.component.ts` - Sửa onSubmit() return ALL fields

**Key change in dialog:**
```typescript
// OLD: Chỉ return fields != null
if (this.businessCategory !== null) {
    updatedFields.businessCategory = this.businessCategory;
}

// NEW: Return TẤT CẢ fields (kể cả null)
const updatedFields: any = {
    businessCategory: this.businessCategory,      // null or '' or value
    disputeCode: this.disputeCode,                // null or '' or value
    disputeReason: this.disputeReason,            // null or '' or value
    // ... all 11 fields
};
```

**Result:**
- ✅ Giữ nguyên API endpoint `/dispute/updateByBatch`
- ✅ Giữ nguyên request format (gửi full dispute objects)
- ✅ Dialog return ALL fields (backend sẽ check null)
- ✅ No breaking changes

---

## ⚠️ Cần Làm

### 2. Backend - Sửa DAO Method

**File:** `DisputeDao.java`

**Method:** `updateDisputeByBatch()`

**Template:** `backend_simple_updateByBatch.java`

**Key Logic:**
```java
// Build dynamic SQL
StringBuilder sql = new StringBuilder("UPDATE tb_dispute SET ");
List<String> updates = new ArrayList<>();
List<Object> params = new ArrayList<>();

// Only add field to UPDATE if NOT NULL
if (item.getBusinessCategory() != null) {
    updates.add("s_business_category = ?");
    params.add(item.getBusinessCategory());
}

if (item.getDisputeCode() != null) {
    updates.add("s_dispute_code = ?");
    params.add(item.getDisputeCode().trim());
}

// ... check all fields ...

// Always update audit fields
updates.add("d_update = SYSDATE");
updates.add("s_updated_by = ?");
params.add(item.getOperatorId());

// Build complete SQL
sql.append(String.join(", ", updates));
sql.append(" WHERE n_id = ? AND n_parent_id IS NULL");
```

---

## 📊 How It Works

### Example 1: User chỉ chọn Business Category

**Frontend sends:**
```javascript
{
  data: [
    {
      id: 1,
      businessCategory: '',        // Will update
      disputeCode: null,           // Will SKIP
      disputeReason: null,         // Will SKIP
      disputeStage: null,          // Will SKIP
      outcome: null,               // Will SKIP
      // ... all other fields: null
      operatorId: 123,
      operatorName: 'John Doe',
      // ... required fields ...
    }
  ]
}
```

**Backend generates:**
```sql
UPDATE tb_dispute 
SET 
    s_business_category = '',  -- Only this field
    s_merchant_group = '0',    -- Always
    d_update = SYSDATE,        -- Always
    s_updated_by = 123         -- Always
WHERE n_id = 1 AND n_parent_id IS NULL;
```

**Result:** ✅ Chỉ update `s_business_category`, tất cả fields khác KHÔNG đổi!

---

### Example 2: User chọn 3 fields

**Frontend sends:**
```javascript
{
  data: [
    {
      id: 1,
      businessCategory: '',        // Will update
      disputeCode: '10001',        // Will update
      disputeReason: null,         // Will SKIP
      disputeStage: null,          // Will SKIP
      outcome: 'won',              // Will update
      // ... other fields: null
    }
  ]
}
```

**Backend generates:**
```sql
UPDATE tb_dispute 
SET 
    s_business_category = '',
    s_dispute_code = '10001',
    s_outcome = 'won',
    s_merchant_group = '0',
    d_update = SYSDATE,
    s_updated_by = 123
WHERE n_id = 1 AND n_parent_id IS NULL;
```

**Result:** ✅ Update 3 fields, others unchanged!

---

## 🚀 Implementation Steps

### Step 1: Frontend (✅ DONE)

Already done! No more changes needed.

---

### Step 2: Backend (~30 minutes)

1. **Open file:**
   ```bash
   vim DisputeDao.java
   ```

2. **Find method:**
   ```java
   public static JsonObject updateDisputeByBatch(List<SendDisputeParam> listDisputes)
   ```

3. **Replace with code from:**
   ```bash
   cat backend_simple_updateByBatch.java
   ```

4. **Build:**
   ```bash
   mvn clean package
   ```

5. **Deploy to server**

---

### Step 3: Test (~15 minutes)

**Test Case 1: Single field**
```
1. Select 1 dispute with businessCategory = '5'
2. Open dialog
3. Change Business Category to Blank
4. Click Update
5. ✅ Verify: Only s_business_category = '', other fields unchanged
```

**Test Case 2: Multiple fields**
```
1. Select 1 dispute
2. Change 3 fields
3. Click Update
4. ✅ Verify: Only 3 fields updated, others unchanged
```

**Test Case 3: No changes**
```
1. Select dispute
2. Open dialog
3. Don't change anything
4. Click Update
5. ✅ Dialog closes, no API call
```

---

## ✨ Benefits

### 1. Minimal Changes
- ✅ Frontend: 1 method sửa (`onSubmit`)
- ✅ Backend: 1 method sửa (`updateDisputeByBatch`)
- ✅ No new API endpoint
- ✅ No database changes

### 2. Backward Compatible
- ✅ Same API endpoint
- ✅ Same request format
- ✅ No breaking changes

### 3. Simple Logic
- ✅ `if (field != null)` → Update
- ✅ `if (field == null)` → Skip
- ✅ Dễ hiểu, dễ maintain

### 4. Works with Null vs ''
- ✅ `null` = Skip (không update)
- ✅ `''` = Update to blank
- ✅ `value` = Update to value

---

## 📁 Files Overview

### Frontend (All Done ✅)
```
src/app/
├── service/
│   └── ss-dispute-management.service.ts
│       ✅ Reverted (removed new method)
│
├── module/.../risk-dispute-management-international/
│   └── risk-dispute-management-international-component.ts
│       ✅ Reverted (original updateDisputesBatch)
│
└── module/.../share-dispute-input-dialog/
    ├── share-dispute-input-dialog.component.ts
    │   ✅ Modified onSubmit() to return ALL fields
    │
    ├── SIMPLE_BACKEND_SOLUTION.md
    │   ✅ Detailed explanation (this solution)
    │
    └── backend_simple_updateByBatch.java
        ✅ Backend code template (copy-paste ready)
```

### Backend (Need to Deploy ⚠️)
```
DisputeDao.java
└── updateDisputeByBatch()
    ⚠️ Replace with code from backend_simple_updateByBatch.java
```

---

## 🧪 Testing Queries

### Before Update
```sql
SELECT n_id, s_business_category, s_dispute_code, s_outcome
FROM tb_dispute
WHERE n_id = 1;

-- Example: 1, '5', '10001', NULL
```

### After Update (User only changed Business Category)
```sql
SELECT n_id, s_business_category, s_dispute_code, s_outcome
FROM tb_dispute
WHERE n_id = 1;

-- Expected: 1, '', '10001', NULL
--           ↑ Changed  ↑ Same  ↑ Same
```

---

## ⏱️ Time Estimate

- **Frontend:** ✅ Done (0 minutes)
- **Backend:** Replace method (30 minutes)
- **Testing:** Test cases (15 minutes)
- **Total:** ~45 minutes

---

## 📞 Next Steps

1. **✅ Frontend:** Done, no action needed

2. **⚠️ Backend:**
   - Open `DisputeDao.java`
   - Replace `updateDisputeByBatch()` method
   - Copy from `backend_simple_updateByBatch.java`
   - Build and deploy

3. **⚠️ Test:**
   - Test single field update
   - Test multiple fields update
   - Test no changes
   - Verify in database

---

## 🎉 Summary

### What Changed:
- ✅ Frontend: Dialog return ALL fields (kể cả null)
- ⚠️ Backend: Check null và build dynamic SQL

### What Stayed:
- ✅ Same API endpoint
- ✅ Same request format
- ✅ No database changes
- ✅ No new dependencies

### Result:
**User không sửa field → Backend skip field đó → Field không được update! ✅**

---

## 📚 Documentation

- **Detailed Guide:** `SIMPLE_BACKEND_SOLUTION.md`
- **Backend Code:** `backend_simple_updateByBatch.java`
- **Original Reference:** `code_be_update_by_batch.txt`

---

**Giải pháp này ĐƠN GIẢN và NHANH! Chỉ cần 45 phút! 🚀**

