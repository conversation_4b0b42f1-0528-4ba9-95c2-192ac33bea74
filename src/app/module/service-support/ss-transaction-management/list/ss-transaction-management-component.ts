import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, ElementRef } from '@angular/core';
import { DatePipe } from '@angular/common'
import { SSTransManagementService } from '@service/ss-trans-management.service';
import { Globals } from '@core/global'
import { Observable, Subscription } from 'rxjs';
import 'rxjs/add/observable/zip';
import 'rxjs/add/observable/combineLatest';
import { LazyLoadEvent } from 'primeng/api';
import { SSTransactionManagementSearch } from '../search/ss-transaction-management-search.component';
import { ActivatedRoute, Router } from '@angular/router';
import { InternationalService } from '@service/international.service';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { CreateDisputeModalComponent } from '../dispute/create-dispute-modal/create-dispute-modal.component';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AnalysicDialogComponent } from '../dialog/dialog-analysis-error';
import { ColumnDisplayComponent } from '../display-column/column-display.component';
import { DBTransactionPurchaseDetailComponent } from '../detail/direct-debit/transaction-db-purchase-detail.component';
import { DBTransactionRefundDetailComponent } from '../detail/direct-debit/transaction-db-refund-detail.component';
import { DBTransactionRequestRefundDetailComponent } from '../detail/direct-debit/transaction-db-request-refund-detail.component';
import { TransactionQTPurchaseDetailComponent } from '../detail/international/transaction-qt-purchase-detailComponent';
import { TransactionQTRefundDetailComponent } from '../detail/international/transaction-qt-refund-detailComponent';
import { TransactionQTRequestRefundDetailComponent } from '../detail/international/transaction-qt-request-refund-detailComponent';
import { TransactionQTVoidDetailComponent } from '../detail/international/transaction-qt-void-detailComponent';
import { TransactionQRRefundDetailComponent } from '../detail/qr/transaction-qr-refund-detailComponent';
import { TransactionQRRequestRefundDetailComponent } from '../detail/qr/transaction-qr-request-refund-detailComponent';
import { TransactionQRPurchaseDetailComponent } from '../detail/qr/transaction-qr-purchase-detailComponent';
import { TransactionNDRequestRefundDetailComponent } from '../detail/domestic/transaction-nd-request-refund-detailComponent';
import { TransactionRefundDomesticDetailComponent } from '../detail/domestic/transaction-nd-refund-detailComponent';
import { TransactionPurchaseDomesticDetailComponent } from '../detail/domestic/transaction-nd-purchase-detailComponent';
import { UposInstallmentPurchaseDetailComponent } from '../detail/upos/installment/installment_purchase_detail_component';
import { UposInstallmentRefundDetailComponent } from '../detail/upos/installment/installment_refund_detail_component';
import { UposInstallmentRequestRefundDetailComponent } from '../detail/upos/installment/installment_request_refund_detail_component';
import { UposInstallmentVoidDetailComponent } from '../detail/upos/installment/installment_void_detail_component';
import { UposCardPurchaseDetailComponent } from '../detail/upos/card_payment/card_purchase_detail_component';
import { UposCardRefundDetailComponent } from '../detail/upos/card_payment/card_refund_detail_component';
import { UposCardRequestRefundDetailComponent } from '../detail/upos/card_payment/card_request_refund_detail_component';
import { UposCardVoidDetailComponent } from '../detail/upos/card_payment/card_void_detail_component';
import { LocalService } from '@service/local-service.service';
import { TransactionVietQRPurchaseDetailComponent } from '../detail/vietqr/transaction-vietqr-purchase-detailComponent';
import { TransactionVietQRRefundDetailComponent } from '../detail/vietqr/transaction-vietqr-refund-detailComponent';
import { TransactionVietQRRequestRefundDetailComponent } from '../detail/vietqr/transaction-vietqr-request-refund-detailComponent';
import { BNPLTransactionDetailComponent } from '../detail/bnpl/BNPL_Transaction_DetailComponent';
import { BNPLRequestRefundDetailComponent } from '../detail/bnpl/BNPL_Request_Refund_DetailComponent';
import { BNPLRefundDetailComponent } from '../detail/bnpl/BNPL_Refund_DetailComponent';
import { BNPLVoidDetailComponent } from '../detail/bnpl/BNPL_Void_DetailComponent';
import { BNPLAmigoTransactionDetailComponent } from '../detail/bnpl/BNPL_Amigo_Transaction_DetailComponent';

@Component({
    selector: 'ss-transaction-management',
    templateUrl: './ss-transaction-management-component.html',
    styleUrls: ['./ss-transaction-management-component.scss'],
    providers: [SSTransManagementService, DialogService]
})

export class SSTransactionManagementComponent implements OnInit, OnDestroy {
    public title = "Transaction Management";
    public pageSize: number;
    public sub: Subscription;
    public resultsLength = 0;
    public loading: boolean;
    public data: Array<any> = [];
    private offsetHeight = 320;
    public flexScrollHeight = '200px';
    public showFormSearch = true;
    public gateList: Array<any> = [];
    public gateListEcom: Array<any>;
    public gateListUpos: Array<any>;

    public paygateSelected: Array<any> = [];
    // public partnerName: String;
    public partnerList: Array<any>;
    public partnerSelected: Array<any> = [];
    public referralPartnerList: Array<any>;
    public referralPartnerSelected: Array<any> = [];
    public acquirerList: Array<any> = [];
    public acquirerListQt: Array<any> = [];
    public acquirerListNd: Array<any> = [];
    public acquirerListQr: Array<any> = [];
    public acquirerListCard: Array<any> = [];
    public acquirerListVietQr: Array<any> = [];


    public acquirerSelected: Array<any> = [];
    public merchantId: String;
    public invoiceId: String;
    public transId: String;
    public paymentId: String;
    public quicklinkId: String;
    public orderRef: String;
    public merchantTransRef: String;
    public qrId: String;
    public qrChannelList: Array<any>;
    public channelListQt: Array<any>;
    public channelListNd: Array<any>;
    public channelListQr: Array<any>;
    public channelListBnpl: Array<any>;
    public channelListCard: Array<any>;

    public qrChannelSelected: Array<any> = [];
    public cardList: any[];
    public cardListQt: any[];
    public cardListNd: any[];
    public cardListQr: any[];
    public cardListVietQr: any[];
    public cardListBnpl: any[];
    public cardListCard: any[];
    public cardListDD: any[];

    public cardSelected: Array<any> = [];
    public cardNumber: String;
    public sourceList: Array<any>;
    public sourceListQt: Array<any>;
    public sourceListDD: Array<any>;
    public sourceListNd: Array<any>;

    public sourceSelected: Array<any> = [];
    public binCountryList: Array<any>;
    public binCountryListQt: Array<any>;
    public binCountryListCard: Array<any>;
    public binCountrySelected: Array<any> = [];
    public issuerList: Array<any>;
    public issuerListQt: Array<any>;
    public issuerListNd: Array<any>;
    public issuerListQr: Array<any>;
    public issuerListCard: Array<any>;

    public issuerSelected: Array<any> = [];
    public installmentBankList: Array<any>;
    public installmentBankListQt: Array<any>;
    // public installmentBankListCard: Array<any>;

    public installmentBankSelected: Array<any> = [];
    public installmentPeriodList: Array<any>;
    public installmentPeriodListQt: Array<any>;
    public installmentPeriodListCard: Array<any>;

    public installmentPeriodSelected: Array<any> = [];
    public promotionCode: String;
    public promotionName: String;
    public originalAmount: String;
    public transCurrency: Array<any> = [];
    public transTypeList: Array<any>;
    public transTypeListBlank: Array<any>;
    public transTypeListQt: Array<any>;
    public transTypeListNd: Array<any>;
    public transTypeListQr: Array<any>;
    public transTypeListQr2: Array<any>;
    public transTypeListBnpl                                                                                                                                                                        : Array<any>;
    public transTypeListCard: Array<any>;
    public transTypeListDD: Array<any>;

    public transCurrencyList: Array<any>;
    public transCurrencyListQt: Array<any>;
    public transTypeSelected: Array<any> = [];
    public authenStateList: Array<any>;
    public authenStateListQt: Array<any>;
    public authenStateSelected: Array<any> = [];
    public responseCodeList: Array<any>;
    public responseCodeListQt: Array<any>;
    public responseCodeListNd: Array<any>;
    public responseCodeListQr: Array<any>;
    public responseCodeListBnpl: Array<any>;
    public responseCodeListDD: Array<any>;
    public responseCodeListCard: Array<any>;
    public responseCodeSelected: Array<any> = [];
    public invoiceStateList: Array<any>;
    public invoiceStateSelected: Array<any> = [];
    public transStateList: Array<any>;
    public transStateSelected: Array<any> = [];
    public authoCode: String;
    public installmentStatusList: Array<any>;
    public installmentStatusListQt: Array<any>;
    public installmentStatusListCard: Array<any>;
    public installmentStatusSelected: Array<any> = [];
    public inteTypeSelected: Array<any> = [];
    public inteTypeList: Array<any>;
    public themeList: Array<any>;
    public themeSelected: Array<any> = [];
    public merchantChannelList: Array<any>;
    public merchantChannelSelected: Array<any> = [];
    private delayTimer: any;
    private columnDisplayRef: any;
    public numberColumn: any;
    public coLumnSelected: any;
    public coLumnSelectedArray: Array<any> = [];
    public cols: any[];
    public reply: String;
    public replyMessage: String;
    public reasonCode: String;
    public authenticationId: String;
    public networkTransId: String;
    public requestId: String;
    public cardVerificationInfo: String;
    public providerMessage: String;

    public ipnStatusList: Array<any>;
    public ipnStatusSelected: Array<any> = [];

    public contractTypeSelected: Array<any> = [];
    public contractTypeList: Array<any>;
    public bankTransId: String;

    public page = [
        {
            label: '1',
            value: 1
        },
        {
            label: '2',
            value: 2
        },
        {
            label: '3',
            value: 3
        },
        {
            label: '4',
            value: 4
        },
        {
            label: '5',
            value: 5
        }
    ];
    public selectedTrans: any[] = [];
    public showDisputeCheck: boolean = false;
    @ViewChild(SSTransactionManagementSearch, { static: true }) searchForm: SSTransactionManagementSearch;

    constructor(
        private ssTranService: SSTransManagementService
        , private internationalService: InternationalService
        , public datepipe: DatePipe
        , public global: Globals
        , private route: ActivatedRoute
        , private router: Router
        , private toastr: ToastrService
        , public dialogService: MatDialog
        , public dialogService2: DialogService
        , private localService: LocalService
        , public dialogPrime: DialogService) {
    }

    ngOnInit() {
        let params = this.route.snapshot.queryParams
        if (params['order_reference']) {
            this.orderRef = params['order_reference'];
            this.searchForm.typeFilter = 'contains';
        }
        this.searchForm.init(params);
        this.resultsLength = 0;

        this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
        this.cols =  this.searchForm.columnItems;
        this.cols = this.cols.filter(item => item.active === true);
        this.cols.sort((a, b) => (a.order > b.order) ? 1 : -1);
    }

    @HostListener('window:resize', ['$event'])
    onResize(event) {
        this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }

    }

    filter() {
        // var self = this;
        // this.searchForm.first = 0;
        // this.searchForm.page = 1;
        // clearTimeout(this.delayTimer);
        // this.delayTimer = setTimeout(function () {
        //     return self.searchData().subscribe(responses => {
        //         self.resultsLength = responses.response.data.total;
        //         self.data = responses.response.data.list;
        //         self.initPage(self.resultsLength);
        //     });
        // }, 3000);
    }
    keyup($event) {
        var self = this;
        this.searchForm.first = 0;
        this.searchForm.page = 1;
        return self.searchData().subscribe(responses => {
            // self.resultsLength = responses.response.data.total;
            self.resultsLength = 0;
            self.data = responses.response.data.list;
            self.initPage(self.resultsLength);
        });
    }
    filterResponseCode(data: any){

    }

    filterResponseCodeDanhnt(data: any) {

        var self = this;
        this.searchForm.first = 0;
        this.searchForm.page = 1;
        this.paygateSelected = [];
        for (var i in this.responseCodeSelected) {
            // kiem tra response code đang check là loại paychannel(gate) gì
            let gates = this.responseCodeSelected[i].label.split('-');
            if (gates) {
                let gate = gates[gates.length - 1];
                // lấy paychannel(gate) tương ứng với response code đang select
                let gateSelected = this.gateList.find(g => g.value === gate.trim());
                let checkExit = this.paygateSelected.find(g => g === gateSelected);
                // check paychannel (Gate) nếu chưa tồn tại trong
                if (!checkExit) {
                    this.paygateSelected.push(gateSelected);
                    // nếu là response code QT thì sẽ bao gồm pay channel QT + Installment,PR
                    if (gate.trim() === 'QT') {
                        let gateInstallment = this.gateList.find(g => g.value === 'Installment');
                        let gatePR = this.gateList.find(g => (g.value === 'QT' || g.value === 'ND' || g.value === 'QR' || g.value === 'BNPL'));
                        this.paygateSelected.push(gateInstallment);
                        this.paygateSelected.push(gatePR);
                    }

                }
            }
        }

        clearTimeout(this.delayTimer);
        this.delayTimer = setTimeout(function () {
            return self.searchData().subscribe(responses => {
                // self.resultsLength = responses.response.data.total;
                self.resultsLength = 0;
                self.data = responses.response.data.list;
                self.initPage(this.resultsLength);
            });
        }, 3000);

    }

    onClear() {
        this.paygateSelected = [];
        this.qrId = '';
        this.qrChannelSelected = [];
        this.cardSelected = [];
        this.issuerSelected = [];
        this.installmentBankSelected = [];
        this.authenStateSelected = [];
        this.installmentStatusSelected = [];
        this.inteTypeSelected = [];
        // this.partnerName = '';
        this.partnerSelected = [];
        this.referralPartnerSelected = [];
        this.acquirerSelected = [];
        this.merchantId = '';
        this.invoiceId = '';
        this.transId = '';
        this.paymentId = '';
        this.orderRef = '';
        this.merchantTransRef = '';
        this.cardNumber = '';
        this.sourceSelected = [];
        this.binCountrySelected = [];
        this.promotionCode = '';
        this.promotionName = '';
        this.originalAmount = '';
        this.transCurrency = [];
        this.transTypeSelected = [];
        this.responseCodeSelected = [];
        this.invoiceStateSelected = [];
        this.transStateSelected = [];
        this.authoCode = '';
        this.themeSelected = [];
        this.merchantChannelSelected = [];
        this.ipnStatusSelected = [];
        this.reply = '';
        this.replyMessage= '';
        this.reasonCode = '';
        this.authenticationId = '';
        this.networkTransId = '';
        this.requestId = '';
        this.cardVerificationInfo = '';
        this.providerMessage = '';

        this.contractTypeSelected = [];
        this.bankTransId = '';
        this.filterGateControler([]);
    }

    onClearPaygate() {
        this.qrChannelSelected = [];
        this.cardSelected = [];
        this.issuerSelected = [];
        this.installmentBankSelected = [];
        this.authenStateSelected = [];
        this.installmentStatusSelected = [];
        this.inteTypeSelected = [];
        this.referralPartnerSelected = [];
        this.acquirerSelected = [];
        this.sourceSelected = [];
        this.binCountrySelected = [];
        this.transCurrency = [];
        this.transTypeSelected = [];
        this.responseCodeSelected = [];
        this.invoiceStateSelected = [];
        this.transStateSelected = [];
        this.themeSelected = [];
        this.ipnStatusSelected = [];
        this.contractTypeSelected = [];
    }

    onSync() {
        return this.ssTranService.syncTransaction().subscribe(responses => {
            this.toastr.success('Sync Transaction Successfully', null, { positionClass: 'toast-top-center' });
            this.onSubmit();
        });
    }

    searchData() {
        this.router.navigate(['/ss-trans-management'], { queryParams: this.redirectParams() });
        let from_date;
        let firstSearchDefine : boolean;
        if (this.searchForm.firstSearch) {
            firstSearchDefine = true;
        }
        from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        this.validateFilterColumn();

        var params = {
            'from_date': from_date,
            'to_date': to_date,
            'gate': this.paygateSelected ? this.paygateSelected.map(x => x.value).join(",") : "",
            // 'partner_name': this.partnerName ? this.partnerName.trim() : "",
            'partner_name': this.partnerSelected ? this.partnerSelected.map(x => x.value).join(",") : "",
            'referralPartner': this.referralPartnerSelected ? this.referralPartnerSelected.map(x => x.value).join(",") : "",
            'acquirerId': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(",") : "",
            'merchantId': this.merchantId ? this.merchantId.trim() : "",
            'contractType': this.contractTypeSelected ? this.contractTypeSelected.map(x => x.value).join(",") : "",
            'invoiceId': this.invoiceId ? this.invoiceId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'paymentId': this.paymentId ? this.paymentId.trim() : "",
            'quicklinkId': this.quicklinkId ? this.quicklinkId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchantTransactionRef': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'bankTransId': this.bankTransId ? this.bankTransId.trim() : "",
            'qr_id': this.qrId ? this.qrId : "",
            'qr_channel': this.qrChannelSelected ? this.qrChannelSelected.map(x => x.value).join(",") : "",
            'card_type': this.cardSelected ? this.cardSelected.map(x => x.value).join(",") : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'source': this.sourceSelected ? this.sourceSelected.map(x => x.value).join(",") : "",
            'bin_country': (this.binCountrySelected && this.binCountrySelected.length <= 10) ? this.binCountrySelected.map(x => x.value).join(',') : (this.binCountrySelected && this.binCountrySelected.length === this.binCountryList.length) ? '' : this.binCountrySelected ? this.binCountrySelected.slice(0, 10).map(x => x.value).join(',') : '',
            'issue': this.issuerSelected ? this.issuerSelected.map(x => x.value).join(",") : "",
            'installment_bank': this.installmentBankSelected ? this.installmentBankSelected.map(x => x.value).join(",") : "",
            'promotion_code': this.promotionCode ? this.promotionCode.trim() : "",
            'promotion_name': this.promotionName ? this.promotionName.trim() : "",
            'original_amount': this.originalAmount ? Number(this.originalAmount) : 0,
            'trans_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'authentication_state': this.authenStateSelected ? this.authenStateSelected.map(x => x.value).join(",") : "",
            'response_code': this.responseCodeSelected ? this.responseCodeSelected.map(x => x.value).join(",") : "",
            'invoice_state': this.invoiceStateSelected ? this.invoiceStateSelected.map(x => x.value).join(",") : "",
            'trans_state': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'authorization_code': this.authoCode ? this.authoCode.trim() : "",
            'installment_status': this.installmentStatusSelected ? this.installmentStatusSelected.map(x => x.value).join(",") : "",
            'installment_period': this.installmentPeriodSelected ? this.installmentPeriodSelected.map(x => x.value).join(",") : "",
            'integration_type': this.inteTypeSelected ? this.inteTypeSelected.map(x => x.value).join(",") : "",
            'theme': this.themeSelected ? this.themeSelected.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'ipnStatus': this.ipnStatusSelected ? this.ipnStatusSelected.map(x => x.value).join(",") : "",
            'page_size': 100,
            'page': (this.searchForm.page - 1) + '',
            'file_name': this.searchForm.typeFilter.length == 0 ? 'equals' : this.searchForm.typeFilter.toString().replace(/,/g, ''),
            'firstSearch': this.searchForm.firstSearch,
            'reply': this.reply ? this.reply.trim() : "",
            'replyMessage': this.replyMessage ? this.replyMessage.trim() : "",
            'reasonCode': this.reasonCode ? this.reasonCode.trim() : "",
            'authenticationId': this.authenticationId ? this.authenticationId.trim() : "",
            'networkTransId': this.networkTransId ? this.networkTransId.trim() : "",
            'requestId': this.requestId ? this.requestId.trim() : "",
            'cardVerificationInfo': this.cardVerificationInfo ? this.cardVerificationInfo.trim() : "",
            'providerMessage': this.providerMessage ? this.providerMessage.trim() : ""
        };

        this.searchForm.firstSearch = false;
        if (firstSearchDefine){
            return null;
        }
        return this.ssTranService.getList(params);
    }

    getTotal() {
        this.router.navigate(['/ss-trans-management'], { queryParams: this.redirectParams() });
        let from_date;
        let firstSearchDefine : boolean;
        if (this.searchForm.firstSearch) {
            firstSearchDefine = true;
        }
        //     const fromDate = new Date();
        //     fromDate.setHours(fromDate.getHours() - 1);
        //     from_date = this.datepipe.transform(fromDate, 'dd/MM/yyyy HH:mm:ss');
        // } else {
            from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        // }
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        this.validateFilterColumn();

        var params = {
            'from_date': from_date,
            'to_date': to_date,
            'gate': this.paygateSelected ? this.paygateSelected.map(x => x.value).join(",") : "",
            // 'installment' : pay_gate.includes('Installment')? "1" : "",
            // 'promotion' : pay_gate.includes('PR')? "1" : "",
            // 'partner_name': this.partnerName ? this.partnerName.trim() : "",
            'partner_name': this.partnerSelected ? this.partnerSelected.map(x => x.value).join(",") : "",
            'referralPartner': this.referralPartnerSelected ? this.referralPartnerSelected.map(x => x.value).join(",") : "",
            'acquirerId': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(",") : "",
            'merchantId': this.merchantId ? this.merchantId.trim() : "",
            'contractType': this.contractTypeSelected ? this.contractTypeSelected.map(x => x.value).join(",") : "",
            'invoiceId': this.invoiceId ? this.invoiceId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'paymentId': this.paymentId ? this.paymentId.trim() : "",
            'quicklinkId': this.quicklinkId ? this.quicklinkId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchantTransactionRef': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'bankTransId': this.bankTransId ? this.bankTransId.trim() : "",
            'qr_id': this.qrId ? this.qrId : "",
            'qr_channel': this.qrChannelSelected ? this.qrChannelSelected.map(x => x.value).join(",") : "",
            'card_type': this.cardSelected ? this.cardSelected.map(x => x.value).join(",") : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'source': this.sourceSelected ? this.sourceSelected.map(x => x.value).join(",") : "",
            // 'bin_country': this.binCountrySelected ? this.binCountrySelected.map(x => x.value).join(",") : "",
            'bin_country': (this.binCountrySelected && this.binCountrySelected.length <= 10) ? this.binCountrySelected.map(x => x.value).join(',') : (this.binCountrySelected && this.binCountrySelected.length === this.binCountryList.length) ? '' : this.binCountrySelected ? this.binCountrySelected.slice(0, 10).map(x => x.value).join(',') : '',
            'issue': this.issuerSelected ? this.issuerSelected.map(x => x.value).join(",") : "",
            'installment_bank': this.installmentBankSelected ? this.installmentBankSelected.map(x => x.value).join(",") : "",
            'promotion_code': this.promotionCode ? this.promotionCode.trim() : "",
            'promotion_name': this.promotionName ? this.promotionName.trim() : "",
            'original_amount': this.originalAmount ? Number(this.originalAmount) : 0,
            'trans_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'authentication_state': this.authenStateSelected ? this.authenStateSelected.map(x => x.value).join(",") : "",
            'response_code': this.responseCodeSelected ? this.responseCodeSelected.map(x => x.value).join(",") : "",
            'invoice_state': this.invoiceStateSelected ? this.invoiceStateSelected.map(x => x.value).join(",") : "",
            'trans_state': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'authorization_code': this.authoCode ? this.authoCode.trim() : "",
            'installment_status': this.installmentStatusSelected ? this.installmentStatusSelected.map(x => x.value).join(",") : "",
            'installment_period': this.installmentPeriodSelected ? this.installmentPeriodSelected.map(x => x.value).join(",") : "",
            'integration_type': this.inteTypeSelected ? this.inteTypeSelected.map(x => x.value).join(",") : "",
            'theme': this.themeSelected ? this.themeSelected.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'ipnStatus': this.ipnStatusSelected ? this.ipnStatusSelected.map(x => x.value).join(",") : "",
            'page_size': 100,
            'page': (this.searchForm.page - 1) + '',
            'file_name': this.searchForm.typeFilter.length == 0 ? 'equals' : this.searchForm.typeFilter.toString().replace(/,/g, ''),
            'firstSearch': this.searchForm.firstSearch,
            'reply': this.reply ? this.reply.trim() : "",
            'replyMessage': this.replyMessage ? this.replyMessage.trim() : "",
            'reasonCode': this.reasonCode ? this.reasonCode.trim() : "",
            'authenticationId': this.authenticationId ? this.authenticationId.trim() : "",
            'networkTransId': this.networkTransId ? this.networkTransId.trim() : "",
            'requestId': this.requestId ? this.requestId.trim() : "",
            'cardVerificationInfo': this.cardVerificationInfo ? this.cardVerificationInfo.trim() : "",
            'providerMessage': this.providerMessage ? this.providerMessage.trim() : ""
        };

        this.searchForm.firstSearch = false;
        if (firstSearchDefine){
            return null;
        }
        return this.ssTranService.getTotal(params);
    }

    download() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        // let keys = Array.from( myMap.keys() );
        this.validateFilterColumn();
        // let column_active = Array.from(this.searchForm.columnMap.keys());
        let column_active1 = this.searchForm.columnItems.filter(element => element.active === true);
        let column_active = column_active1.map(element => element.code);
        let column_list = this.searchForm.columnItems.map(element => element.code);
        /* 01-02-2023 If Column Active contains amount column, add column currency next to amount column */
        let orgAmountIdx = column_active.indexOf('originalAmount');
        let transAmountIdx = column_active.indexOf('transAmount')
        if (orgAmountIdx !== -1) {
            column_active.splice(orgAmountIdx + 1, 0, 'currency');
            column_list.splice(column_list.indexOf('originalAmount') + 1, 0, 'currency');
        }
        if (transAmountIdx !== -1) {
            column_active.splice(transAmountIdx + 1, 0, 'currency');
            column_list.splice(column_list.indexOf('transAmount') + 1, 0, 'currency');
        }
        //Download by selected if available
        let selectedSkey = '';
        if (this.selectedTrans.length > 0 ) {
            this.selectedTrans.map(item => {
                selectedSkey = selectedSkey + "," + item.key;
            });
            selectedSkey = selectedSkey.slice(1); // remove first ',' redundent
        }

        var paramsDownload = {
            'from_date': from_date,
            'to_date': to_date,
            'gate': this.paygateSelected ? this.paygateSelected.map(x => x.value).join(",") : "",
            // 'installment' : pay_gate.includes('Installment')? "1" : "",
            // 'promotion' : pay_gate.includes('PR')? "1" : "",
            // 'partner_name': this.partnerName ? this.partnerName.trim() : "",            
            'partner_name': this.partnerSelected ? this.partnerSelected.map(x => x.value).join(",") : "",
            'referralPartner': this.referralPartnerSelected ? this.referralPartnerSelected.map(x => x.value).join(",") : "",
            'acquirerId': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(",") : "",
            'merchantId': this.merchantId ? this.merchantId.trim() : "",
            'contractType': this.contractTypeSelected ? this.contractTypeSelected.map(x => x.value).join(",") : "",
            'invoiceId': this.invoiceId ? this.invoiceId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'paymentId': this.paymentId ? this.paymentId.trim() : "",
            'quicklinkId': this.quicklinkId ? this.quicklinkId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchantTransactionRef': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'bankTransId': this.bankTransId ? this.bankTransId.trim() : "",
            'qr_id': this.qrId ? this.qrId : "",
            'qr_channel': this.qrChannelSelected ? this.qrChannelSelected.map(x => x.value).join(",") : "",
            'card_type': this.cardSelected ? this.cardSelected.map(x => x.value).join(",") : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'source': this.sourceSelected ? this.sourceSelected.map(x => x.value).join(",") : "",
            // 'bin_country': this.binCountrySelected ? this.binCountrySelected.map(x => x.value).join(",") : "",
            'bin_country': (this.binCountrySelected && this.binCountrySelected.length <= 10) ? this.binCountrySelected.map(x => x.value).join(',') : (this.binCountrySelected && this.binCountrySelected.length === this.binCountryList.length) ? '' : this.binCountrySelected ? this.binCountrySelected.slice(0, 10).map(x => x.value).join(',') : '',
            'issue': this.issuerSelected ? this.issuerSelected.map(x => x.value).join(",") : "",
            'installment_bank': this.installmentBankSelected ? this.installmentBankSelected.map(x => x.value).join(",") : "",
            'promotion_code': this.promotionCode ? this.promotionCode.trim() : "",
            'promotion_name': this.promotionName ? this.promotionName.trim() : "",
            'original_amount': this.originalAmount ? this.originalAmount : 0,
            'trans_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'authentication_state': this.authenStateSelected ? this.authenStateSelected.map(x => x.value).join(",") : "",
            'response_code': this.responseCodeSelected ? this.responseCodeSelected.map(x => x.value).join(",") : "",
            'invoice_state': this.invoiceStateSelected ? this.invoiceStateSelected.map(x => x.value).join(",") : "",
            'trans_state': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'authorization_code': this.authoCode ? this.authoCode.trim() : "",
            'installment_status': this.installmentStatusSelected ? this.installmentStatusSelected.map(x => x.value).join(",") : "",
            'installment_period': this.installmentPeriodSelected ? this.installmentPeriodSelected.map(x => x.value).join(",") : "",
            'integration_type': this.inteTypeSelected ? this.inteTypeSelected.map(x => x.value).join(",") : "",
            'theme': this.themeSelected ? this.themeSelected.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'ipnStatus': this.ipnStatusSelected ? this.ipnStatusSelected.map(x => x.value).join(",") : "",
            'page_size': 100,
            'page': (this.searchForm.page - 1) + '',
            'file_name': this.searchForm.typeFilter.length == 0 ? 'equals' : this.searchForm.typeFilter.toString().replace(/,/g, ''),
            'column_list': column_list.join(","),
            'column_active': column_active.join(","),
            'selectedTrans': selectedSkey,
            'reply': this.reply ? this.reply.trim() : "",
            'replyMessage': this.replyMessage ? this.replyMessage.trim() : "",
            'reasonCode': this.reasonCode ? this.reasonCode.trim() : "",
            'authenticationId': this.authenticationId ? this.authenticationId.trim() : "",
            'networkTransId': this.networkTransId ? this.networkTransId.trim() : "",
            'requestId': this.requestId ? this.requestId.trim() : "",
            'cardVerificationInfo': this.cardVerificationInfo ? this.cardVerificationInfo.trim() : "",
            'providerMessage': this.providerMessage ? this.providerMessage.trim() : ""
        };
        return this.ssTranService.downloadFile(paramsDownload).subscribe(response => {
            this.global.downloadEmit.emit(true);
        });
    }

    downloadPayment() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        // let keys = Array.from( myMap.keys() );
        this.validateFilterColumn();
        var paramsDownload = {
            'from_date': from_date,
            'to_date': to_date,
            'gate': this.paygateSelected ? this.paygateSelected.map(x => x.value).join(",") : "",
            // 'installment' : pay_gate.includes('Installment')? "1" : "",
            // 'promotion' : pay_gate.includes('PR')? "1" : "",
            // 'partner_name': this.partnerName ? this.partnerName.trim() : "",
            'partner_name': this.partnerSelected ? this.partnerSelected.map(x => x.value).join(",") : "",
            'referralPartner': this.referralPartnerSelected ? this.referralPartnerSelected.map(x => x.value).join(",") : "",
            'acquirerId': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(",") : "",
            'merchantId': this.merchantId ? this.merchantId.trim() : "",
            'contractType': this.contractTypeSelected ? this.contractTypeSelected.map(x => x.value).join(",") : "",
            'invoiceId': this.invoiceId ? this.invoiceId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'paymentId': this.paymentId ? this.paymentId.trim() : "",
            'quicklinkId': this.quicklinkId ? this.quicklinkId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchantTransactionRef': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'bankTransId': this.bankTransId ? this.bankTransId.trim() : "",
            'qr_id': this.qrId ? this.qrId : "",
            'qr_channel': this.qrChannelSelected ? this.qrChannelSelected.map(x => x.value).join(",") : "",
            'card_type': this.cardSelected ? this.cardSelected.map(x => x.value).join(",") : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'source': this.sourceSelected ? this.sourceSelected.map(x => x.value).join(",") : "",
            // 'bin_country': this.binCountrySelected ? this.binCountrySelected.map(x => x.value).join(",") : "",
            'bin_country': (this.binCountrySelected && this.binCountrySelected.length <= 10) ? this.binCountrySelected.map(x => x.value).join(',') : (this.binCountrySelected && this.binCountrySelected.length === this.binCountryList.length) ? '' : this.binCountrySelected ? this.binCountrySelected.slice(0, 10).map(x => x.value).join(',') : '',
            'issue': this.issuerSelected ? this.issuerSelected.map(x => x.value).join(",") : "",
            'installment_bank': this.installmentBankSelected ? this.installmentBankSelected.map(x => x.value).join(",") : "",
            'promotion_code': this.promotionCode ? this.promotionCode.trim() : "",
            'promotion_name': this.promotionName ? this.promotionName.trim() : "",
            'original_amount': this.originalAmount ? this.originalAmount : 0,
            'trans_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'authentication_state': this.authenStateSelected ? this.authenStateSelected.map(x => x.value).join(",") : "",
            'response_code': this.responseCodeSelected ? this.responseCodeSelected.map(x => x.value).join(",") : "",
            'invoice_state': this.invoiceStateSelected ? this.invoiceStateSelected.map(x => x.value).join(",") : "",
            'trans_state': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'authorization_code': this.authoCode ? this.authoCode.trim() : "",
            'installment_status': this.installmentStatusSelected ? this.installmentStatusSelected.map(x => x.value).join(",") : "",
            'installment_period': this.installmentPeriodSelected ? this.installmentPeriodSelected.map(x => x.value).join(",") : "",
            'integration_type': this.inteTypeSelected ? this.inteTypeSelected.map(x => x.value).join(",") : "",
            'theme': this.themeSelected ? this.themeSelected.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'ipnStatus': this.ipnStatusSelected ? this.ipnStatusSelected.map(x => x.value).join(",") : "",
            'page_size': 100,
            'page': (this.searchForm.page - 1) + '',
            'file_name': this.searchForm.typeFilter.length == 0 ? 'equals' : this.searchForm.typeFilter.toString().replace(/,/g, ''),
            'column_list': this.searchForm.columnItems.map(element => element.code).join(","),
            'column_active': Array.from(this.searchForm.columnMap.keys()).join(","),
            'reply': this.reply ? this.reply.trim() : "",
            'replyMessage': this.replyMessage ? this.replyMessage.trim() : "",
            'reasonCode': this.reasonCode ? this.reasonCode.trim() : "",
            'authenticationId': this.authenticationId ? this.authenticationId.trim() : "",
            'networkTransId': this.networkTransId ? this.networkTransId.trim() : "",
            'requestId': this.requestId ? this.requestId.trim() : "",
            'cardVerificationInfo': this.cardVerificationInfo ? this.cardVerificationInfo.trim() : "",
            'providerMessage': this.providerMessage ? this.providerMessage.trim() : ""
        };
        return this.ssTranService.downloadFilePayment(paramsDownload).subscribe(response => {
            this.global.downloadEmit.emit(true);
        });
    }

    initDataSearchTable(param?: any) {
        this.paygateSelected = (param['gate'] !== '' && param['gate'] !== undefined) ? this.gateList.filter(col => param['gate'].includes(col.value)) : [];
        // this.paygateSelected = (param['gate'] !== '' && param['gate'] !== undefined) ?  param['gate'] : [];
        // this.partnerName = param['partner_name'] === undefined ? '' : param['partner_name'];
        this.partnerSelected = (param['partnerSelected'] !== '' && param['partnerSelected'] !== undefined) ? this.partnerList.filter(col => param['partnerSelected'].split(',').includes(col.value)) : [];
        this.referralPartnerSelected = (param['referralPartner'] !== '' && param['referralPartner'] !== undefined) ? this.referralPartnerList.filter(col => param['referralPartner'].split(',').includes(col.value)) : [];
        this.acquirerSelected = (param['acquirerId'] !== '' && param['acquirerId'] !== undefined) ? this.acquirerList.filter(col => param['acquirerId'].split(',').includes(col.value)) : [];
        this.contractTypeSelected = (param['contractType'] !== '' && param['contractType'] !== undefined) ? this.contractTypeList.filter(col => param['contractType'].split(',').includes(col.value)) : [];
        this.merchantId = param['merchantId'] === undefined ? '' : param['merchantId'];
        this.invoiceId = param['invoiceId'] === undefined ? '' : param['invoiceId'];
        this.transId = param['transaction_id'] === undefined ? '' : param['transaction_id'];
        this.paymentId = param['paymentId'] === undefined ? '' : param['paymentId'];
        this.quicklinkId = param['quicklinkId'] === undefined ? '' : param['quicklinkId'];
        this.orderRef = param['order_ref'] === undefined ? '' : param['order_ref'];
        this.merchantTransRef = param['merchantTransactionRef'] === undefined ? '' : param['merchantTransactionRef'];
        this.bankTransId = param['bankTransId'] === undefined ? '' : param['bankTransId'];
        this.qrId = param['qr_id'] === undefined ? '' : param['qr_id'];
        this.qrChannelSelected = (param['qr_channel'] !== '' && param['qr_channel'] !== undefined) ? this.qrChannelList.filter(col => param['qr_channel'].split(',').includes(col.value)) : [];
        // this.cardSelected = (param['card_type'] !== '' && param['card_type'] !== undefined) ? this.cardList.filter(col => param['card_type'].split(',').includes(col.value)) : [];
        let flatCardList = [];
        this.cardList.forEach(x => flatCardList = flatCardList.concat(x.items));
        this.cardSelected = (param['card_type'] !== '' && param['card_type'] !== undefined) ? flatCardList.filter(x => param['card_type'].split(',').includes(x.value)) : [];
        this.cardNumber = param['card_number'] === undefined ? '' : param['card_number'];
        this.sourceSelected = (param['source'] !== '' && param['source'] !== undefined) ? this.sourceList.filter(col => param['source'].split(',').includes(col.value)) : [];
        this.binCountrySelected = (param['bin_country'] !== '' && param['bin_country'] !== undefined) ? this.binCountryList.filter(col => param['bin_country'].split(',').includes(col.value)) : [];
        this.issuerSelected = (param['issue'] !== '' && param['issue'] !== undefined) ? this.issuerList.filter(col => param['issue'].split(',').includes(col.value)) : [];
        this.installmentBankSelected = (param['installment_bank'] !== '' && param['installment_bank'] !== undefined) ? this.installmentBankList.filter(col => param['installment_bank'].split(',').includes(col.value)) : [];
        this.promotionCode = param['promotion_code'] === undefined ? '' : param['promotion_code'];
        this.promotionName = param['promotion_name'] === undefined ? '' : param['promotion_name'];
        this.originalAmount = param['original_amount'] === undefined ? 0 : param['original_amount'];
        this.transCurrency = (param['trans_currency'] !== '' && param['trans_currency'] !== undefined) ? this.transCurrencyList.filter(col => param['trans_currency'].split(',').includes(col.value)) : [];
        this.transTypeSelected = (param['transaction_type'] !== '' && param['transaction_type'] !== undefined) ? this.transTypeList.filter(col => param['transaction_type'].split(',').includes(col.value)) : [];
        this.authenStateSelected = (param['authentication_state'] !== '' && param['authentication_state'] !== undefined) ? this.authenStateList.filter(col => param['authentication_state'].split(',').includes(col.value)) : [];
        this.responseCodeSelected = (param['response_code'] !== '' && param['response_code'] !== undefined) ? this.responseCodeList.filter(col => param['response_code'].split(',').includes(col.value)) : [];
        this.invoiceStateSelected = (param['invoice_state'] !== '' && param['invoice_state'] !== undefined) ? this.invoiceStateList.filter(col => param['invoice_state'].split(',').includes(col.value)) : [];
        this.transStateSelected = (param['trans_state'] !== '' && param['trans_state'] !== undefined) ? this.transStateList.filter(col => param['trans_state'].split(',').includes(col.value)) : [];
        this.authoCode = param['authorization_code'] === undefined ? '' : param['authorization_code'];
        this.installmentStatusSelected = (param['installment_status'] !== '' && param['installment_status'] !== undefined) ? this.installmentStatusList.filter(col => param['installment_status'].split(',').includes(col.value)) : [];
        this.installmentPeriodSelected = (param['installment_period'] !== '' && param['installment_period'] !== undefined) ? this.installmentPeriodList.filter(col => param['installment_period'].split(',').includes(col.value)) : [];
        this.inteTypeSelected = (param['integration_type'] !== '' && param['integration_type'] !== undefined) ? this.inteTypeList.filter(col => param['integration_type'].split(',').includes(col.value)) : [];
        this.themeSelected = (param['theme'] !== '' && param['theme'] !== undefined) ? this.themeList.filter(col => param['theme'].split(',').includes(col.value)) : [];
        this.merchantChannelSelected = (param['merchantChannel'] !== '' && param['merchantChannel'] !== undefined) ? this.merchantChannelList.filter(col => param['merchantChannel'].split(',').includes(col.value)) : [];
        this.ipnStatusSelected = (param['ipnStatus'] !== '' && param['ipnStatus'] !== undefined) ? this.ipnStatusList.filter(col => param['ipnStatus'].split(',').includes(col.value)) : [];
        this.reply = param['reply'] === undefined ? '' : param['reply'];
        this.replyMessage = param['replyMessage'] === undefined ? '' : param['replyMessage'];
        this.reasonCode = param['reasonCode'] === undefined ? '' : param['reasonCode'];
        this.authenticationId = param['authenticationId'] === undefined ? '' : param['authenticationId'];
        this.networkTransId = param['networkTransId'] === undefined ? '' : param['networkTransId'];
        this.requestId = param['requestId'] === undefined ? '' : param['requestId'];
        this.cardVerificationInfo = param['cardVerificationInfo'] === undefined ? '' : param['cardVerificationInfo'];
        this.providerMessage = param['providerMessage'] === undefined ? '' : param['providerMessage'];

        this.filterMerchantChanelControler(this.merchantChannelSelected);
        this.filterGateControler(this.paygateSelected);
    }

    redirectParams() {
        const paramSearch = this.searchForm.redirectParams();
        const params = {
            'fromDate': this.searchForm.fromDate,
            'toDate': this.searchForm.toDate,
            'gate': this.paygateSelected ? this.paygateSelected.map(x => x.value).join(",") : "",
            // 'partner_name': this.partnerName ? this.partnerName.trim() : "",
            'partnerSelected' :  this.partnerSelected ? this.partnerSelected.map(x => x.value).join(",") : "",
            'referralPartner': this.referralPartnerSelected ? this.referralPartnerSelected.map(x => x.value).join(",") : "",
            'acquirerId': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(",") : "",
            'merchantId': this.merchantId ? this.merchantId.trim() : "",
            'contractType': this.contractTypeSelected ? this.contractTypeSelected.map(x => x.value).join(",") : "",
            'invoiceId': this.invoiceId ? this.invoiceId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'paymentId': this.paymentId ? this.paymentId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'quicklinkId': this.quicklinkId ? this.quicklinkId.trim() : "",
            'merchantTransactionRef': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'bankTransId': this.bankTransId ? this.bankTransId.trim() : "",
            'qr_id': this.qrId ? this.qrId : "",
            'qr_channel': this.qrChannelSelected ? this.qrChannelSelected.map(x => x.value).join(",") : "",
            'card_type': this.cardSelected ? this.cardSelected.map(x => x.value).join(",") : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'source': this.sourceSelected ? this.sourceSelected.map(x => x.value).join(",") : "",
            'bin_country': this.binCountrySelected ? this.binCountrySelected.map(x => x.value).join(",") : "",
            'issue': this.issuerSelected ? this.issuerSelected.map(x => x.value).join(",") : "",
            'installment_bank': this.installmentBankSelected ? this.installmentBankSelected.map(x => x.value).join(",") : "",
            'promotion_code': this.promotionCode ? this.promotionCode.trim() : "",
            'promotion_name': this.promotionName ? this.promotionName.trim() : "",
            'original_amount': this.originalAmount ? this.promotionName : 0,
            'trans_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'authentication_state': this.authenStateSelected ? this.authenStateSelected.map(x => x.value).join(",") : "",
            'response_code': this.responseCodeSelected ? this.responseCodeSelected.map(x => x.value).join(",") : "",
            'invoice_state': this.invoiceStateSelected ? this.invoiceStateSelected.map(x => x.value).join(",") : "",
            'trans_state': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'authorization_code': this.authoCode ? this.authoCode.trim() : "",
            'installment_status': this.installmentStatusSelected ? this.installmentStatusSelected.map(x => x.value).join(",") : "",
            'installment_period': this.installmentPeriodSelected ? this.installmentPeriodSelected.map(x => x.value).join(",") : "",
            'integration_type': this.inteTypeSelected ? this.inteTypeSelected.map(x => x.value).join(",") : "",
            'theme': this.themeSelected ? this.themeSelected.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'ipnStatus': this.ipnStatusSelected ? this.ipnStatusSelected.map(x => x.value).join(",") : "",
            // 'page': this.searchForm.page + '',
            'typeFilter': this.searchForm.typeFilter,
            'page_size': paramSearch.page_size,
            'page': paramSearch.page,
            'first': paramSearch.first,
            // 'columnItem': paramSearch.columnItem,
            'order_reference': paramSearch.order_reference,
            'firstSearch': false,
            'reply': this.reply ? this.reply.trim() : "",
            'replyMessage': this.replyMessage ? this.replyMessage.trim() : "",
            'reasonCode': this.reasonCode ? this.reasonCode.trim() : "",
            'authenticationId': this.authenticationId ? this.authenticationId.trim() : "",
            'networkTransId': this.networkTransId ? this.networkTransId.trim() : "",
            'requestId': this.requestId ? this.requestId.trim() : "",
            'cardVerificationInfo': this.cardVerificationInfo ? this.cardVerificationInfo.trim() : "",
            'providerMessage': this.providerMessage ? this.providerMessage.trim() : "",
        };
        return params;
    }

    validateFilterColumn() {
        if (this.binCountrySelected && this.binCountrySelected.length > 10 && this.binCountrySelected.length != this.binCountryList.length) {
            this.toastr.warning('Chọn tối đa 10 Bin Country', null);
        }

        for (let i = 0; i < this.cols.length; i++) {
            if (this.cols[i].active === false) {
                if (this.cols[i].code === 'gate') {
                    this.paygateSelected = [];
                }
                if (this.cols[i].code === 'partnerName') {
                    // this.partnerName = "";
                    this.partnerSelected = [];
                }
                if (this.cols[i].code === 'referralPartner') {
                    this.referralPartnerSelected = [];
                }
                if (this.cols[i].code === 'acquirer') {
                    this.acquirerSelected = [];
                }
                if (this.cols[i].code === 'invoiceId') {
                    this.invoiceId = "";
                }
                if (this.cols[i].code === 'qrId') {
                    this.qrId = "";
                }
                if (this.cols[i].code === 'qrChannel') {
                    this.qrChannelSelected = [];
                }
                if (this.cols[i].code === 'source') {
                    this.sourceSelected = [];
                }
                if (this.cols[i].code === 'binCountry') {
                    this.binCountrySelected = [];
                }
                if (this.cols[i].code === 'issuer') {
                    this.issuerSelected = [];
                }
                if (this.cols[i].code === 'installmentBank') {
                    this.installmentBankSelected = [];
                }
                if (this.cols[i].code === 'installmentPeriod') {
                    this.installmentPeriodSelected = [];
                }
                if (this.cols[i].code === 'promotionCode') {
                    this.promotionCode = "";
                }
                if (this.cols[i].code === 'promotionName') {
                    this.promotionName = "";
                }
                if (this.cols[i].code === 'authenState') {
                    this.authenStateSelected = [];
                }
                if (this.cols[i].code === 'authoCode') {
                    this.authoCode = '';
                }
                if (this.cols[i].code === 'installmentStatus') {
                    this.installmentStatusSelected = [];
                }
                if (this.cols[i].code === 'inteType') {
                    this.inteTypeSelected = [];
                }
                if (this.cols[i].code === 'theme') {
                    this.themeSelected = [];
                }
                if (this.cols[i].code === 'merchantChannel') {
                    this.merchantChannelSelected = [];
                }
                if (this.cols[i].code === 'ipnStatus') {
                    this.ipnStatusSelected = [];
                }
                if (this.cols[i].code === 'paymentId') {
                    this.paymentId = "";
                }
                if (this.cols[i].code === 'quicklinkId') {
                    this.quicklinkId = "";
                }
                if (this.cols[i].code === 'contractType') {
                    this.contractTypeSelected = [];
                }
                if (this.cols[i].code === 'bankTransId') {
                    this.bankTransId = "";
                }
            }
        }

    }

    onSubmit() {
        this.searchForm.first = 0;
        this.searchForm.page = 1;
        return this.searchData().subscribe(responses => {
            // this.resultsLength = responses.response.data.total;
            this.resultsLength = 0;
            this.data = responses.response.data.list;
            this.initPage(this.resultsLength);
            // if(this.data && this.data.length > 0) this.innitFilterDropDown();
        });
    }

    onChangePage() {
        this.searchForm.page = this.searchForm.page == undefined ? 0 : this.searchForm.page;
        this.searchForm.first = this.searchForm.page == undefined ? 0 : ((this.searchForm.page - 1) * 100);
        return this.searchData().subscribe(responses => {
            // this.resultsLength = responses.response.data.total;
            this.data = responses.response.data.list;
            this.initPage(this.resultsLength);
        });
    }

    initPage(resultsLength: any) {
        if (resultsLength && resultsLength > 100) {
            let numberPage = Math.ceil(resultsLength / 100);
            this.page = [];
            for (var i = 1; i <= numberPage; i++) {
                this.page.push({
                    value: i,
                    label: i + ''
                });
            }
        } else {
            this.page = [];
            this.page.push({
                value: 1,
                label: '1'
            });
        }
    }

    convertAcquirer(inputData: string, type: string): string {
      var outputData = '';
      // Client is MSB, display Mobile Banking / E-Wallet instead of QR
      // if (type !== 'Domestic') {
      //     outputData = inputData;
      // } else {
      if (inputData !== undefined && inputData !== '' && inputData !== null) {
            let acq = this.cardList.find(obj => {if(obj && obj.items )  {
                    obj.items.find(obj => obj.value.split(',').includes(inputData))
                }
            });
          if (acq !== undefined)
              outputData = acq.items.label;
          else
              outputData = '';
      }
      // }
      if (!outputData) {
          outputData = inputData;
      }
      return outputData;
    }

    loadLazy(event: LazyLoadEvent) {
        this.loading = true;
        this.searchForm.page = (event.first / event.rows) + 1;
        this.searchForm.first = event.first;
        let params = this.route.snapshot.queryParams
        if (params['order_reference']) {
            this.orderRef = params['order_reference'];
            this.searchForm.typeFilter = 'contains';
        }
        this.initDropbox().subscribe(a => {
            // const arrAcquires = a[0].acquirers;

            // this.cardList = a[0] instanceof Array ? a[0].map(m => {
            //     return { value: `${m.items[0].value.split('|').join(',')}`, label: m.items[0].label };
            // }) : [];
            var blank = [{
                label:'Blank',
                value:'blank'
            }];
            this.cardList = a[0];
            this.cardListQt = this.cardList[0];
            this.cardListNd = this.cardList[1];
            this.cardListQr = this.cardList[2];
            this.cardListBnpl = this.cardList[3];
            this.cardListCard = this.cardList[4];
            this.cardListVietQr = this.cardList[5];
            this.cardListDD= this.cardList[6];
            this.binCountryList = []
            const binCountryListTemp = a[1].list.map(m => {
                return { label: `${m.name}`, value: m.name };
            });
            this.binCountryListQt = [
                {
                    label:'International',
                    value:'QT',
                    items: binCountryListTemp
                }
            ];
            this.binCountryListCard = [
                {
                    label:'CARD',
                    value:'CARD',
                    items: binCountryListTemp
                }
            ]
            this.issuerList = a[2].list;
            this.issuerListQt = this.issuerList[0];
            this.issuerListNd = this.issuerList[1];
            this.issuerListQr = this.issuerList[2];
            this.issuerListCard = this.issuerList[3];
            // this.issuerList = a[2].list.map(m => {
            //     return { label: `${m.name}`, value: m.name };
            // });
            this.installmentBankList = [];
            const installmentBankListTemp = a[3].list instanceof Array ?
                a[3].list.sort((a, b) => {
                    let fa = a.name.toLowerCase(),
                        fb = b.name.toLowerCase();
                    if (fa < fb) {
                        return -1;
                    }
                    if (fa > fb) {
                        return 1;
                    }
                    return 0;
                })
                    .map(m => {
                        return { label: `${m.name}`, value: m.name };
                    }) : [];
            this.installmentBankListQt = installmentBankListTemp
            // [
            //     {
            //         label: '',
            //         value: '',
            //         items: installmentBankListTemp
            //     }
            // ];

            this.themeList = a[4].list.map(m => {
                return { label: `${m.name}`, value: m.value };
            });

            this.responseCodeList = [];
            const responseCodes = a[5];
            responseCodes.sort((a, b) => {
                // Then compare by value
                const aNum = Number(a.value);
                const bNum = Number(b.value);
                const aIsNum = !isNaN(aNum);
                const bIsNum = !isNaN(bNum);

                if (aIsNum && bIsNum) {
                    // both numeric
                    return aNum - bNum;
                } else if (aIsNum && !bIsNum) {
                    // numeric comes before non-numeric
                    return -1;
                } else if (!aIsNum && bIsNum) {
                    return 1;
                } else {
                    // both non-numeric, compare lexicographically
                    return a.value.localeCompare(b.value);
                }
            });

            this.responseCodeListQt =
            [
                {
                    label: 'International',
                    value: 'QT',
                    items: blank.concat(responseCodes.filter(s => s.s_gate == 'QT'))
                }
            ];
            this.responseCodeListNd =
            [
                {
                    label: 'Domestic',
                    value: 'ND',
                    items: blank.concat(responseCodes.filter(s => s.s_gate == 'ND'))
                }
            ];
            this.responseCodeListQr =
            [
                {
                    label: 'Mobile App',
                    value: 'QR',
                    items: blank.concat(responseCodes.filter(s => s.s_gate == 'QR'))
                }
            ];
            this.responseCodeListBnpl =
            [
                {
                    label: 'BNPL',
                    value: 'BNPL',
                    items: blank.concat(responseCodes.filter(s => s.s_gate == 'BNPL'))
                }
            ];
            this.responseCodeListDD = 
            [
                {
                    label: 'Direct Debit',
                    value: 'DD',
                    items: blank.concat(responseCodes.filter(s => s.s_gate == 'DD'))
                }
            ];

            this.responseCodeListCard=
                [
                    {
                        label: 'Upos Card',
                        value: 'CARD',
                        items: blank.concat(a[5].filter(s => s.s_gate=='CARD'))
                    }
                ];

            this.contractTypeList = [
                {
                    label: '2B',
                    value: '2B'
                },
                {
                    label: '3B',
                    value: '3B'
                }
            ];

            this.partnerList = a[6].listPartner.map(m => {
                return { label: `${m.shortName}`, value: m.partnerId };
            });
            this.initDataSearchTable(params);
            return this.searchData() == null ? "" : this.searchData().subscribe(responses => {
                this.loading = false;
                // this.resultsLength = responses.response.data.total;
                this.resultsLength = 0;
                this.data = responses.response.data.list;
                this.initPage(this.resultsLength);
            });
        });
        // this.searchForm.init(params);
        // this.resultsLength = 0;
    }

    getPage(): number {
        return Math.floor(this.searchForm.first / this.pageSize);
    }

    getPageCount() {
        return Math.ceil(this.resultsLength / this.pageSize) || 1;
    }

    isFirstPage() {
        return this.getPage() === 0;
    }

    isLastPage() {
        return this.getPage() === this.getPageCount() - 1;
    }
    checkColumn(code: string): boolean {

        var check = false;
        // if (this.searchForm.columnMap && Array.from(this.searchForm.columnMap.keys()).length > 0) {
        //     return this.searchForm.columnMap.get(code);
        // }
        this.searchForm.columnItems.forEach(e => {
            if (e.code === code && e.active === true) {
                check = true;
            }
        });
        return check;
    }

    checkDuplicateList(checkArray: any, checkExists: boolean, value: any) {
        checkExists = false;
        if (checkArray.length > 0) {
            checkArray.forEach(dropdown => {
                if (dropdown.value === value) {
                    checkExists = true;
                    return checkExists;
                }
            });
        }

        return checkExists;
    }

    checkFormSearch(type: boolean) {
        this.showFormSearch = type;
    }

    initDropbox() {
        this.gateListEcom = [
            {
                label: 'Ecom',
                value: 'ecom',
                items: [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'International',
                        value: 'QT'
                    },
                    {
                        label: 'Domestic',
                        value: 'ND'
                    },
                    {
                        label: 'Mobile App',
                        value: 'QR'
                    },
                    {
                        label: 'BNPL',
                        value: 'BNPL'
                    },
                    {
                        label: 'VietQR',
                        value: 'VietQR'
                    },
                    {
                        label: 'Direct Debit',
                        value: 'DD'
                    }
                ]
            }
        ];
        this.gateListUpos = [
            {
                label: 'UPOS',
                value: 'upos',
                items: [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'CARD',
                        value: 'CARD'
                    },
                    {
                        label: 'Mobile App',
                        value: 'QR'
                    },
                    {
                        label: 'VietQR',
                        value: 'VietQR'
                    }
                ]
            }
        ];
        this.referralPartnerList = [
            {
                label: '2c2p',
                value: '2c2p'
            },
            {
                label: 'Enetviet',
                value: 'enetviet'
            },
            {
                label: 'Baocongnghe',
                value: 'baocongnghe'
            },
            {
                label: 'Shopify V2',
                value: 'Shopify V2'
            },
            {
                label: 'Direct Debit',
                value: 'DB'
            },
            {
                label: 'KOVENA',
                value: 'kovena'
            },
            {
                label: 'BAOKIM',
                value: 'BAOKIM'
            }
        ];
        this.contractTypeList = [
            {
                label: '2B',
                value: '2B'
            },
            {
                label: '3B',
                value: '3B'
            }
        ];
        this.contractTypeList = [
            {
                label: "Direct Debit",
                value: "DD",
                items: 
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Direct',
                        value: 'Direct'
                    }
                ]
            }
        ];

        this.internationalService.getListAcquirer().subscribe(data => {
            const acquirers = data ? data.map(m => ({
                label: `${m.acquirerName}`,
                value: `${m.acquirerId}`
            })) : [];
            this.acquirerListQt = [
                {
                    label: 'International',
                    value: 'qt',
                    items: acquirers
                }
            ];
        });
        
        this.acquirerListNd = [
            {
                label: 'Domestic',
                value: 'nd',
                items:
                [

                    {
                        label: 'OnePay',
                        value: 'OnePay'
                    },
                    {
                        label: 'Napas',
                        value: 'Napas'
                    }
                ]
            }
        ];
        this.acquirerListQr = [
            {
                label: 'Mobile App',
                value: 'qr',
                items:
                [

                    {
                        label: 'OnePay',
                        value: 'Onepay'
                    },
                    {
                        label: 'VietinBank',
                        value: 'Vietinbank'
                    },
                    {
                        label: 'MSB',
                        value: 'MSB'
                    },
                    {
                        label: 'Vietcombank',
                        value: 'Vietcombank'
                    }
                ]
            }
        ];
        this.acquirerListCard = [
            {
                label: 'CARD',
                value: 'CARD',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'KBank',
                        value: 'KBank'
                    }
                ]
            }
        ];

        this.acquirerListVietQr = [
            {
                label: 'VietQR',
                value: 'VietQR',
                items:
                [
                    {
                        label: 'BIDV',
                        value: 'BIDV'
                    },
                    {
                        label: 'VPBANK',
                        value: 'VPBANK'
                    },
                    {
                        label: 'VIETCOMBANK',
                        value: 'VIETCOMBANK'
                    },
                    {
                        label: 'MSB',
                        value: 'MSB'
                    }
                ]
            }
        ];

        // this.qrChannelList = [
            // {
            //     label: 'VietinBank',
            //     value: 'VIETINQR'
            // },
            // {
            //     label: 'MSB',
            //     value: 'MSBQR'
            // },
            // {
            //     label: 'mPayvn',
            //     value: 'DSP'
            // },
            // {
            //     label: 'BIDV Pay+',
            //     value: 'BIDV'
            // },
            // {
            //     label: 'MyVIB',
            //     value: 'VIB'
            // },
            // {
            //     label: 'MoMo',
            //     value: 'MOMO'
            // },
            // {
            //     label: 'ZaloPay',
            //     value: 'ZALOPAY'
            // },
            // {
            //     label: 'SmartPay',
            //     value: 'SMARTPAY'
            // },
            // {
            //     label: 'ShopeePay',
            //     value: 'SHOPEEPAY'
            // },
            // {
            //     label: 'UnionPay',
            //     value: 'UNIONPAY'
            // },
            // {
            //     label: 'GrabPay',
            //     value: 'GRABPAY'
            // },
            // {
            //     label: 'Upos Card',
            //     value: 'CARD'
            // },
            // {
            //     label: 'Upos Installment',
            //     value: 'INSTALLMENT'
            // },
            // {
            //     label: 'Upos QR',
            //     value: 'QR'
            // },
            // {
            //     label: 'Promotion',
            //     value: 'PR'
            // }
        // ];
        this.channelListQt = [
            {
                label:'International',
                value:'QT',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Promotion',
                        value: 'PR'
                    },
                    {
                        label: 'Installment',
                        value: 'Installment'
                    }
                ]
            }
        ];
        this.channelListNd = [
            {
                label:'Domestic',
                value:'ND',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Promotion',
                        value: 'PR'
                    }
                ]
            }
        ];
        this.channelListQr = [
            {
                label:'Mobile App',
                value:'qr',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Promotion',
                        value: 'PR'
                    }
                ]
            }
        ];
        this.channelListBnpl = [
            {
                label:'BNPL',
                value:'BNPL',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Promotion',
                        value: 'PR'
                    }
                ]
            }
        ];
        this.channelListCard = [
            {
                label:'CARD',
                value:'CARD',
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Installment',
                        value: 'Installment'
                    }
                ]
            }
        ];
        this.installmentPeriodList = []
        this.installmentPeriodListQt = [
            {
                label: 'International',
                value: 'QT',
                items:
                [
                    {
                        label: '3 months',
                        value: '3'
                    },
                    {
                        label: '6 months',
                        value: '6'
                    },
                    {
                        label: '9 months',
                        value: '9'
                    },
                    {
                        label: '12 months',
                        value: '12'
                    },
                    {
                        label: '15 months',
                        value: '15'
                    },
                    {
                        label: '24 months',
                        value: '24'
                    },
                    {
                        label: '36 months',
                        value: '36'
                    }
                ]
            }
        ];
        this.installmentPeriodListCard = [
            {
                label: 'CARD',
                value: 'CARD',
                items:
                [
                    {
                        label: '3 months',
                        value: '3'
                    },
                    {
                        label: '6 months',
                        value: '6'
                    },
                    {
                        label: '9 months',
                        value: '9'
                    },
                    {
                        label: '12 months',
                        value: '12'
                    },
                    {
                        label: '15 months',
                        value: '15'
                    },
                    {
                        label: '24 months',
                        value: '24'
                    },
                    {
                        label: '36 months',
                        value: '36'
                    }
                ]
            }
        ];
        this.transTypeList = [];

        this.transTypeListBlank = [
            {
                label: "Blank",
                value: "blank",
                items:
                [
                    {
                        label: 'Authorize',
                        value: 'Authorize'
                    },
                    {
                        label: 'Capture',
                        value: 'Capture'
                    },
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Refund Capture',
                        value: 'Refund capture'
                    },
                    {
                        label: 'Refund Dispute',
                        value: 'Refund Dispute'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    },
                    {
                        label: 'Void Authorize',
                        value: 'Void authorize'
                    },
                    // {
                    //     label: 'Void',
                    //     value: 'Void'
                    // },
                    {
                        label: 'Void Capture',
                        value: 'Void capture'
                    },
                    {
                        label: 'Void Purchase',
                        value: 'Void purchase'
                    },
                    {
                        label: 'Void Refund',
                        value: 'Void refund'
                    },
                    {
                        label: 'Void Refund Capture',
                        value: 'Void refund capture'
                    }
                ]
            }
        ];
        this.transTypeListQt = [
            {
                label: "International",
                value: "QT",
                items:
                [
                    {
                        label: 'Authorize',
                        value: 'Authorize'
                    },
                    {
                        label: 'Capture',
                        value: 'Capture'
                    },
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Refund Capture',
                        value: 'Refund capture'
                    },
                    {
                        label: 'Refund Dispute',
                        value: 'Refund Dispute'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    },
                    {
                        label: 'Void Authorize',
                        value: 'Void authorize'
                    },
                    // {
                    //     label: 'Void',
                    //     value: 'Void'
                    // },
                    {
                        label: 'Void Capture',
                        value: 'Void capture'
                    },
                    {
                        label: 'Void Purchase',
                        value: 'Void purchase'
                    },
                    {
                        label: 'Void Refund',
                        value: 'Void refund'
                    },
                    {
                        label: 'Void Refund Capture',
                        value: 'Void refund capture'
                    }
                ]
            }
        ];
        this.transTypeListNd = [
            {
                label: "Domestic",
                value: "ND",
                items:
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    },
                    {
                        label: 'Refund Dispute',
                        value: 'Refund Dispute'
                    },

                ]
            }
        ];

        this.transTypeListDD = [
            {
                label: "Direct Debit",
                value: "DD",
                items: 
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    }
                ]
            }
        ];
        this.transTypeListQr = [
            {
                label: "Mobile App",
                value: "QR",
                items:
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    },
                    {
                        label: 'Refund Dispute',
                        value: 'Refund Dispute'
                    }
                ]
            }
        ];
        this.transTypeListQr2 = [
            {
                label: "Mobile App",
                value: "QR",
                items:
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    }
                ]
            }
        ];
        this.transTypeListBnpl = [
            {
                label: "BNPL",
                value: "BNPL",
                items:
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    }
                ]
            }
        ];
        this.transTypeListCard = [
            {
                label: 'CARD',
                value: 'CARD',
                items:
                [
                    {
                        label: 'Purchase',
                        value: 'Purchase'
                    },
                    {
                        label: 'Refund',
                        value: 'Refund'
                    },
                    {
                        label: 'Request Refund',
                        value: 'Request Refund'
                    },
                    {
                        label: 'Void Purchase',
                        value: 'Void purchase'
                    }
                ]
            }
        ];
        this.transCurrencyList = []
        this.transCurrencyListQt = [
            // {
            //     label: "International",
            //     value: "QT",
            //     items:
            //     [
                    {
                        label: 'VND',
                        value: 'VND'
                    },
                    {
                        label: 'USD',
                        value: 'USD'
                    },
                    {
                        value: 'THB',
                        label: 'THB'
                    },
                    {
                        value: 'SGD',
                        label: 'SGD'
                    },
                    {
                        value: 'MYR',
                        label: 'MYR'
                    },
                    {
                        value: 'IDR',
                        label: 'IDR'
                    },
                    {
                        value: 'JPY',
                        label: 'JPY'
                    },
                    {
                        value: 'KRW',
                        label: 'KRW'
                    },
                    {
                        value: 'TWD',
                        label: 'TWD'
                    },
                    {
                        value: 'CNY',
                        label: 'CNY'
                    }
            //     ]
            // }
        ];
        this.authenStateList = [];

        this.authenStateListQt = [
            {
                label: "International",
                value: "QT",
                items:
                [
                    {
                        label: 'Y - Cardholder Verified',
                        value: 'Y'
                    },
                    {
                        label: 'M - Verification Attempt',
                        value: 'M'
                    },
                    {
                        label: 'E - CSC not match/ Card limit exceeded',
                        value: 'E'
                    },
                    {
                        label: 'N - Cardholder Not Verified',
                        value: 'N'
                    },
                    {
                        label: 'T - ACS Timeout',
                        value: 'T'
                    },
                    {
                        label: 'A - Authentication Failed',
                        value: 'A'
                    },
                    {
                        label: 'P - Parse Error',
                        value: 'P'
                    },
                    {
                        label: 'U - Undetermined',
                        value: 'U'
                    },
                    {
                        label: 'S - Security Error',
                        value: 'S'
                    },
                    {
                        label: 'I - Internal Error',
                        value: 'I'
                    },
                    {
                        label: 'D - Directory Communication Error',
                        value: 'D'
                    }
                ]
            }
        ];
        this.invoiceStateList = [
            {
            label: 'Not Paid',
            value: 'not_paid'
            },
            {
                label: 'Paid',
                value: 'paid'
            },
            {
                label: 'Close',
                value: 'closed'
            },
            {
                label: 'Expired',
                value: 'expired'
            },
            {
                label: 'Failed',
                value: 'failed'
            },
            {
                label: 'Canceled',
                value: 'canceled'
            }
        ];
        this.transStateList = [
            {
                label: 'Successful',
                value: 'Successful'
            },
            {
                label: 'Failed',
                value: 'Failed'
            },
            {
                label: 'Processing',
                value: 'Processing'
            },
            {
                label: 'Waiting for authentication',
                value: 'Waiting for authentication'
            },
            {
                label: 'Incomplete',
                value: 'Incomplete'
            },
            {
                label: 'Fraud',
                value: 'Fraud'
            },
            {
                label: 'Pending',
                value: 'Pending'
            },
            {
                label: 'Waiting for Approval',
                value: 'Waiting for approval'
            },
            {
                label: "Waiting for OnePay's Approval",
                value: "Waiting for OnePay's Approval,Waiting for onepays approval"
            },
            {
                label: "Waiting for verification",
                value: 'Waiting for verification'
            },
            {
                label: "Decline",
                value: 'Decline'
            },
            {
                label: "Verified",
                value: 'Verified'
            }
        ];
        this.installmentStatusList = [];
        this.installmentStatusListQt = [
            {
                label: "International",
                value: "QT",
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Waiting for Approval',
                        value: 'created'
                    },
                    {
                        label: 'Approved',
                        value: 'approved'
                    },
                    {
                        label: 'Void',
                        value: 'void'
                    },
                    {
                        label: 'Failed',
                        value: 'failed'
                    },
                    {
                        label: 'Sent',
                        value: 'sent'
                    }
                ]
            }
        ];
        this.installmentStatusListCard = [
            {
                label: 'CARD',
                value: 'CARD',
                items:
                [
                    {
                        label: 'Waiting for Approval',
                        value: 'created'
                    },
                    {
                        label: 'Approved',
                        value: 'approved'
                    },
                    {
                        label: 'Void',
                        value: 'void'
                    },
                    {
                        label: 'Failed',
                        value: 'failed'
                    },
                    {
                        label: 'Sent',
                        value: 'sent'
                    }
                ]
            }
        ];
        this.inteTypeList = [
            {
                label: 'Website / App',
                value: 'website_app'
            },
            {
                label: 'Invoice',
                value: 'invoice'
            },
            {
                label: 'Quicklink',
                value: 'Quicklink'
            },
            {
                label: 'Online Booking',
                value: 'online_booking'
            }
        ];
        this.merchantChannelList = [
            // {
            //     label: 'All',
            //     value: ''
            // },
            {
                label: 'Ecom',
                value: 'ecom'
            },
            {
                label: 'UPOS',
                value: 'upos'
            },
        ];
        this.sourceList = []
        this.sourceListQt = [
            {
                label: "International",
                value: "QT",
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Direct',
                        value: 'Direct'
                    },
                    {
                        label: 'Apple Pay',
                        value: 'Apple Pay'
                    },
                    {
                        label: 'Google Pay',
                        value: 'Google Pay'
                    },
                    {
                        label: 'Samsung Pay',
                        value: 'Samsung Pay'
                    },
                ]
            }
        ];
        this.sourceListDD = [
            {
                label: "Direct Debit",
                value: "DD",
                items: 
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Direct',
                        value: 'Direct'
                    }
                ]
            }
        ];
        this.sourceListNd = [
            {
                label: "Domestic",
                value: "ND",
                items:
                [
                    {
                        label: 'Blank',
                        value: 'blank'
                    },
                    {
                        label: 'Direct',
                        value: 'Direct'
                    },
                    {
                        label: 'Apple Pay',
                        value: 'Apple Pay'
                    }
                ]
            }
        ];

        this.ipnStatusList = [
            {
                label: 'Success',
                value: 'successful'
            },
            {
                label: 'Failed',
                value: 'failed'
            },
            {
                label: 'Pending',
                value: 'created'
            },
            {
                label: '-',
                value: 'undetermine'
            },
        ];
        // this.ssTranService.getDropdownThemeList().subscribe(data => {
        //     this.themeList = data.list.map(m => {
        //         return { label: `${m.name}`, value: m.value };
        //     });
        // });

        // this.ssTranService.getResponseCode().subscribe(data => {
        //     this.responseCodeList = data.map(s => {
        //         return { label: `${s.value} - ${s.label} - ${s.s_gate}`, value: s.value };
        //     });
        // });
        // this.ssTranService.getDropdownCardList().subscribe(data => {
        //     const arrAcquires = data.acquirers;
        //     this.cardList = arrAcquires instanceof Array ? arrAcquires.map(m => {
        //         return { value: `${m.acquirer_id.split('|').join(',')}`, label: m.acquirer_short_name };
        //     }) : [];
        // });
        // this.ssTranService.getDropdownBinCountry().subscribe(data => {
        //     this.binCountryList = data.list.map(m => {
        //         return { label: `${m.name}`, value: m.name };
        //     });
        // });
        // this.ssTranService.getDropdownIssuer().subscribe(data => {
        //     this.issuerList = data.list.map(m => {
        //         return { label: `${m.name}`, value: m.name };
        //     });
        // });
        // this.internationalService.getListInstallmentBank().subscribe(data => {

        //     this.installmentBankList = data.list instanceof Array ?
        //         data.list.sort((a, b) => {
        //             let fa = a.name.toLowerCase(),
        //                 fb = b.name.toLowerCase();
        //             if (fa < fb) {
        //                 return -1;
        //             }
        //             if (fa > fb) {
        //                 return 1;
        //             }
        //             return 0;
        //         })
        //             .map(m => {
        //                 return { label: `${m.name}`, value: m.name };
        //             }) : [];
        // });

        return Observable.zip(this.ssTranService.getDropdownCardListSS()
            , this.ssTranService.getDropdownBinCountry()
            , this.ssTranService.getDropdownIssuer()
            , this.internationalService.getListInstallmentBank()
            , this.ssTranService.getDropdownThemeList()
            , this.ssTranService.getResponseCode()
            , this.ssTranService.getListPartner());
    }

    createDispute() {
        let hasDispute = false;
        const transNoDispute = [];
        this.selectedTrans.forEach(trans => {
            if (trans.hasDispute) {
                hasDispute = true;
                trans.error = true;
            } else {
                transNoDispute.push(trans);
            }
        });

        if (transNoDispute?.length === 0) {
            // this.toastr.error('Disputes already exist.', 'Error');
            this.toastr.error('Disputes already exist.', 'Error', {
                disableTimeOut: true,
                closeButton: true,
                toastClass: 'button-close-error ngx-toastr interactive-toast-container',
            });
            return;
        }

        if (transNoDispute.length > 0) {
            this.selectedTrans = [...transNoDispute];
        }

        const operator = {
            'operatorId': this.global.activeProfile.n_id.toString(),
            'operatorName': this.global.activeProfile.name
        }

        const ref = this.dialogService.open(CreateDisputeModalComponent, {
            data: { list: this.selectedTrans },
            autoFocus: false,
            maxWidth: '90vw',
            maxHeight: '80vh',
        });

        ref.afterClosed().subscribe(result => {
            if (result && result.data) {
                const listDisputeData = result.data.map((item) => {
                    return {
                        'key': item.key,
                        'disputeCurrency': item.disputeCurrency,
                        'disputeAmount': item.disputeAmount,
                    }
                });
                const body = {
                    'operator': operator,
                    'department': result.department,
                    'listDisputeData': listDisputeData,
                }
                console.log(body);
                this.ssTranService.createDispute(body).subscribe(data => {
                    if (data && data.status == 'Successful') {
                        this.toastr.success('Successfully created dispute.', 'Successfully');
                        this.selectedTrans.forEach(trans => trans.hasDispute = true);
                        this.data.forEach(trans => trans.error = false);
                        this.selectedTrans = [];
                    }
                }, err => {
                    this.toastr.error(err.error?.message || 'Create Dispute error!', 'Error');
                });
            }
        });
    }

    isNumber(str: string): boolean {
        if (typeof str !== 'string') {
            return false;
        }
        if (str.trim() === '') {
            return false;
        }
        return !Number.isNaN(Number(str));
    }

    showNewDialog(key, trans_id) {
        console.log('key analysic ' + key)
        this.ssTranService.getAnalysisError(btoa(key)).subscribe(data => {
            const ref = this.dialogPrime.open(AnalysicDialogComponent, {
                data: {
                    id: trans_id,
                    analysis: data
                },
                width: '70%'
            });
        });
    }

    getElementInArray(array: Array<any>) {
        let element;
        if (array && array.length > 0) {
            element = array.map(i => i.label).join(",");
        }
        return element;
    }

    resetValue(data: any) {
        if (data == 'paygateSelected') {
            this.paygateSelected = [];
        } else if (data == 'partnerName') {
            // this.partnerName = undefined;
            this.partnerSelected = [];
        } else if (data == 'referralPartnerSelected') {
            this.referralPartnerSelected = undefined;
        } else if (data == 'acquirerSelected') {
            this.acquirerSelected = [];
        } else if (data == 'merchantId') {
            this.merchantId = undefined;
        } else if (data == 'invoiceId') {
            this.invoiceId = undefined;
        } else if (data == 'transId') {
            this.transId = undefined;
        } else if (data == 'paymentId') {
            this.paymentId = undefined;
        } else if (data == 'quicklinkId') {
            this.quicklinkId = undefined;
        }else if (data == 'orderRef') {
            this.orderRef = undefined;
        } else if (data == 'merchantTransRef') {
            this.merchantTransRef = undefined;
        } else if (data == 'qrId') {
            this.qrId = undefined;
        } else if (data == 'qrChannelSelected') {
            this.qrChannelSelected = [];
        } else if (data == 'cardSelected') {
            this.cardSelected = [];
        } else if (data == 'cardNumber') {
            this.cardNumber = undefined;
        } else if (data == 'sourceSelected') {
            this.sourceSelected = [];
        } else if (data == 'binCountrySelected') {
            this.binCountrySelected = [];
        } else if (data == 'issuerSelected') {
            this.issuerSelected = [];
        } else if (data == 'installmentBankSelected') {
            this.installmentBankSelected = [];
        } else if (data == 'installmentPeriodSelected') {
            this.installmentPeriodSelected = [];
        } else if (data == 'promotionCode') {
            this.promotionCode = undefined;
        } else if (data == 'promotionName') {
            this.promotionName = undefined;
        } else if (data == 'transCurrency') {
            this.transCurrency = [];
        } else if (data == 'transTypeSelected') {
            this.transTypeSelected = [];
        } else if (data == 'authenStateSelected') {
            this.authenStateSelected = [];
        } else if (data == 'responseCodeSelected') {
            this.responseCodeSelected = [];
        } else if (data == 'invoiceStateSelected') {
            this.invoiceStateSelected = [];
        } else if (data == 'transStateSelected') {
            this.transStateSelected = [];
        } else if (data == 'authoCode') {
            this.authoCode = undefined;
        } else if (data == 'installmentStatusSelected') {
            this.installmentStatusSelected = [];
        } else if (data == 'inteTypeSelected') {
            this.inteTypeSelected = [];
        } else if (data == 'themeSelected') {
            this.themeSelected = [];
        } else if (data == 'merchantChannelSelected') {
            this.merchantChannelSelected = [];
        } else if (data == 'ipnStatusSelected') {
            this.ipnStatusSelected = [];
        } else if (data == 'reply') {
            this.reply = undefined;
        } else if (data == 'replyMessage') {
            this.replyMessage = undefined;
        } else if (data == 'reasonCode') {
            this.reasonCode = undefined;
        } else if (data == 'authenticationId') {
            this.authenticationId = undefined;
        } else if (data == 'networkTransId') {
            this.networkTransId = undefined;
        } else if (data == 'requestId') {
            this.requestId = undefined;
        } else if (data == 'cardVerificationInfo') {
            this.cardVerificationInfo = undefined;
        } else if (data == 'providerMessage') {
            this.providerMessage = undefined;
        } else if (data == 'contractType') {
            this.contractTypeSelected = [];
        } else if (data == 'bankTransId') {
            this.bankTransId = undefined;
        }
        return this.filter();
    }

    getTotalPage(totalRecords: number, rows: number) {
        return Math.ceil(totalRecords / rows);
    }

    openColumnDisplay() {
        this.columnDisplayRef = this.dialogService2.open(ColumnDisplayComponent, {
            header: 'Column Display ',
            contentStyle: { "max-height": "90%", "width": "400px"},
            baseZIndex: 10000,
            data: {
                columnItems: this.searchForm.columnItems,
                // selectedCode: Array.from(this.searchForm.columnMap.keys())
            }
        });

        this.columnDisplayRef.onClose.subscribe(result => {
            // danh sach code
            if (result) {
                // this.searchForm.columnMap.clear();
                // result.forEach(a => this.searchForm.columnMap.set(a, this.searchForm.columnItemsMap.get(a)));
                // this.numberColumn = result.length;
                this.cols = result;
                this.localService.saveData('ss-trans-search-cols-01', this.cols);
                this.cols = this.cols.filter(item => item.active === true);
                this.cols.sort((a, b) => (a.order > b.order) ? 1 : -1);
                this.validateFilterColumn();
            }
        });
    }

    isRowSelected(sData) {
        if (!this.selectedTrans || this.selectedTrans.length === 0) {
            return false;
        }
        return this.selectedTrans.findIndex(data => data === sData) === -1 ? false : true;
    }

    getLabel(object: any): string {
        let label = '';
        if (object) {
            label = object.label;
        }
        return label;
    }

    getValue(object: any): string {
        let value = '';
        if (object) {
            value = object.value;
        }
        return value;
    }

    routerLinkRedirect(sKey, transType, gate, channel, merchantChannel, cardType){
    if (transType === 'Purchase' && merchantChannel === 'upos' && channel == 'Installment' ){
        this.openDialogDetail(sKey, UposInstallmentPurchaseDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-card-purchase',sKey],  { queryParams: this.redirectParams() });
    } else if (transType === 'Refund' && merchantChannel === 'upos' && channel == 'Installment' ){
        this.openDialogDetail(sKey, UposInstallmentRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-card-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Request Refund' && merchantChannel === 'upos' && channel == 'Installment' ){
        this.openDialogDetail(sKey, UposInstallmentRequestRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-card-request-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Void' && merchantChannel === 'upos' && channel == 'Installment' ){
        this.openDialogDetail(sKey, UposInstallmentVoidDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-card-void',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Purchase' && merchantChannel === 'upos' && gate === 'CARD' ){
        this.openDialogDetail(sKey, UposCardPurchaseDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-ita-purchase',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Refund' && merchantChannel === 'upos' && gate === 'CARD' ){
        this.openDialogDetail(sKey, UposCardRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-ita-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Request Refund' && merchantChannel === 'upos' && gate === 'CARD' ){
        this.openDialogDetail(sKey, UposCardRequestRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-ita-reqeust-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Void' && merchantChannel === 'upos' && gate === 'CARD' ){
        this.openDialogDetail(sKey, UposCardVoidDetailComponent);
        // return this.router.navigate(['/ss-trans-management/upos-ita-void',sKey], { queryParams: this.redirectParams() });
    } else if ((transType === 'Purchase' || transType === 'Authorize') && gate === 'International' ){
        this.openDialogDetail(sKey, TransactionQTPurchaseDetailComponent);
        // return this.router.navigate(['/ss-trans-management/international-purchase', sKey], { queryParams: this.redirectParams() });
    } else if ((transType === 'Refund' || transType === 'Refund Dispute' || transType === 'Capture'|| transType === 'Refund capture') && gate === 'International' ){
        this.openDialogDetail(sKey, TransactionQTRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/international-refund', sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Request Refund'  && gate === 'International' ){
        this.openDialogDetail(sKey, TransactionQTRequestRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/international -request-refund', sKey], { queryParams: this.redirectParams() });
    } else if ((transType === 'Void purchase' || transType === 'Void authorize' || transType === 'Void capture' || transType === 'Void refund capture' || transType === 'Void refund') && gate === 'International' ){
        this.openDialogDetail(sKey, TransactionQTVoidDetailComponent);
        // return this.router.navigate(['/ss-trans-management/international-void', sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Refund' && gate === 'Mobile App' ){
        this.openDialogDetail(sKey, TransactionQRRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/qr-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Request Refund' && gate === 'Mobile App' ){
        this.openDialogDetail(sKey, TransactionQRRequestRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/qr-request-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Purchase' && gate === 'Mobile App'){
        this.openDialogDetail(sKey, TransactionQRPurchaseDetailComponent);
        // return this.router.navigate(['/ss-trans-management/qr-purchase',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Purchase' && gate === 'Domestic'){
        this.openDialogDetail(sKey, TransactionPurchaseDomesticDetailComponent);
        // return this.router.navigate(['/ss-trans-management/domestic-purchase',sKey], { queryParams: this.redirectParams() });
    } else if ((transType === 'Refund' || transType === 'Refund Dispute') && gate === 'Domestic'){
        this.openDialogDetail(sKey, TransactionRefundDomesticDetailComponent);
        // return this.router.navigate(['/ss-trans-management/domestic-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Request Refund' && gate === 'Domestic'){
        this.openDialogDetail(sKey, TransactionNDRequestRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/domestic-request-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Purchase' && gate === 'DB'){
        this.openDialogDetail(sKey, DBTransactionPurchaseDetailComponent);
    } else if ((transType === 'Refund' || transType === 'Refund Dispute') && gate === 'DB'){
        this.openDialogDetail(sKey, DBTransactionRefundDetailComponent);
    } else if (transType === 'Request Refund' && gate === 'DB'){
        this.openDialogDetail(sKey, DBTransactionRequestRefundDetailComponent);
    // } else if (transType === 'Purchase' && ((gate === 'International' && channel === 'PR') || (gate === 'BNPL' && channel === 'PR'))){
    //     this.openDialogDetail(sKey, TransactionPurchasePromotionDetailComponent);
        // return this.router.navigate(['/ss-trans-management/promotion-purchase',sKey], { queryParams: this.redirectParams() });
    // } else if (transType === 'Refund' && ((gate === 'International' && channel === 'PR'))){
    //     this.openDialogDetail(sKey, TransactionPRRefundDetailComponent);
        // return this.router.navigate(['/ss-trans-management/pr-refund',sKey], { queryParams: this.redirectParams() });
    // } else if (transType === 'Request Refund' && ((gate === 'International' && channel === 'PR'))){
    //     this.openDialogDetail(sKey, TransactionRequestRefundPromotionDetailComponent);
        // return this.router.navigate(['/ss-trans-management/promotion-request-refund',sKey], { queryParams: this.redirectParams() });
    } else if (transType === 'Purchase' && (gate === 'VietQR')) {
        console.log('Refund Purchase')
        this.openDialogDetail(sKey, TransactionVietQRPurchaseDetailComponent);
    } else if (transType === 'Refund' && (gate === 'VietQR')) {
        console.log('Refund Detail')
        this.openDialogDetail(sKey, TransactionVietQRRefundDetailComponent);
    } else if (transType === 'Request Refund' && (gate === 'VietQR')) {
        this.openDialogDetail(sKey, TransactionVietQRRequestRefundDetailComponent);
    } else if (transType === 'Refund' && gate === 'Direct Debit' ){
        this.openDialogDetail(sKey, DBTransactionRefundDetailComponent);
    } else if (transType === 'Request Refund' && gate === 'Direct Debit' ){
        this.openDialogDetail(sKey, DBTransactionRequestRefundDetailComponent);
    } else if (transType === 'Purchase' && gate === 'Direct Debit' ){
        this.openDialogDetail(sKey, DBTransactionPurchaseDetailComponent);
    } else if (transType === 'Purchase' && gate === 'BNPL' && cardType === 'Amigo'){
      this.openDialogDetail(sKey, BNPLAmigoTransactionDetailComponent);
    } else if (transType === 'Void' && gate === 'BNPL' && cardType === 'Amigo' ){
      this.openDialogDetail(sKey, BNPLVoidDetailComponent);
    } else if (transType === 'Purchase' && gate === 'BNPL' && cardType != 'Amigo'){
        this.openDialogDetail(sKey, BNPLTransactionDetailComponent);
    } else if (transType === 'Request Refund' && gate === 'BNPL'){
        this.openDialogDetail(sKey, BNPLRequestRefundDetailComponent);
    } else if (transType === 'Refund' && gate === 'BNPL'){
        this.openDialogDetail(sKey, BNPLRefundDetailComponent);
    }
      return '';
    }

    openDialogDetail(sKey, componet) {
        const detailDialog = this.dialogPrime.open(componet, {
            contentStyle: { "max-height": "100%", "max-width": "100%", "overflow": "auto" ,'text-align': 'center' },
            data: {
                sKey: sKey,
                isPopup: true,
                queryParams: this.redirectParams()
            },
            width: '100%'
        });
        detailDialog.onClose.subscribe(() => {
            // Do something when the dialog is closed
          });
      }

      getIpnStatusTooltip(target){
        if(target.label === '-')
            return 'Cases included:\n \t - No IPN Config. \n \t - Active IPN Config, but not sent yet'
            return '';
    }

    handleDisputeCheck(e){
        if(e.target.checked) this.showDisputeCheck = true;
        else this.showDisputeCheck = false;
    }

    showTotalRecord(){
        return this.getTotal().subscribe(responses => {
            this.resultsLength = responses.response.data.total;
        });
    }
    filterMerchantChanelControler(merchantChannel){
        // if (merchantChannel == undefined) {
        //     merchantChannel = [{label: 'All',value:'all'}];
        // }
        this.gateList = [];
        // this.paygateSelected = [];
        if (merchantChannel && merchantChannel.length == 1 ){
            if ( merchantChannel[0].value == 'ecom'){
                this.gateList = this.gateListEcom;
                this.paygateSelected = this.paygateSelected.filter(item => (item.value == 'QT' || item.value == 'ND' || item.value == 'BNPL' || item.value == 'blank' || item.value == 'QR'));
            } else if (merchantChannel[0].value == 'upos'){
                this.gateList = this.gateListUpos;
                this.paygateSelected = this.paygateSelected.filter(item => (item.value == 'CARD' || item.value == 'QR' || item.value == 'blank'));
            }
        } else { //case all
            this.gateList = this.gateListEcom.concat(this.gateListUpos);
        }
        this.filterGateControler(this.paygateSelected);
    }

    filterGateControler(gate){
        this.onClearPaygate();
        this.acquirerList = [];
        this.qrChannelList = [];
        this.cardList = [];
        this.sourceList = [];
        this.binCountryList = [];
        this.installmentBankList = [];
        this.installmentPeriodList = [];
        this.authenStateList = [];
        this.installmentStatusList = [];
        this.issuerList = [];
        this.responseCodeList = [];
        this.transCurrencyList = [
            // {
            //     label:'Default',
            //     value:'default',
            //     items:
            //     [
                    {
                        label:'VND',
                        value:'VND',
                    }
            //     ]
            // }
        ];
        this.transTypeList = []
        const listBlank = [
            {
                label:'Blank',
                value:'blank',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    }
                ]
            }
        ]
        const listBlankDomestic = [
            {
                label:'Domestic',
                value:'domestic',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    }
                ]
            }
        ]
        const listBlankQr = [
            {
                label:'Mobile App',
                value:'qr',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    },
                    {
                        label:'MOMO PayLater',
                        value:'MOMO PayLater',
                    },
                    {
                        label:'MOMO Direct',
                        value:'MOMO Direct',
                    }
                ]
            }
        ]
        const listBlankBnpl = [
            {
                label:'BNPL',
                value:'bnpl',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    }
                ]
            }
        ]
        const listBlankCard = [
            {
                label:'CARD',
                value:'CARD',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    }
                ]
            }
        ]
        const listBlankVietQR = [
            {
                label:'VietQR',
                value:'VietQR',
                items:
                [
                    {
                        label:'Blank',
                        value:'blank',
                    }
                ]
            }
        ]

        // this.sourceList = this.sourceList.concat(listBlank);
        // this.binCountryList = this.binCountryList.concat(listBlank);
        this.installmentBankList = this.installmentBankList.concat(listBlank);
        this.installmentPeriodList = this.installmentPeriodList.concat(listBlank);
        this.authenStateList = this.authenStateList.concat(listBlank);
                // this.installmentStatusList = this.installmentStatusList.concat(this.installmentStatusListQt);
        // this.responseCodeList = this.responseCodeList.concat(listBlank);
        // this.installmentStatusList = this.installmentStatusList.concat(listBlank);
        // this.issuerList = this.issuerList.concat(listBlank);
        if(gate.length === 0){
            if (this.merchantChannelSelected.length == 1){
                if(this.merchantChannelSelected.find(x => x.value == 'ecom')){
                    this.acquirerList = this.acquirerList.concat(listBlank).concat(this.acquirerListQt).concat(this.acquirerListNd).concat(this.acquirerListQr);
                    this.qrChannelList = this.qrChannelList.concat(listBlank).concat(this.channelListQt).concat(this.channelListNd).concat(this.channelListQr).concat(this.channelListBnpl);
                    this.cardList = this.cardList.concat(listBlank).concat(this.cardListQt).concat(this.cardListNd).concat(this.cardListQr).concat(this.cardListBnpl).concat(this.cardListDD).concat(this.cardListVietQr);
                    this.sourceList = this.sourceList.concat(listBlank).concat(this.sourceListQt);
                    this.transTypeList = this.transTypeList.concat(this.transTypeListBlank).concat(this.transTypeListQt).concat(this.transTypeListNd).concat(this.transTypeListQr).concat(this.transTypeListBnpl);
                    this.responseCodeList=this.responseCodeList.concat(this.responseCodeListQt).concat(this.responseCodeListNd).concat(this.responseCodeListBnpl).concat(this.responseCodeListDD).concat(this.responseCodeListCard);
                    this.issuerList = this.issuerList.concat(listBlank).concat(this.issuerListQt).concat(this.issuerListNd).concat(this.issuerListQr);
                    this.binCountryList = this.binCountryList.concat(listBlank);
                    this.transCurrencyList = this.transCurrencyListQt;
                    this.installmentStatusList = this.installmentStatusList.concat(listBlank).concat(this.installmentStatusListQt);
                } else {
                    this.acquirerList = this.acquirerList.concat(listBlank).concat(this.acquirerListQr).concat(this.acquirerListCard);
                    this.qrChannelList = this.qrChannelList.concat(listBlank).concat(this.channelListQr).concat(this.channelListCard);
                    this.cardList = this.cardList.concat(listBlank).concat(this.cardListQr).concat(this.cardListCard).concat(this.cardListVietQr);
                    this.sourceList = this.sourceList.concat(listBlank);
                    this.transTypeList = this.transTypeList.concat(this.transTypeListBlank).concat(this.transTypeListQr).concat(this.transTypeListCard);
                    this.responseCodeList = this.responseCodeList.concat(listBlank);
                    this.issuerList = this.issuerList.concat(listBlank).concat(this.issuerListQr).concat(this.issuerListCard);
                    this.binCountryList = this.binCountryList.concat(listBlank);
                    this.transCurrencyList = this.transCurrencyListQt;
                    this.installmentStatusList = this.installmentStatusList.concat(listBlank).concat(this.installmentStatusListCard);
                }
            } else {
                this.acquirerList = this.acquirerList.concat(listBlank).concat(this.acquirerListQt).concat(this.acquirerListNd).concat(this.acquirerListQr).concat(this.acquirerListCard).concat(this.acquirerListVietQr);
                this.qrChannelList = this.qrChannelList.concat(listBlank).concat(this.channelListQt).concat(this.channelListNd).concat(this.channelListQr).concat(this.channelListBnpl).concat(this.channelListCard);
                this.cardList = this.cardList.concat(listBlank).concat(this.cardListQt).concat(this.cardListNd).concat(this.cardListQr).concat(this.cardListBnpl).concat(this.cardListCard).concat(this.cardListDD).concat(this.cardListVietQr);
                this.sourceList = this.sourceList.concat(listBlank).concat(this.sourceListQt);
                this.transTypeList = this.transTypeList.concat(this.transTypeListBlank).concat(this.transTypeListQt).concat(this.transTypeListNd).concat(this.transTypeListQr).concat(this.transTypeListBnpl).concat(this.transTypeListCard);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListQt).concat(this.responseCodeListNd).concat(this.responseCodeListBnpl).concat(this.responseCodeListDD).concat(this.responseCodeListCard);
                this.issuerList = this.issuerList.concat(listBlank).concat(this.issuerListQt).concat(this.issuerListNd).concat(this.issuerListQr).concat(this.issuerListCard);
                this.binCountryList = this.binCountryList.concat(listBlank);
                this.transCurrencyList = this.transCurrencyListQt;
                this.installmentStatusList = this.installmentStatusList.concat(listBlank).concat(this.installmentStatusListQt).concat(this.installmentStatusListCard);
            }
            this.installmentBankList = this.installmentBankListQt;
        } else {
            // this.issuerList = this.issuerList.concat(listBlank);
            this.installmentStatusList = this.installmentStatusList.concat(listBlank);
            if (gate.length === 1){
                this.issuerList = [];
                this.sourceList = [];
                this.responseCodeList = [];
                this.installmentStatusList = [];
                this.binCountryList=[];
                this.installmentPeriodList = [];
                this.cardList = [];
                this.qrChannelList = [];
                this.authenStateList = [];
            }
            if(gate.find(x => x.value == 'blank')){
                this.installmentPeriodList = [];
                this.installmentStatusList = [];
                this.binCountryList=[];
                this.issuerList = [];
                this.authenStateList = [];
                this.acquirerList = this.acquirerList.concat(listBlank);
                this.qrChannelList = this.qrChannelList.concat(listBlank);
                this.cardList = this.cardList.concat(listBlank);
                this.transTypeList = this.transTypeList.concat(this.transTypeListBlank);
                this.binCountryList = this.binCountryList.concat(listBlank);
                this.installmentStatusList = this.installmentStatusList.concat(listBlank).concat(this.installmentStatusListQt).concat(this.installmentStatusListCard);
                this.authenStateList = this.authenStateList.concat(listBlank);
                this.sourceList = this.sourceList.concat(listBlank);
                this.responseCodeList = this.responseCodeList.concat(listBlank);
                this.issuerList = this.issuerList.concat(listBlank);
                this.installmentPeriodList = this.installmentPeriodList.concat(listBlank);
                // this.transCurrencyList = this.transCurrencyListQt;

            }
            if(gate.find(x => x.value == 'QT')){
                this.acquirerList = this.acquirerList.concat(this.acquirerListQt);
                this.qrChannelList = this.qrChannelList.concat(this.channelListQt);
                this.cardList = this.cardList.concat(this.cardListQt);
                this.binCountryList = this.binCountryList.concat(this.binCountryListQt);
                this.installmentBankList = this.installmentBankListQt;
                this.installmentPeriodList = this.installmentPeriodList.concat(this.installmentPeriodListQt);
                this.transCurrencyList = this.transCurrencyListQt;
                this.transTypeList = this.transTypeList.concat(this.transTypeListQt);
                this.authenStateList = this.authenStateList.concat(this.authenStateListQt);
                this.installmentStatusList = this.installmentStatusList.concat(this.installmentStatusListQt);
                this.issuerList = this.issuerList.concat(this.issuerListQt);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListQt);
                this.sourceList = this.sourceList.concat(this.sourceListQt);
            }
            if(gate.find(x => x.value == 'ND')){
                this.acquirerList = this.acquirerList.concat(this.acquirerListNd);
                this.qrChannelList = this.qrChannelList.concat(this.channelListNd)
                this.cardList = this.cardList.concat(this.cardListNd);
                this.transTypeList = this.transTypeList.concat(this.transTypeListNd);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListNd);
                this.issuerList = this.issuerList.concat(this.issuerListNd);
                this.binCountryList = this.binCountryList.concat(listBlankDomestic);
                this.installmentPeriodList = this.installmentPeriodList.concat(listBlankDomestic);
                this.installmentStatusList = this.installmentStatusList.concat(listBlankDomestic);
                this.sourceList = this.sourceList.concat(this.sourceListNd);
                this.authenStateList = this.authenStateList.concat(listBlankDomestic);

            }
            if(gate.find(x => x.value == 'QR')){
                this.acquirerList = this.acquirerList.concat(this.acquirerListQr);
                this.qrChannelList = this.qrChannelList.concat(this.channelListQr)
                this.cardList = this.cardList.concat(this.cardListQr);
                this.issuerList = this.issuerList.concat(this.issuerListQr);
                this.binCountryList = this.binCountryList.concat(listBlankQr);
                this.installmentStatusList = this.installmentStatusList.concat(listBlankQr);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListQr);
                this.sourceList = this.sourceList.concat(listBlankQr);
                this.authenStateList = this.authenStateList.concat(listBlankQr);
                this.installmentPeriodList = this.installmentPeriodList.concat(listBlankQr);
                if(this.merchantChannelSelected.find(x => x.value == 'upos') && this.merchantChannelSelected.length == 1){
                    this.transTypeList = this.transTypeList.concat(this.transTypeListQr2);
                } else {
                    this.transTypeList = this.transTypeList.concat(this.transTypeListQr);
                }

            }
            if(gate.find(x => x.value == 'BNPL')){
                this.acquirerList = this.acquirerList.concat(listBlankBnpl);
                this.qrChannelList = this.qrChannelList.concat(this.channelListBnpl)
                this.cardList = this.cardList.concat(this.cardListBnpl);
                this.transTypeList = this.transTypeList.concat(this.transTypeListBnpl);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListBnpl);
                this.installmentStatusList = this.installmentStatusList.concat(listBlankBnpl);
                this.sourceList = this.sourceList.concat(listBlankBnpl);
                this.authenStateList = this.authenStateList.concat(listBlankBnpl);
                this.binCountryList = this.binCountryList.concat(listBlankBnpl);
                this.issuerList = this.issuerList.concat(listBlankBnpl);
                this.installmentPeriodList = this.installmentPeriodList.concat(listBlankBnpl);

            }
            if(gate.find(x => x.value == 'CARD')){
                this.acquirerList = this.acquirerList.concat(this.acquirerListCard);
                this.qrChannelList = this.qrChannelList.concat(this.channelListCard)
                this.cardList = this.cardList.concat(this.cardListCard);
                this.binCountryList = this.binCountryList.concat(this.binCountryListCard);
                this.installmentBankList = this.installmentBankListQt;
                this.installmentPeriodList = this.installmentPeriodList.concat(this.installmentPeriodListCard);
                this.transTypeList = this.transTypeList.concat(this.transTypeListCard);
                this.installmentStatusList = this.installmentStatusList.concat(this.installmentStatusListCard);
                this.issuerList = this.issuerList.concat(this.issuerListCard);
                this.sourceList = this.sourceList.concat(listBlankCard);
                this.authenStateList = this.authenStateList.concat(listBlankCard);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListCard);

            } else if(gate.find(x => x.value == 'DD')){
                // this.acquirerList = this.acquirerList.concat(this.acquirerListNd);
                // this.qrChannelList = this.qrChannelList.concat(this.channelListNd)
                this.cardList = this.cardList.concat(this.cardListDD);
                this.transTypeList = this.transTypeList.concat(this.transTypeListDD);
                this.responseCodeList = this.responseCodeList.concat(this.responseCodeListDD);
                // this.issuerList = this.issuerList.concat(this.issuerListNd);
                // this.binCountryList = this.binCountryList.concat(listBlankDomestic);
                // this.installmentPeriodList = this.installmentPeriodList.concat(listBlankDomestic);
                // this.installmentStatusList = this.installmentStatusList.concat(listBlankDomestic);
                this.sourceList = this.sourceList.concat(this.sourceListDD);
                // this.authenStateList = this.authenStateList.concat(listBlankDomestic);

            }
            if(gate.find(x => x.value == 'VietQR')){
                this.acquirerList = this.acquirerList.concat(this.acquirerListVietQr);
                this.qrChannelList = this.qrChannelList.concat(listBlankVietQR)
                this.cardList = this.cardList.concat(this.cardListVietQr);
                this.transTypeList = this.transTypeList.concat(listBlankVietQR);
                this.issuerList = this.issuerList.concat(listBlankVietQR);
                this.binCountryList = this.binCountryList.concat(listBlankVietQR);
                this.installmentBankList = this.installmentBankList.concat(listBlankVietQR);
                this.installmentStatusList = this.installmentStatusList.concat(listBlankVietQR);
                this.responseCodeList = this.responseCodeList.concat(listBlankVietQR);
                this.sourceList = this.sourceList.concat(listBlankVietQR);
                this.authenStateList = this.authenStateList.concat(listBlankVietQR);
                this.installmentPeriodList = this.installmentPeriodList.concat(listBlankVietQR);
            }

        }

    }

    displayResponseCode(txn: any): string {
        return [txn.responseCode, txn.responseDesc].filter(e => e != null && e != '').join(' - ');
    }
}
            // this.data = responses.response.data.list;
