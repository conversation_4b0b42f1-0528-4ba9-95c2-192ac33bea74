import { DatePipe } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { DataService } from '@service/data.service';
import { InternationalService } from '@service/international.service';
import { SSDisputeManagementService } from '@service/ss-dispute-management.service';
import { SSTransManagementService } from '@service/ss-trans-management.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { DisputeManagement } from 'app/model/dispute-management';
import { ToastrService } from 'ngx-toastr';
import { LazyLoadEvent } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { Observable, Subscription } from 'rxjs';
import 'rxjs/add/observable/zip';
import {
    disputeStatusList,
    fileStatusList,
    gateList,
    listDisputeCode,
    listDisputeStage,
    listOutcomeSearchSS,
    listReasonRiskQT,
    merchantChannelList,
    pageList,
    pageSizeList,
    SSDisputeCodeList,
    transCurrencyList,
    transStateList,
    transTypeList
} from '../../dispute-management-constants';
import { DisputeUtils } from '../../dispute-utils';
import { SSDisputeManagementSearch } from '../../search/ss-dispute-management-search.component';
import { DisputeColumnDisplayComponent } from '../column-display/dispute-column-display.component';
import { DisputeConfirmModalComponent } from '../dispute-detail/confirm-modal/confirm-modal.component';
import { DisputeDetailComponent } from '../dispute-detail/dispute-detail-component';
import { SendEmailDisputeComponent } from '../dispute-detail/send-email-modal/email-modal.component';
import { UpdateDisputeByBatchComponent } from '../update-dispute-dialog/update-by-batch.component';

@Component({
    selector: 'ss-dispute-management-component',
    templateUrl: './ss-dispute-management-component.html',
    styleUrls: ['./ss-dispute-management-component.scss'],
    providers: [SSTransManagementService, DialogService, SSDisputeManagementService]
})

export class SSDisputeManagementComponent implements OnInit, OnDestroy {
    public title = "Service Support - Dispute";
    public pageSize: number;
    public sub: Subscription;
    public resultsLength = 0;
    public loading: boolean;
    public data: Array<any>;
    private offsetHeight = 120;
    public flexScrollHeight = '200px';
    public showFormSearch = true;
    public transectionIdDetail;
    // public disputeStatusList: Array<any> = [];
    public disputeStatusList = disputeStatusList;
    public disputeStatusSelected;
    public cardTypeListGrouped;
    public cardTypeList: Array<any> = [];
    public cardTypeSelected;
    public disputeReason: any;
    public disputeCode;
    public disputeAmount: any;
    public outcome: any;
    public disputeStage: any;
    listDisputeReason: any[];
    public partnerName: string;
    public acquirerList: Array<any> = [];
    public acquirerSelected;
    public merchantChannelSelected;
    public transTypeSelected;
    public gateSelected;
    public transStateSelected;
    public merchantId: string;
    public invoiceId: string;
    public transId: string;
    public orderRef: string;
    public merchantTransRef: string;
    public qrId: string;
    public cardSelected;
    public cardNumber: string;
    public originalAmount: string;
    public transAmount: string;
    public authCode: string;
    private delayTimer: any;
    public selectedDisputes: any[];
    public checkWaitSendList = false;
    public checkWaitAdviseList = false;
    public checkWaitReopenList = false;
    public checkWaitRemindList = false;
    public checkEnableSendToBank = false;
    public checkEnableDelete = false;
    public channel;
    disputeDetail: DisputeManagement;
    public data_history = [];
    listDisputeStage: any[] = listDisputeStage;
    transTypeList: any[] = transTypeList;
    listDisputeCode: any[];
    public originalDispute: any;
    public dataEmail;
    private columnDisplayRef: any;
    public numberColumn: any;
    public coLumnSelected: any;
    public coLumnSelectedArray: Array<any> = [];
    public cols: any[];
    public closeMatch;
    public pageSizeList = pageSizeList;
    public pageList = pageList;
    public page = 1;
    public fileStatusList = fileStatusList;
    public fileStatus:any;
    public listReason = listReasonRiskQT;
    public listOutCome = listOutcomeSearchSS;
    public gateList = gateList;
    public merchantChannelList = merchantChannelList;
    public transStateList = transStateList;
    public SSDisputeCodeList = SSDisputeCodeList;

    public listDisputeStageSearch;
    public mapDisputeStage;
    public mapReason;
    public mapOutcome;
    public mapDisputeCode;
    @ViewChild(SSDisputeManagementSearch, { static: true }) searchForm: SSDisputeManagementSearch;
    channel1: any;
    public transCurrency;
    public disputeCurrency;
    public transCurrencyList = transCurrencyList;
    public listOnepayPic: any[] = [];
    public onepayPicSelected;
    public department;
    public navigateUrl;

    //update edit info dispute
    public listOutComeEdit: any[] = [];
    public listDisputeCodeEdit = [{label: '', value: ''}, ...SSDisputeCodeList.slice(1)];
    public listOperator: any[] = [];
    public showUpdateDispute = false;
    mapTimeout: Map<number, any> = new Map();

    files: File[] = [];
    fileNames: string = "";
    allowedExtensionArr = ['.zip'];
    allowedExtensionStr = this.allowedExtensionArr.join(',');
    invalidExtArr = [];

    isBack:string="";
    public SS_DISPUTE_ROLE = 'ss_dispute_domestic';

    constructor(
        private ssTranService: SSTransManagementService
        , private confirmService: ConfirmService
        , private ssDisputeService: SSDisputeManagementService
        , private internationalService: InternationalService
        , public datepipe: DatePipe
        , public global: Globals
        , private route: ActivatedRoute
        , private router: Router
        , private toastr: ToastrService
        , public datePipe: DatePipe
        , public dialogService: MatDialog
        , public dialogService2: DialogService
        , public dialogEmailService: DialogService
        , private disputeUtil: DisputeUtils
        , private disputeManagementService: SSDisputeManagementService
        , private fileDataService: DataService
        , public dialogPrime: DialogService
        , private disputeService: SSDisputeManagementService
    ) {
        this.disputeDetail = new DisputeManagement();
    }

    ngOnInit() {
        this.navigateUrl = ['/ss-dispute-management/list'];
        this.department = 'SERVICE_SUPPORT';

        this.cols = this.disputeUtil.getSavedColumns(this.department, '');
        this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
        this.mapDisputeStage = new Map(listDisputeStage.map(item => [(Number)(item.value), item.label]));
        this.listDisputeStageSearch = listDisputeStage.filter(item => item.label && item.value);
        this.mapOutcome = new Map(this.listOutCome.map(item => [item.value, item.label]));
        this.mapReason = new Map(this.listReason.map(item => [(Number)(item.value), item.label]));

        //close popup detail update data
        this.disputeUtil.saveDispute$.subscribe(dispute => {
            console.log('new value comming', dispute);
            this.setRow(dispute.id, dispute);
        });
    }

    setRow(id, dispute) {
        let row = this.data.find(e => e.id == id);
        if (!row) return;

        row.disputeDate = dispute.disputeDate || row.disputeDate;
        row.dueDate = dispute.dueDate || row.dueDate;
        row.disputeAmount = dispute.disputeAmount || row.disputeAmount;
        row.disputeCurrency = dispute.disputeCurrency || row.disputeCurrency;
        row.disputeStage = dispute.disputeStage || row.disputeStage;
        row.disputeCode = dispute.disputeCode || row.disputeCode;
        row.disputeReason = dispute.disputeReason || row.disputeReason;
        if(dispute.outcome) {
            row.disputeStatus = 'resolved';
            row.outcome = dispute.outcome
        }
        row.fraudInves = dispute.fraudInves || row.fraudInves;
        row.onepayPic = dispute.onepayPic || row.onepayPic;
        row.refundAmount = dispute.refundAmount || row.refundAmount; 
        row.lastUpdate = new Date();

        this.setRowTempValues(row);
    }

    @HostListener('window:resize', ['$event'])
    onResize(event) {
        this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
    }

    ngOnDestroy() {
    }

    checkSelectedDisputesByAttribute(attribute: string): { isSame: boolean, value?: any } {
        if (!this.selectedDisputes || this.selectedDisputes.length === 0) return { isSame: false };

        let valueFirstItem = this.selectedDisputes[0][attribute] || '';

        for (let item of this.selectedDisputes) {
            if (item[attribute] !== valueFirstItem) {
                return { isSame: false };
            }
        }
        return { isSame: true, value: valueFirstItem };
    }

    checkSelectedDisputesByChannel(): { isSame: boolean, value?: any, isDOM_APP: boolean, isINT: boolean } {
        if (!this.selectedDisputes || this.selectedDisputes.length === 0) return { isSame: false, isDOM_APP: true, isINT: true };

        let valueFirstItem = this.convertChannel(this.selectedDisputes[0].paygate);
        let isDOM_APP = valueFirstItem === 'DOM' || valueFirstItem === 'APP';
        let isINT = valueFirstItem === 'INT';

        for (let item of this.selectedDisputes) {
            let value = this.convertChannel(item.paygate);
            if (value !== valueFirstItem) {
                if (valueFirstItem === 'DOM' || valueFirstItem === 'APP') {
                    isDOM_APP = (value === 'DOM' || value === 'APP');
                    isINT = !isDOM_APP;
                } else { // === 'INT'
                    isINT = value === 'INT';
                    isDOM_APP = !isINT;
                }
                return { isSame: false, isDOM_APP: isDOM_APP, isINT: isINT };
            }
        }
        return { isSame: true, value: valueFirstItem, isDOM_APP: isDOM_APP, isINT: isINT };
    }

    checkSelectedDisputesByAccquirer(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('acquirer');
    }

    checkSelectedDisputesByMerchantId(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('merchantId');
    }

    checkSelectedDisputesByTransactionType(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('transactionType');
    }

    checkSelectedDisputesByTransactionStatus(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('transactionStatus');
    }

    checkSelectedDisputesByDisputeStatus(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('disputeStatus');
    }

    checkSelectedDisputesByDisputeCode(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('disputeCode');
    }

    handleCheckbox(dataItem: any) {
        let checkListMerchantId = this.checkSelectedDisputesByMerchantId();
        let checkListChannel = this.checkSelectedDisputesByChannel();
        let checkListTransactionType = this.checkSelectedDisputesByTransactionType();
        let checkListTransactionStatus = this.checkSelectedDisputesByTransactionStatus();
        let checkListDisputeStatus = this.checkSelectedDisputesByDisputeStatus();
        let checkListDisputeCode = this.checkSelectedDisputesByDisputeCode();
        let checkListAccquirer = this.checkSelectedDisputesByAccquirer();
        console.log(this.selectedDisputes);
        console.log('checkListMerchantId', checkListMerchantId);
        console.log('checkListChannel', checkListChannel);
        console.log('checkListTransactionType', checkListTransactionType);
        console.log('checkListTransactionStatus', checkListTransactionStatus);
        console.log('checkListDisputeStatus', checkListDisputeStatus);
        console.log('checkListDisputeCode', checkListDisputeCode);

        this.checkWaitSendList =
            checkListDisputeStatus.isSame && checkListDisputeStatus.value === 'created'
            && checkListMerchantId.isSame
            && (checkListChannel.isDOM_APP ? checkListChannel.isSame : true)
            && checkListTransactionType.isSame
            && checkListTransactionStatus.isSame
            && (checkListChannel.isINT ? checkListDisputeCode.isSame : true);
        
        this.checkEnableDelete = 
            checkListDisputeStatus.isSame && checkListDisputeStatus.value === 'created';

        this.checkWaitRemindList =
            checkListDisputeStatus.isSame
            && checkListDisputeStatus.value === 'need_merchant_response';

        this.checkWaitReopenList =
            checkListDisputeStatus.isSame
            && checkListDisputeStatus.value === 'resolved';

        this.checkWaitAdviseList = false; // để lại sau

        this.checkEnableSendToBank = checkListAccquirer.isSame && checkListAccquirer.value.includes("KBank");
        //xử lý update dispute theo lô
        this.checkShowUpdateDisputeBtn();
        
    }

    adviseHandler() {
        const disputeIdsArray = [];
        const transactionIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
            transactionIdsArray.push(this.selectedDisputes[i].transactionId);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'transactionIds': transactionIdsArray.toString(),
            'disputeStatus': 'need_merchant_response'
        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {

        });
    }

    sendDisputeHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, false, true);
    }

    reopenHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, true, false);
    }

    reopenHandlerActiveButton() {
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'disputeStatus': 'need_merchant_response',

        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {
            if (res && res.code) {
                this.selectedDisputes = [];
                this.onSubmit();
                this.toastr.success('Successfully re-opened dispute', 'Successfully');
            }
        });
    }

    remindHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, false, false);
    }

    remindHandlerActiveButton() {
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'disputeStatus': 'need_merchant_response'
        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {
            if (res && res.code) {
                this.selectedDisputes = [];
                this.onSubmit();
                this.toastr.success('Successfully reminded dispute', 'Successfully');
            }
        });
    }

    ////////
    approveAndSendToMerchant(reopen, send) {

        if (send) {
            if (this.disputeDetail && (this.disputeDetail.onepayPic == '' || this.disputeDetail.onepayPic == undefined)) {
                this.toastr.error('Please input required information.', 'Error');
                return;
            }
        }
        let body = {
            "dispute_ids": this.selectedDisputes.map(e => e.id).join(","),
            "transaction_id": this.disputeDetail.transactionId,
            "due_date": this.datePipe.transform(this.disputeDetail.dueDate, 'dd/MM/yyyy'),
            "merchant_name": this.disputeDetail.merchantName,
            "dispute_stage": this.disputeDetail.disputeStage,
            "paygate": this.disputeDetail.paygate,
            "dispute_code": this.disputeDetail.disputeCode,
            "order_ref": this.disputeDetail.orderReference ? this.disputeDetail.orderReference : '',
            "authorisation_code": this.disputeDetail.authorisationCode ? this.disputeDetail.authorisationCode : '',
            "merchant_id": this.disputeDetail.merchantId,
            "dispute_date": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
            "card_number": this.disputeDetail.cardNumber,
            "transaction_date": this.datePipe.transform(this.disputeDetail.transactionDate, 'dd/MM/yyyy HH:mm:ss'),
            "merchant_transaction_ref": this.disputeDetail.merchantTransactionReference,
            "transaction_amount": this.disputeDetail.transactionAmount,
            "dispute_amount": this.disputeDetail.disputeAmount

        };

        const countHistory = this.data_history.filter((item) => item.parentId !== 0).length;
        if (countHistory !== 0) {
            const ref = this.dialogService.open(DisputeConfirmModalComponent, {
                data: this.data_history,
            });
            ref.afterClosed().subscribe(result => {
                console.log('The dialog was closed');
                if (result) {
                    this.ssDisputeService.loadEmailContent(body).subscribe(resp => {
                        let dataEmail = {
                            'subject': resp.email_subject,
                            'emailTo': this.disputeDetail.sendDisputeTo,
                            'emailContent': resp.email_content,
                            'management': false,
                        }
                        const emailRef = this.dialogEmailService.open(SendEmailDisputeComponent, {
                            header: 'Send email',
                            contentStyle: { "min-height": "500px", "overflow": "auto" },
                            baseZIndex: 10000,
                            data: JSON.stringify(dataEmail),
                        });


                        emailRef.onClose.subscribe((data: any) => {
                            if (data?.management && !reopen) {
                                this.remindHandlerActiveButton();
                                this.sendEmailForm("sendDispute", data);
                            }
                            if (data?.management && reopen) {
                                this.reopenHandlerActiveButton();
                                this.sendEmailForm("sendDispute", data);
                            }
                        });
                        return;
                    });

                }
            });
        } else {
            this.ssDisputeService.loadEmailContent(body).subscribe(resp => {
                // if (resp && resp.nStatus === 200) {
                let dataEmail = {
                    'subject': resp.email_subject,
                    'emailTo': this.disputeDetail.sendDisputeTo,
                    'emailContent': resp.email_content,
                    'management': false,
                }
                const emailRef = this.dialogEmailService.open(SendEmailDisputeComponent, {
                    header: 'Send email',
                    contentStyle: { "min-height": "500px", "overflow": "auto" },
                    baseZIndex: 10000,
                    data: JSON.stringify(dataEmail),
                });

                emailRef.onClose.subscribe((data: any) => {
                    if (data?.management && !reopen) {
                        this.remindHandlerActiveButton();
                        this.sendEmailForm("sendDispute", data);
                    }
                    if (data?.management && reopen) {
                        this.reopenHandlerActiveButton();
                        this.sendEmailForm("sendDispute", data);
                    }
                });

                return;
            });
        }
    }


    formatDate2(inputDate: string): string {
        var outputDate = '';
        if (inputDate) {
            outputDate = this.datePipe.transform(new Date(inputDate), 'dd/MM/yyyy hh:mm a');
        }
        return outputDate;
    }

    formatDate3(inputDate: string): string {
        var outputDate = '';
        if (inputDate) {
            outputDate = this.datePipe.transform(new Date(inputDate), 'dd/MM/yyyy');
        }
        return outputDate;
    }

    convertDisputeStage(disputeStage) {
        let disputeStageLabel = '';
        if (this.listDisputeStage) {
            for (let i = 0; i < this.listDisputeStage.length; i++) {
                if (disputeStage == this.listDisputeStage[i].value) {
                    disputeStageLabel = this.listDisputeStage[i].label;
                }
            }
        }
        return disputeStageLabel;
    }

    convertDisputeCode(disputeCode) {
        let disputeCodeLabel = '';
        if (this.listDisputeCode) {
            for (let i = 0; i < this.listDisputeCode.length; i++) {
                if (this.listDisputeCode[i]['N_ID'] === (Number)(disputeCode)) {
                    disputeCodeLabel = this.listDisputeCode[i]['NAME'];
                    break;
                }

            }
        }
        return disputeCodeLabel;
    }

    convertDisputeStatus(status) {
        if (status == 'created') return 'Created';
        else if (status == 'need_merchant_response') return 'Need Merchant Response';
        else if (status == 'waiting_for_onepay_review') return 'Waiting for OnePay review';
        else if (status == 'resolved') return 'Resolved';
        else if (status == 'dispute_reminded') return 'Dispute Reminded';
    }


    sendEmailForm(type, data) {
        let body = {
            dispute_ids: this.selectedDisputes.map(e => e.id).join(",")
        };
        this.ssDisputeService.sendDispute(body).subscribe(res => {
            const bodyEmail = {
                "email_to": data.to ? data.to : '',
                "email_cc": data.cc ? data.cc : '',
                "email_subject": data.subject ? data.subject : '',
                "email_content": data.content ? data.content : '',
            }
            this.ssDisputeService.sendEmailToMerchant(bodyEmail).subscribe(res => {
                if (res && res.nStatus === '200')
                    this.toastr.success("Successfully sent dispute", "Success");
                else
                    this.toastr.error("An internal server error", "Error");
            });
            // this.dataContent(false, true, false);
        });
    }

    dataContent(init, reopen, send) {
        this.data_history = [];
        this.disputeDetail.disputeCode = '';
        let checkBeforeSend = true;
        let disputeIds = this.selectedDisputes.map(dispute => dispute.id.toString()).join(',');
        let body = {
            'dispute_ids': disputeIds
        }
        this.ssDisputeService.searchDisputeDetailByIds(body).subscribe(data => {
            if (data && data.list) {
                data.list.forEach(item => {
                    item['disputeStageLabel'] = item['disputeStage'] ? this.convertDisputeStage(item['disputeStage']) : '';
                    item['disputeCodeLabel'] = item['disputeCode'] ? this.convertDisputeCode(item['disputeCode']) : '';
                    item['transactionDateLabel'] = item['transactionDate'] ? this.formatDate2(item['transactionDate']) : '';
                    item['disputeDateLabel'] = item['disputeDate'] ? this.formatDate2(item['disputeDate']) : '';
                    item['dueDateLabel'] = item['dueDate'] ? this.formatDate3(item['dueDate']) : '';
                    item['disputeStatus'] = item['disputeStatus'] ? this.convertDisputeStatus(item['disputeStatus']) : '';
                    this.data_history.push(item);
                });


                this.disputeDetail.transactionId = data.list.map(dispute => dispute.transactionId).join(',');

                if (init) {
                    data.list.forEach(item => {
                        if (item && item.parentId == 0) {
                            this.originalDispute = item;
                            this.disputeDetail.transactionDate = new Date(item.transactionDate);
                            this.disputeDetail.authorisationCode = item.authorisationCode;
                            this.disputeDetail.transactionAmount = item.transactionAmount;
                            this.disputeDetail.merchantName = item.merchantName;
                            this.disputeDetail.orderReference = item.orderReference;
                            this.disputeDetail.merchantTransactionReference = item.merchantTransactionReference;
                            this.disputeDetail.merchantId = item.merchantId;
                            this.disputeDetail.cardNumber = item.cardNumber;
                            this.disputeDetail.id = item.id;
                            this.disputeDetail.disputeSender = item.disputeSender ? item.disputeSender : item.acquirer + ' ' + this.convertChannel(item.paygate);
                            this.disputeDetail.onepayPic = item.onepayPic;
                            this.disputeDetail.paygate = item.paygate;
                            if (item.sendDisputeTo) {
                                this.disputeDetail.sendDisputeTo = item.sendDisputeTo;
                            } else {

                                this.disputeManagementService.emailListByPartner(item.partnerId, item.paygate).subscribe(emailResponse => {
                                    this.disputeDetail.sendDisputeTo = emailResponse.emailList;
                                });

                            }
                            this.disputeDetail.merchantRespond = item.merchantRespond;
                            this.disputeDetail.businessCategory = item.businessCategory;
                            this.disputeDetail.merchantGroup = item.merchantGroup;
                            this.disputeDetail.disputeStage = item.disputeStage;
                            this.disputeDetail.disputeReason = item.disputeReason;
                            this.disputeDetail.disputeCode = item.disputeCode;

                            this.disputeDetail.disputeAmount = item.disputeAmount;
                            this.disputeDetail.disputeCurrency = item.disputeCurrency ? item.disputeCurrency : item.transactionCurrency;
                            this.disputeDetail.dueDate = item.dueDate;
                            this.disputeDetail.outcome = reopen ? '' : item.outcome;
                            this.disputeDetail.refNumber = item.refNumber;
                            this.disputeDetail.note = item.note;
                            this.disputeDetail.department = item.department;

                            if (!this.disputeUtil.checkRequiredFieldsBeforeSend(this.disputeDetail)) {
                                checkBeforeSend = false;
                            }
                        }
                    });

                    if (checkBeforeSend) {
                        this.approveAndSendToMerchant(reopen, send);
                    } else {
                        this.toastr.error('Please input required information.', 'Error');
                        return;
                    }
                }
            }
        });
    }

    deleteDispute() {
        console.log(this.selectedDisputes);
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
        }
        this.confirmService.build()        
            .title('Warning')
            .message('Are you sure that you want to delete these disputes?')
            .no('No')
            .yes('Yes')
            .confirm()
            .subscribe(agree => {
                if (agree) {
                    this.ssDisputeService.deleteDispute(body).subscribe(res => {
                        console.log('deleteDispute res: ', res);
                        if (res && res.code) {
                            this.selectedDisputes = [];
                            this.toastr.success('Delete disputes successfully', 'Successfully');
                            this.onSubmit();
                        }
                    });
                }
            })
        ;

    }


    getNewListDisputeCode() {
        const array = [];
        this.listDisputeCode = listDisputeCode;
        for (let i = 0; i < this.listDisputeCode.length; i++) {
            if (this.originalDispute && this.originalDispute.cardType && this.disputeDetail && this.disputeDetail.disputeStage
                && this.listDisputeCode[i].type == this.originalDispute.cardType && ((this.listDisputeCode[i].stage == "1" && this.disputeDetail.disputeStage == "1") || (this.listDisputeCode[i].stage == "2" && parseInt(this.disputeDetail.disputeStage)) > 1)) {
                array.push(this.listDisputeCode[i]);
            } else if (this.originalDispute && this.originalDispute.cardType && this.listDisputeCode[i].type == this.originalDispute.cardType && this.disputeDetail && this.disputeDetail.disputeStage == '') {
                array.push(this.listDisputeCode[i]);
            } else if (this.originalDispute && this.originalDispute.cardType == '' && this.disputeDetail && this.disputeDetail.disputeStage && ((this.listDisputeCode[i].stage == "1" && this.disputeDetail.disputeStage == "1") || (this.listDisputeCode[i].stage == "2" && parseInt(this.disputeDetail.disputeStage)) > 1)) {
                array.push(this.listDisputeCode[i]);
            }
        }
        if (array && array.length) {
            this.listDisputeCode = array;
        };
    }

    clearFilters(){
        this.disputeStatusSelected = [];
        this.merchantId = '';
        this.transId = '';
        this.orderRef = '';
        this.merchantTransRef = '';
        this.gateSelected = [];
        this.onepayPicSelected = [ {id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name}];
        this.cardTypeSelected = [];
        this.transStateSelected = [];
        this.transTypeSelected = [];
        this.merchantChannelSelected = [];
        this.partnerName = '';
        this.cardNumber = '';
        this.page = 1;
        this.transCurrency = [];
        this.disputeCurrency = [];
        this.disputeCode = [];
        this.outcome = [];
        this.filter();
    }

    filter() {
        // var self = this;
        // clearTimeout(this.delayTimer);
        // this.delayTimer = setTimeout(function () {
        //     return self.searchData().subscribe(responses => {
        //         self.resultsLength = responses.totalItems;
        //         self.data = responses.list;
        //         self.data.forEach(item => {
        //             self.setDisplayData(item);
        //         });
        //         self.initPage(self.resultsLength);
        //         //ver1: mỗi khi bấm filter 
        //         //thì phải upload lại file
        //         self.files = [];
        //     });
        // }, 3000);
        var self = this;
        return self.searchData().subscribe(responses => {
            self.resultsLength = responses.totalItems;
            self.data = responses.list;
            self.data.forEach(item => {
                self.setDisplayData(item);
            });
            self.initPage(self.resultsLength);
            //ver1: mỗi khi bấm filter 
            //thì phải upload lại file
            self.files = [];
        });

    }
    searchData() {
        this.router.navigate(this.navigateUrl, { queryParams: this.generateParams() });
        this.validateFilterColumn();
        return this.ssDisputeService.searchDisputeV2(this.generateParams());
    }

    getOnepayPicName(id) {
        let found;
        if (this.listOnepayPic) {
            found = this.listOnepayPic.find(e => e['id'] == id)
        }
        return found? found['name'] : id;
    }

    disputeStatus(status) {
        if (status == 'created') return 'Created';
        else if (status == 'need_merchant_response') return 'Need Merchant Response';
        else if (status == 'waiting_for_onepay_review') return 'Waiting for OnePay review';
        else if (status == 'resolved') return 'Resolved';
        else if (status == 'dispute_reminded') return 'Dispute Reminded';
    }

    download() {
        return this.ssDisputeService.downloadDispute(this.generateParamsDownload()).subscribe(response => {
            this.global.downloadEmit.emit(true);
        });
    }

    editCaseId(event: any) {
        event.target.nextElementSibling.focus();
    }

    validateFileUpload() {
        this.invalidExtArr = [];

        for (let i = 0; i < this.files.length; i++) {
            let file = this.files[i];
            let filename = file.name;
            let ext = this.getFileExtension(filename);
            if (!this.allowedExtensionArr.includes(ext)) {
                console.log('File extension not allowed: ', ext);
                this.invalidExtArr.push(ext);
            }
        }

        if (this.invalidExtArr.length == 0) {
            return true;
        }
        return false;
    }

    onFilechange(event: any, index: number) {
        console.log('onFilechange');
        if (event.target.files?.length) {
            this.data[index].fileName = event.target.files[0].name;
            //mỗi transaction chỉ đc upload 1 file
            let newFile: File = event.target.files[0];

            let valid = true;
            if (!this.validateFile(newFile)) {
                valid = false;
                return;
            }

            if (valid) {
                this.files = this.files.concat(newFile);
            }
        }
    }

    clearFileAfterUpload(event:any){
        const element = event.target as HTMLInputElement;
        element.value = '';
    }

    getFileExtension(filename: string) {
        let arr = filename.split('.');
        if (arr.length <= 1) {
            return "";
        }
        return "." + arr.pop();
    }

    validateFile(newFile) {
        let ext = this.getFileExtension(newFile.name);

        if (!this.allowedExtensionArr.includes(ext)) {
            let msg = 'File extension not allowed: ' + ext;
            this.toastr.error(msg);
            return false;
        }

        for (let oldFile of this.files) {
            if (oldFile.name == newFile.name) {
                let msg = 'Duplicate file name: ' + oldFile.name;
                this.toastr.error(msg);
                return false;
            }
        }

        return true;
    }

    removeFile(fileName: string, index: number) {
        this.files.splice(this.files.map(x => x.name).indexOf(fileName), 1);
        this.data[index].fileName = '';

        //remove file by fileName in localStorage
        this.fileDataService.removeFileDataByName(fileName);
    }

    validateCaseId(){
        let valid = true;
        this.data.forEach(it=> {
            if(it.fileName && !it.sCaseID){
                this.toastr.warning("Case ID cannot be empty", "Error");
                valid = false;
            }
        })
        return valid;
    }

    validateEmptyFile(){
        if(this.files.length > 0){
            return true;
        }
        else{
            this.toastr.warning("Choose file upload", "Error");
            return false;
        }
    }

    async sendToBank() {
        this.files = [];
        for (let row of this.selectedDisputes) {
            let res = await this.fileDataService.getFileFromTmp(row.filePath).toPromise();
            console.log("logg promise getFileBlob", res);
            const fileFromBlob = new File([res], row.fileName, { type: res.type, });
            console.log("file from blob", fileFromBlob);
            this.files = this.files.concat(fileFromBlob);
        }
        console.log('files', this.files);        
        //chạy sau
        if(this.validateCaseId() && this.validateEmptyFile()){
            let res:any = await this.disputeManagementService.sendToBank(this.files, this.global.activeProfile.email, this.selectedDisputes).toPromise();
            if (res && res.status && res.status == 'success') {
                this.files = [];
                this.fileDataService.clearFileData();
                this.onSubmit();
                this.checkEnableSendToBank = false;
                this.selectedDisputes = [];
                this.toastr.success("Successfully sent file to bank", "Success");
            }
            else{
                this.toastr.error("An internal server error", "Error");
            }
        }
    }

    onSubmit() {
        return this.searchData().subscribe(responses => {
            this.resultsLength = responses.totalItems;
            this.initPage(this.resultsLength);
            if (responses.list && responses.list.length > 0) {
                const array = [];
                responses.list.forEach(item => {
                    item.active = true;
                    if (!item.disputeCurrency) {
                        item.disputeCurrency = item.transactionCurrency;
                    }
                    //thêm trường fileName
                    item.fileName = '';
                    //thêm caseId
                    if(!item.sCaseID){
                        item.sCaseID = ""; 
                    }
                    //thêm noteDisputeCase
                    if(!item.noteDisputeCase){
                        item.noteDisputeCase = "";
                    }
                    //
                    this.setDisplayData(item);
                    if(this.addDataWhenFilterFile(item) != null){
                        array.push(item);
                    }
                });
                this.data = array;
                this.fileDataService.sendListDispute(this.data);
                this.checkShowUpdateDisputeBtn();
            } else {
                this.data = [];
            }
        });
    }

    onChangePage() {
        return this.searchData().subscribe(responses => {
            this.resultsLength = responses.totalItems;
            this.data = responses.list;
            this.data.forEach(item => {
                this.setDisplayData(item);
            });
            this.initPage(this.resultsLength);
        });
    }

    initPage(resultsLength: any) {
        if (resultsLength && resultsLength > 100) {
            let numberPage = Math.ceil(resultsLength / 100);
            this.pageList = [];
            for (let i = 1; i <= numberPage; i++) {
                this.pageList.push({
                    value: i,
                    label: i + ''
                });
            }
        } else {
            this.pageList = [];
            this.pageList.push({
                value: 1,
                label: '1'
            });
        }
    }

    addDataWhenFilterFile(itemAdd:any){
        //nếu có filter fileStatus = chưa gửi hoặc đã gửi
        if(this.fileStatus && this.fileStatus.length == 1){
            let fileStatus = this.fileStatus.map(x => x.name).join(",");
            if(fileStatus == 'Chưa gửi' && !itemAdd.nFileID){
                return itemAdd;
            }
            else if(fileStatus == 'Đã gửi' && itemAdd.nFileID && itemAdd.nFileID != ''){
                return itemAdd;
            }
            else {
                return null;
            }
        }
        else{
            return itemAdd;
        }
    }

    initFilters(params) {
        this.disputeStatusSelected = params['dispute_status'] ? this.disputeStatusList.filter(item => params['dispute_status'].split(',').includes(item.value)) : [];
        this.merchantId = params['merchant_id'] || '';
        this.partnerName = params['partner_name'] || '';
        this.transId = params['transaction_id'] || '';
        this.orderRef = params['order_ref'] || '';
        this.merchantTransRef = params['merchant_transaction_ref'] || '';
        this.gateSelected = params['channel'] ? this.gateList.filter(item => params['channel'].split(',').includes(item.value)) : [];
        this.onepayPicSelected = params['pic'] ? this.listOnepayPic?.filter(item => params['pic'].split(',').includes(item.id)) : this.onepayPicSelected;
        this.cardTypeSelected = params['card_type'] ? this.cardTypeList.filter(item => params['card_type'].split(',').includes(item.value)) : [];
        this.cardNumber = params['card_number'] || '';
        this.page = params['page'] ? + params['page'] + 1 : 1;
        this.transCurrency = params['transaction_currency'] ? this.transCurrencyList.filter(item => params['transaction_currency'].split(',').includes(item.value)) : [];
        this.disputeCurrency = params['dispute_currency'] ? this.transCurrencyList.filter(item => params['dispute_currency'].split(',').includes(item.value)) : [];
        this.disputeCode = params['dispute_code'] ? this.SSDisputeCodeList.filter(item => params['dispute_code'].split(',').includes(item.value)) : [];
        this.outcome = params['outcome'] ? this.listOutCome.filter(item => params['outcome'].split(',').includes(item.value)) : [];
        this.closeMatch = 'contains' == params["filter_type"];
        this.isBack = params['isBack'];
    }

    loadLazy(event: LazyLoadEvent) {
        let params = this.route.snapshot.queryParams;
        Observable.zip(
            this.ssTranService.getDropdownCardListSS(),
            this.disputeManagementService.getAllOnePayPics(this.SS_DISPUTE_ROLE),
            this.disputeManagementService.getAcquirer(),
            this.disputeManagementService.getDisputeCode(),
            this.disputeService.getOperator()
        ).subscribe( arr => {
            let cardListRes = arr[0];
            let onepayPicRes = arr[1];
            let acqRes = arr[2];
            let disputeCodeRes = arr[3];
            let operatorRes = arr[4];

            this.cardTypeListGrouped = cardListRes as Array<any>;
            this.cardTypeListGrouped.forEach(group => {
                group.items.forEach(item => this.cardTypeList.push(item))
            });
            console.log('dispute cardTypeList: ', this.cardTypeList);
            
            onepayPicRes.list.forEach(element => {
                if (element['id'] && element['name']) {
                element['id'] = element.id.toString();
                this.listOnepayPic.push(element);
                }
            });

            operatorRes.list.forEach(element => {
                if (element['id'] && element['name']) {
                element['id'] = element.id.toString();
                this.listOperator.push(element);
                }
            })
            if (!this.listOnepayPic.find(element => element.id == this.global.activeProfile.n_id.toString())) {
                this.listOnepayPic.push({ id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name });
            }
            console.log('dispute listOnepayPic: ', this.listOnepayPic);

            this.listDisputeCode = disputeCodeRes.list;
            this.mapDisputeCode = new Map(this.listDisputeCode.map(item => [item.N_ID, item.NAME]));

            this.onepayPicSelected = [ {id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name}];
            this.searchForm.init(params, null);
            this.initFilters(params);
            this.loading = true;
            this.searchData().subscribe(responses => {
                this.loading = false;
                this.resultsLength = responses.totalItems;
                this.initPage(this.resultsLength);
                if (responses.list && responses.list.length > 0) {
                    const array = [];
                    responses.list.forEach(item => {
                        item.active = true;
                        if (!item.disputeCurrency) {
                            item.disputeCurrency = item.transactionCurrency;
                        }
                        this.setDisplayData(item);
                        array.push(item);
                    });
                    this.data = array;
                }
            });
        });
    }

    getStage(id): String {
        if (id) {
            for (let i = 0; i < listDisputeStage.length; i++) {
                if (this.listDisputeStage[i].value == id) {
                    return listDisputeStage[i].label;
                    break;
                }
            }
        }
    }
    getCode(id): String {
        if (id) {
            for (let i = 0; i < this.listDisputeCode.length; i++) {
                if (this.listDisputeCode[i].N_ID == id) {
                    return this.listDisputeCode[i].NAME;
                    break;
                }
            }
        }
    }
    getOutCome(id): String {
        if (id) {
            for (let i = 0; i < this.listOutCome.length; i++) {
                if (this.listOutCome[i].value == id) {
                    return this.listOutCome[i].label;
                    break;
                }
            }
        }
    }
    getReason(id): String {
        if (id) {
            for (let i = 0; i < this.listReason.length; i++) {
                if (this.listReason[i].value == id) {
                    return this.listReason[i].label;
                    break;
                }
            }
        }
    }


    checkColumn(code: string): boolean {
        let col = this.cols.find(e => e.code == code);
        return col? col.active : false;
    }

    checkDuplicateList(checkArray: any, checkExists: boolean, value: any) {
        checkExists = false;
        if (checkArray.length > 0) {
            checkArray.forEach(dropdown => {
                if (dropdown.value === value) {
                    checkExists = true;
                    return checkExists;
                }
            });
        }

        return checkExists;
    }

    checkFormSearch(type: boolean) {
        this.showFormSearch = type;
    }

    generateParams() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        this.pageSize = this.pageSize == undefined ? 100 : this.pageSize;
        var params = {
            'back_location': 'ss-list',
            'from_date': from_date,
            'to_date': to_date,
            'dispute_status': this.disputeStatusSelected ? this.disputeStatusSelected.map(x => x.value).join(',') : '',
            'merchant_id': this.merchantId ? this.merchantId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchant_transaction_ref': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'channel': this.gateSelected? this.gateSelected.map(x => x.value).join(',') : '',
            'acquirer_id': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(',') : "",
            'pic': this.onepayPicSelected ? this.onepayPicSelected.map(x => x.id).join(',') : "",
            'card_type': this.cardTypeSelected ? this.cardTypeSelected.map(x => x.value).join(',') : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'authorisation_code': this.authCode ? this.authCode.trim() : "",
            'transaction_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'dispute_currency': this.disputeCurrency ? this.disputeCurrency.map(x => x.value).join(",") : "",
            'dispute_code': this.disputeCode ? this.disputeCode.map(x => x.value).join(",") : "",
            'outcome': this.outcome ? this.outcome.map(x => x.value).join(',') : "",
            'page_size': this.pageSize == undefined ? 100 : this.pageSize,
            'page': (this.page - 1) + '',
            'filter_type': this.closeMatch? 'contains' : 'equals',
            'user_id': this.global.activeProfile.n_id,
            'department': this.department,
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'partner_name': this.partnerName || '',
            'transactionState': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
        }

        return params;
    }


    generateParamsDownload() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');

        var params = {
            'from_date': from_date,
            'to_date': to_date,
            'dispute_status': this.disputeStatusSelected ? this.disputeStatusSelected.map(x => x.value).join(',') : '',
            'merchant_id': this.merchantId ? this.merchantId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchant_transaction_ref': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'channel': this.gateSelected? this.gateSelected.map(x => x.value).join(',') : '',
            'acquirer_id': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(',') : "",
            'pic': this.onepayPicSelected ? this.onepayPicSelected.map(x => x.id).join(',') : "",
            'card_type': this.cardTypeSelected ? this.cardTypeSelected.map(x => x.value).join(',') : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'authorisation_code': this.authCode ? this.authCode.trim() : "",
            'transaction_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'dispute_currency': this.disputeCurrency ? this.disputeCurrency.map(x => x.value).join(",") : "",
            'dispute_code': this.disputeCode ? this.disputeCode.map(x => x.value).join(",") : "",
            'outcome': this.outcome ? this.outcome.map(x => x.value).join(',') : "",
            'page_size': this.pageSize == undefined ? 100 : this.pageSize,
            'page': (this.page - 1) + '',
            'filter_type': this.closeMatch? 'contains' : 'equals',
            'user_id': this.global.activeProfile.n_id,
            'department': this.department,
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'partner_name': this.partnerName || '',
            'transactionState': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'column_list': this.getColumnListDownload(),
            'column_active': this.getColumnActiveDownload(),
            'dispute_ids': (this.selectedDisputes && this.selectedDisputes.length)? this.selectedDisputes.map(e => e.id).join(",") : null,
            'file_type': 'ss-list',
            'role': this.SS_DISPUTE_ROLE
        }

        return params;
    }

    getColumnListDownload() {
        return this.cols
            .map(e => e.code)
            .concat(['transactionCurrency', 'refundCurrency', 'disputeCurrency'])
            .join(",");
    }

    getColumnActiveDownload() {
        let codeArr = this.cols.filter(e => e.active)
            .filter(e => !['MID', 'caseId', 'file', 'fileStatus'].includes(e.code)) /*bo cac cot nay*/
            .map(e => e.code);
        if (this.cols.find(item => item.code == 'transactionAmount' && item.active)) {
            codeArr.push('transactionCurrency');
        }
        if (this.cols.find(item => item.code == 'refundAmount' && item.active)) {
            codeArr.push('refundCurrency');
        }
        if (this.cols.find(item => item.code == 'disputeAmount' && item.active)) {
            codeArr.push('disputeCurrency');
        }
        return codeArr.join(",");
    }

    convertChannel(channel) {
        if (channel === 'QT') return 'INT';
        else if (channel === 'ND') return 'DOM';
        else return 'APP';
    }

    convertGate(gate) {
        if (gate === 'QT') return 'International';
        else if (gate === 'ND') return 'Domestic';
        else if (gate === 'QR') return 'Mobile App';
        else return gate;
    }

    displayCardNo(cardNo: string, gate: string) {
        return cardNo?
            gate == 'QT'? cardNo.substring(0, 6) + "***" + cardNo.substring(cardNo.length - 4)
                : "***" + cardNo.substring(cardNo.length - 4)
            : '';
    }

    formatDate(value, format) {
        if (value && value != '') {
            return this.datepipe.transform(value.toString(), format);
        } else {
            return '';
        }
    }

    convertStage(value) {
        if (!value)
            return '';
        if (this.mapDisputeStage.get((Number)(value)))
            return this.mapDisputeStage.get((Number)(value));
        else
            return value
    }

    convertReason(value) {
        if (!value)
            return '';
        if (this.mapReason && this.mapReason.get((Number)(value)))
            return this.mapReason.get((Number)(value));
        return value
    }

    convertOutcome(value) {
        if (!value)
            return '';
        if (this.mapOutcome && this.mapOutcome.get(value))
            return this.mapOutcome.get(value);
        return value;
    }

    displayDisputeCode(value) {
        if (this.mapDisputeCode && this.mapDisputeCode.get((Number)(value)))
            return this.mapDisputeCode.get(value);
        return '';
    }


    convertCardType(inputData: string, type: string): string {
        var outputData = '';
        // Client is MSB, display Mobile Banking / E-Wallet instead of QR
        if (type !== 'ND') {
            outputData = inputData;
        } else {
            if (inputData) {
                let acq = this.cardTypeList.find(obj => obj.value.split(',').includes(inputData));
                if (acq !== undefined)
                    outputData = acq.label;
                else
                    outputData = '';
            }
        }
        if (!outputData) {
            outputData = inputData;
        }
        return outputData;
    }

    selectAll() {
        if (this.selectedDisputes && this.selectedDisputes.length > 0) {
            this.selectedDisputes.forEach((element, index) => {
                this.handleCheckbox(element);
            });
        } else {
            this.checkWaitSendList = false;
            this.checkWaitAdviseList = false;
            this.checkWaitRemindList = false;
            this.checkWaitReopenList = false;
            const array = [];
            this.data.forEach(item => {
                item['active'] = true;
                array.push(item);
            });
            this.data = array;
        }
        //xử lý update dispute theo lô
        this.checkShowUpdateDisputeBtn();
    }

    getTotalPage(totalRecords: number, rows: number) {
        return Math.ceil(totalRecords / rows);
    }

    openColumnDisplay() {
        this.columnDisplayRef = this.dialogService2.open(DisputeColumnDisplayComponent, {
            header: 'Column Display ',
            contentStyle: { "max-height": "90%", "width": "400px", "overflow": "auto" },
            baseZIndex: 10000,
            data: {
                columnItems: this.cols,
            }
        });

        this.columnDisplayRef.onClose.subscribe(result => {
            if (result) {
                this.cols = result;
                this.cols.sort((a, b) => (a.order > b.order) ? 1 : -1);
                console.log('dispute cols', this.cols);
                this.validateFilterColumn();
                this.disputeUtil.saveColumns(this.cols, this.department, '');
            }
        });
    }

    validateFilterColumn() {
        for (let col of this.cols) {
            if (col.active) continue;

            if (col.code === 'disputeStatus') {
                this.disputeStatusSelected = undefined;
            }
            if (col.code === 'onepayPic') {
                this.onepayPicSelected = undefined;
            }
            if (col.code === 'merchantId') {
                this.merchantId = undefined;
            }
            if (col.code === 'transId') {
                this.transId = undefined;
            }
            if (col.code === 'orderRef') {
                this.orderRef = undefined;
            }
            if (col.code === 'merchantTransRef') {
                this.merchantTransRef = undefined;
            }
            if (col.code === 'channel') {
                this.gateSelected = undefined;
            }
            if (col.code === 'acq') {
                this.acquirerSelected = undefined;
            }
            if (col.code === 'cardType') {
                this.cardTypeSelected = undefined;
            }
            if (col.code === 'cardNumber') {
                this.cardNumber = undefined;
            }
            if (col.code === 'authCode') {
                this.authCode = undefined;
            }
            if (col.code === 'transactionAmount') {
                this.transCurrency = undefined;
            }
            if (col.code === 'disputeAmount') {
                this.disputeCurrency = undefined;
            }
            if (col.code === 'disputeStage') {
                this.disputeStage = undefined;
            }
            if (col.code === 'disputeReason') {
                this.disputeReason = undefined;
            }
            if (col.code === 'outcome') {
                this.outcome = undefined;
            }
            if (col.code === 'merchantChannel') {
                this.merchantChannelSelected = undefined;
            }
            if (col.code === 'transactionType') {
                this.transTypeSelected = undefined;
            }
            if (col.code === 'transactionState') {
                this.transStateSelected = undefined;
            }
        }

    }

    openDetailDialog(disputeId: String) {
        const detailDialog = this.dialogPrime.open(DisputeDetailComponent, {
            contentStyle: { "max-height": "85%", "max-width": "100%"},
            data: {
                disputeId: disputeId,
                isPopup: true,
                queryParams: this.generateParams(),
                baseZIndex: 998
            },
            width: '100%'
        });
        detailDialog.onClose.subscribe(() => {
            console.log('close detail ss_dispute dialog');
        });
    }

    editDropdown(row, fieldName) {
        // if (fieldName == 'disputeStage') {
        //     row.disputeCode = undefined;
        //     this.setRowDisputeCodeList(row);
        // }
        this.setTimeoutSaveDispute(row);
    }

    setRowTempValues(row) {
        row['disputeDate'] = row.disputeDate? new Date(row.disputeDate) : undefined;
        row['displayDueDate'] = row.dueDate? new Date(row.dueDate) : undefined;
    }

    setTimeoutSaveDispute(row) {
        let findTimeout = this.mapTimeout.get(row.id);
        if (findTimeout) {
            clearTimeout(findTimeout);
            this.mapTimeout.delete(row.id);
        }
        
        let timeout = setTimeout(() => this.saveDispute(row), 4000);
        this.mapTimeout.set(row.id, timeout);
    }

    clickPCalendar(element) {
        if (element.containerViewChild.nativeElement.children[0]) {
            element.containerViewChild.nativeElement.children[0].click();
        } else {
            console.log('error');
        }
    }

    saveDispute(row) {
        console.log('saveDispute', row.id);
        let check = this.checkRequiredFields(row);
        if (!check.valid) {
            row.missingRequired = true;
            row.toarstMsg = check.msg;
            this.toastr.warning(row.toarstMsg);
            return;
        } else {
            row.missingRequired = false;
            row.toarstMsg = '';
        }

        let body = {
            "id": row.id,
            "disputeSender": row.disputeSender,
            "onepayPic": row.onepayPic,
            "sendDisputeTo": row.sendDisputeTo || '',
            "merchantRespond": row.merchantRespond,
            "businessCategory": row.businessCategory || '',
            "merchantGroup": row.merchantGroup,
            "disputeStage": row.disputeStage || '',
            "disputeReason": row.disputeReason,
            "disputeCode": row.disputeCode,
            "disputedAmount": row.disputeAmount,
            "disputeCurrency": row.disputeCurrency,
            "dueDate": this.formatDate(row.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "disputeDate": this.formatDate(row.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "outcome": row.outcome,
            "refNumber": row.refNumber,
            "note": row.note,
            "merchantName": row.merchantName,
            "orderReference": row.orderReference,
            "merchantId": row.merchantId,
            "cardNumber": row.cardNumber,
            "merchantTransactionReference": row.merchantTransactionReference,
            "transactionAmount": row.transactionAmount,
            "authorisationCode": row.authorisationCode,
            "transactionDate": this.formatDate(row.transactionDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "operatorId": row.operatorId,
            "operatorName": row.operatorName,
            "fraudInves": row.fraudInves,
        };
        this.disputeService.updateDispute(row.id, body).subscribe(res => {
            if (res && res.code == 200) {
                this.toastr.success('Successfully saved dispute', 'Success');
                row.lastUpdate = new Date();
                row.saveSuccess = true;
                row.saveFailed = false;
                setTimeout(() => row['saveSuccess'] = false, 3000);
                if (row.outcome) row.disputeStatus = 'resolved';
                this.setRowTempValues(row);
            } else {
                row['saveFailed'] = true;
            }
            this.setDisplayData(row);
        } 
        , err => { row['saveFailed'] = true; }
        );
    }

    checkRequiredFields(row) {
        let msg = '';
        let valid = true;
        row.dueDate = row.displayDueDate;
        if (!row.disputeCode) {
            msg = 'Dispute Code is required';
            valid = false;
        // } else if (!row.outcome) {
        //     msg = 'Outcome is required';
        //     valid = false;
        } else if (!row.onepayPic) {
            msg = 'OnePay PIC is required';
            valid = false;
        } else if (!row.dueDate) {
            msg = 'Due Date is required';
            valid = false;
        }
        return {msg, valid};
    }

    editDispute(row, fieldName) {
        if (fieldName == 'dueDate') {
            row.dueDate = row.displayDueDate;
        }
        this.setTimeoutSaveDispute(row);
    }

    filterDisputeCode(transactionType: string, transactionStatus: string) {
        let listDisputeCode = [];
        listDisputeCode.push({
            label: '',
            value: ''
        });
        if (transactionType == 'Purchase') {
            if (!transactionStatus || transactionStatus == 'Failed') {
                listDisputeCode.push({
                    label: 'ATM03: Transaction failed but got deducted',
                    value: '10003'
                });
            } else {
                listDisputeCode.push({
                    label: 'ATM01: Merchandise or Service not received',
                    value: '10001'
                });
                listDisputeCode.push({
                    label: 'ATM02: Fraud / Not recognize transaction',
                    value: '10002'
                });
            }
        } else {
            listDisputeCode.push({
                label: 'ATM04: Refund not processed',
                value: '10004'
            });
        }
        return listDisputeCode;
    }

    filterOutCome(transactionType: string, transactionStatus: string) {
        let listOutComeEdit = [];
        listOutComeEdit.push({
            label: '',
            value: ''
        });
        if (transactionType == 'Purchase') {
            if (!transactionStatus || transactionStatus == 'Failed') {
                listOutComeEdit.push({
                    label: 'Resolved',
                    value: 'Resolved'
                });
            } else {
                listOutComeEdit.push({
                    label: 'Order confirmed',
                    value: '8'
                });
                listOutComeEdit.push({
                    label: 'Refunded',
                    value: 'Refunded'
                });
            }
        } else {
            listOutComeEdit.push({
                label: 'Resolved',
                value: 'Resolved'
            });
        }
        return listOutComeEdit;
    }

    setDisplayData(item) {
        if (item.dueDate) {
            item.displayDueDate = new Date(item.dueDate);
        } else if (item.disputeDate){
            var disputeDate = new Date(item.disputeDate);
            item.displayDueDate = new Date(disputeDate.setDate(disputeDate.getDate() + 5));
        } else {
            item.displayDueDate = undefined; 
        }
    }

    getElementInArray(array: Array<any>) {
        let element;
        if (array && array.length > 0) {
            element = array.map(i => (i.name ? i.name : i.label)).join(",");
        }
        return element;
    }


    resetValue(data: any) {
        if (data == 'disputeStatusSelected') {
            this.disputeStatusSelected = [];
        } else if (data == 'merchantChannelSelected') {
            this.merchantChannelSelected = [];
        } else if (data == 'gateSelected') {
            this.gateSelected = [];
        } else if (data == 'partnerName') {
            this.partnerName = undefined;
        } else if (data == 'merchantId') {
            this.merchantId = undefined;
        } else if (data == 'transId') {
            this.transId = undefined;
        } else if (data == 'orderRef') {
            this.orderRef = undefined;
        } else if (data == 'merchantTransRef') {
            this.merchantTransRef = undefined;
        }else if (data == 'cardTypeSelected') {
            this.cardTypeSelected = [];
        } else if (data == 'cardNumber') {
            this.cardNumber = undefined;
        } else if (data == 'transCurrency') {
            this.transCurrency = [];
        } else if (data == 'transTypeSelected') {
            this.transTypeSelected = [];
        } else if (data == 'transStateSelected') {
            this.transStateSelected = [];
        } else if (data == 'disputeCurrency') {
            this.disputeCurrency = [];
        } else if (data == 'disputeCode') {
            this.disputeCode = [];
        } else if (data == 'outcome') {
            this.outcome = [];
        } else if (data == 'onepayPicSelected') {
            this.onepayPicSelected = [];
        }
    }

    checkShowUpdateDisputeBtn() {
        if (this.selectedDisputes?.length > 1) {
            //check same dispute_status
            const mapDisputeStatus = this.selectedDisputes.map(item => item.disputeStatus);
            let isSameDisputeStatus = mapDisputeStatus?.length === 0 ? true :
                mapDisputeStatus.every(val => val === mapDisputeStatus[0]);
            //check same transaction_type
            const mapTransType = this.selectedDisputes.map(item => item.transactionType);
            let isSameTransType = mapTransType?.length === 0 ? true :
                mapTransType.every(val => val === mapTransType[0]);
            //check same transaction_status
            const mapTransStatus = this.selectedDisputes.map(item => item.transactionStatus);
            let isSameTransStatus = mapTransStatus?.length === 0 ? true :
                mapTransStatus.every(val => val === mapTransStatus[0]);

            this.showUpdateDispute = isSameTransType && isSameTransStatus && isSameDisputeStatus;
        } else {
            this.showUpdateDispute = false;
        }
    }

    openUpdateDisputeByBatch() {
        //xử lý list dispute code
        let firstItem = this.selectedDisputes[0];
        let disputeCodes = this.filterDisputeCode(firstItem.transactionType, firstItem.transactionStatus);
        let outcomes = this.filterOutCome(firstItem.transactionType, firstItem.transactionStatus);;

        this.columnDisplayRef = this.dialogService2.open(UpdateDisputeByBatchComponent, {
            header: 'Update Dispute By Batch - Selected Disputes: ' + this.selectedDisputes.length,
            contentStyle: { "height": "250px", "width": "450px"},
            baseZIndex: 10000,
            data: {
                dispute_code_list: disputeCodes.splice(1),
                outcome_list: outcomes.splice(1)
            }
            
        });

        this.columnDisplayRef.onClose.subscribe(result => {
            if (result) {
                this.selectedDisputes.forEach(dispute => {
                    dispute.disputeCode = result.dispute_code ? result.dispute_code : dispute.disputeCode;
                    dispute.outcome = result.outcome ? result.outcome : dispute.outcome;
                    this.data.forEach(item => {
                        if (item.id === dispute.id) {
                            item.disputeCode = dispute.disputeCode;
                            item.outcome = dispute.outcome;
                            if (item.outcome) {
                                item.disputeStatus = 'resolved';
                            }
                        }
                    })
                });
                let updateList = [];
                this.selectedDisputes.forEach(row => {
                    if (!row.dueDate) {
                        var disputeDate = new Date(row.disputeDate);
                        row.dueDate= new Date(disputeDate.setDate(disputeDate.getDate() + 5));
                    }
                    let dispute = {
                        "id": row.id,
                        "disputeSender": row.disputeSender,
                        "onepayPic": row.onepayPic,
                        "sendDisputeTo": row.sendDisputeTo || '',
                        "merchantRespond": row.merchantRespond,
                        "businessCategory": row.businessCategory || '',
                        "merchantGroup": row.merchantGroup,
                        "disputeStage": row.disputeStage || '',
                        "disputeReason": row.disputeReason,
                        "disputeCode": row.disputeCode,
                        "disputedAmount": row.disputeAmount,
                        "disputeCurrency": row.disputeCurrency,
                        "dueDate": this.formatDate(row.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                        "disputeDate": this.formatDate(row.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                        "outcome": row.outcome,
                        "refNumber": row.refNumber,
                        "note": row.note,
                        "merchantName": row.merchantName,
                        "orderReference": row.orderReference,
                        "merchantId": row.merchantId,
                        "cardNumber": row.cardNumber,
                        "merchantTransactionReference": row.merchantTransactionReference,
                        "transactionAmount": row.transactionAmount,
                        "authorisationCode": row.authorisationCode,
                        "transactionDate": this.formatDate(row.transactionDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                        "operatorId": this.global.activeProfile.n_id,
                        "operatorName": row.operatorName,
                        "fraudInves": row.fraudInves,
                    }
                    updateList.push(dispute);
                });
                let body = {
                    'data': updateList
                }
                this.disputeService.updateByBatch(body).subscribe(res => {
                    if (res && res.status == 200) {
                        this.selectedDisputes = [];
                        this.showUpdateDispute = false;
                        this.toastr.success("Update disputes by batch successful");
                    }
                });
            }
        });
    }
}
export class SelectOption {
    constructor(
        public label: string,
        public value: string
    ) { }
}
export interface DisputeCodeGroup {
    letter: string;
    type: string;
    stage: string
    names: SelectOption[];
}

