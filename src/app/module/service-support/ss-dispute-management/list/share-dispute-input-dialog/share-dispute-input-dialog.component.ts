import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { SelectItem } from 'primeng/api';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';

@Component({
    selector: 'share-dispute-input-dialog',
    templateUrl: './share-dispute-input-dialog.component.html',
    styleUrls: ['./share-dispute-input-dialog.component.scss']
})
export class ShareDisputeInputDialogComponent implements OnInit {

    public data;
    public selectedDisputes: any[] = [];
    
    // Form fields - Initialize with null (not selected/filled) vs '' (selected blank/empty)
    public businessCategory: string | null = null;
    public disputeAmount: string | null = null;
    public disputeCrr: string | null = null;
    public disputeReason: string | null = null;
    public disputeCode: string | null = null;
    public disputeStage: string | null = null;
    public outcome: string | null = null;
    public fraudInvestigation: string | null = null;
    public internalNote: string | null = null;
    public disputeFileFromIssuer: string | null = null;
    public evidence: string | null = null;

    // Dropdown lists
    public businessCategoryList: SelectItem[];
    public disputeReasonList: SelectItem[];
    public disputeCodeList: SelectItem[];
    public disputeStageList: SelectItem[];
    public outcomeList: SelectItem[];
    public fraudInvestigationList: SelectItem[];
    public disputeFileFromIssuerList: SelectItem[];

    pipe = new DatePipe('en-US');

    constructor(
        public config: DynamicDialogConfig,
        private ref: DynamicDialogRef,
        private confirmService: ConfirmService,
    ) {
    }

    ngOnInit(): void {
        this.data = this.config.data;
        this.selectedDisputes = this.data.selectedDisputes || [];
        
        // Initialize dropdown lists from data with safe defaults and normalize
        this.businessCategoryList = this.normalizeDropdownData(
            this.data.business_category_list || [{ label: 'Blank', value: '' }]
        );
        this.disputeReasonList = this.normalizeDropdownData(
            this.data.dispute_reason_list || [{ label: 'Blank', value: '' }]
        );
        this.disputeCodeList = this.normalizeDropdownData(
            this.data.dispute_code_list || [{ label: 'Blank', value: '' }]
        );
        this.disputeStageList = this.normalizeDropdownData(
            this.data.dispute_stage_list || [{ label: 'Blank', value: '' }]
        );
        console.log('=-=-=-=-=-=Outcome original:',  this.data.outcome_list);
        this.outcomeList = this.normalizeDropdownData(
            this.data.outcome_list || [{ label: 'Blank', value: '' }]
        );
        this.fraudInvestigationList = this.normalizeDropdownData(
            this.data.fraud_investigation_list || [{ label: 'Blank', value: '' }]
        );
        this.disputeFileFromIssuerList = this.normalizeDropdownData(
            this.data.dispute_file_from_issuer_list || [
                { label: 'No', value: 'No' },
                { label: 'Yes', value: 'Yes' }
            ]
        );
        
        // Log for debugging
        console.log('Dropdown lists initialized:');
        // console.log('Business Category:', this.businessCategoryList.length, 'items');
        // console.log('Dispute Code:', this.disputeCodeList.length, 'items');
        // console.log('Dispute Reason:', this.disputeReasonList.length, 'items');
        // console.log('Dispute Stage:', this.disputeStageList.length, 'items');
        console.log('Outcome:', this.outcomeList);
        console.log('Fraud Investigation:', this.fraudInvestigationList);
        // console.log('Dispute File from Issuer:', this.disputeFileFromIssuerList.length, 'items');
    }

    /**
     * Normalize dropdown data:
     * 1. Change label to 'Blank' if value is empty string
     * 2. Remove duplicates (same label and value)
     */
    normalizeDropdownData(data: SelectItem[]): SelectItem[] {
        if (!data || !Array.isArray(data)) {
            return [{ label: 'Blank', value: '' }];
        }

        // Step 1: Normalize labels for empty values
        const normalizedData = data.map(item => {
            if (item.value === '' || item.value === null || item.value === undefined) {
                return { ...item, label: 'Blank', value: '' };
            }
            return item;
        });

        // Step 2: Remove duplicates based on both label and value
        const uniqueData: SelectItem[] = [];
        const seen = new Set<string>();

        normalizedData.forEach(item => {
            // Create unique key from label and value
            const key = `${item.label}|${item.value}`;
            
            if (!seen.has(key)) {
                seen.add(key);
                uniqueData.push(item);
            }
        });

        // Ensure at least one Blank option exists at the beginning
        const hasBlankOption = uniqueData.some(item => item.value === '' || item.label === 'Blank');
        if (!hasBlankOption) {
            uniqueData.unshift({ label: 'Blank', value: '' });
        }

        uniqueData.forEach(item => {
            if (item.label === 'Blank') {
                item.value = '';
            }
        });
        return uniqueData;
    }

    onSubmit() {
        // Log current values for debugging
        console.log('Submit - Current Values (null = not touched, "" = selected blank):');
        console.log('businessCategory:', this.businessCategory, typeof this.businessCategory);
        console.log('disputeAmount:', this.disputeAmount, typeof this.disputeAmount);
        console.log('disputeCrr:', this.disputeCrr, typeof this.disputeCrr);
        console.log('disputeReason:', this.disputeReason, typeof this.disputeReason);
        console.log('disputeCode:', this.disputeCode, typeof this.disputeCode);
        console.log('disputeStage:', this.disputeStage, typeof this.disputeStage);
        console.log('outcome:', this.outcome, typeof this.outcome);
        console.log('fraudInvestigation:', this.fraudInvestigation, typeof this.fraudInvestigation);
        console.log('internalNote:', this.internalNote, typeof this.internalNote);
        console.log('disputeFileFromIssuer:', this.disputeFileFromIssuer, typeof this.disputeFileFromIssuer);
        console.log('evidence:', this.evidence, typeof this.evidence);
        
        // Collect ALL fields (including null)
        // Backend will check: null = skip, '' = update to blank, value = update to value
        const updatedFields: any = {
            businessCategory: this.businessCategory,
            disputeAmount: this.disputeAmount,
            disputeCrr: this.disputeCrr,
            disputeReason: this.disputeReason,
            disputeCode: this.disputeCode,
            disputeStage: this.disputeStage,
            outcome: this.outcome,
            fraudInvestigation: this.fraudInvestigation,
            internalNote: this.internalNote,
            disputeFileFromIssuer: this.disputeFileFromIssuer,
            evidence: this.evidence
        };

        console.log('Updated Fields (including nulls):', updatedFields);

        // Check if at least one field is not null (has changes)
        const hasChanges = Object.values(updatedFields).some(value => value !== null);
        
        if (!hasChanges) {
            console.log('No changes detected - all fields are null');
            this.ref.close();
            return;
        }

        // Check if any selected disputes have existing data for the fields being updated
        const fieldsWithExistingData = this.checkExistingData(updatedFields);
        
        if (fieldsWithExistingData.length > 0) {
            // Show confirmation dialog
            const fieldNames = fieldsWithExistingData.join(', ');
            this.confirmService.build()
                .message(`Some disputes already have data for: ${fieldNames}. Do you want to overwrite the existing data?`)
                .title('Confirm Overwrite')
                .no('Cancel')
                .yes('Confirm')
                .confirm()
                .subscribe(accept => {
                    if (accept) {
                        this.ref.close(updatedFields);
                    }
                });
        } else {
            // No conflicts, proceed with update
            this.ref.close(updatedFields);
        }
    }

    checkExistingData(updatedFields: any): string[] {
        const fieldsWithData: string[] = [];
        const fieldMapping = {
            businessCategory: 'Business Category',
            disputeAmount: 'Dispute Amount',
            disputeCrr: 'Dispute CRR',
            disputeReason: 'Dispute Reason',
            disputeCode: 'Dispute Code',
            disputeStage: 'Dispute Stage',
            outcome: 'Outcome',
            fraudInvestigation: 'Fraud Investigation',
            internalNote: 'Internal Note',
            disputeFileFromIssuer: 'Dispute File from Issuer',
            evidence: 'Evidence'
        };

        // Only check fields that user actually selected (not null)
        Object.keys(updatedFields).forEach(field => {
            // Skip fields that user didn't touch (null)
            if (updatedFields[field] === null) {
                return;
            }

            // Check if any selected dispute has existing data for this field
            const hasExistingData = this.selectedDisputes.some(dispute => {
                const value = dispute[field];
                return value !== null && value !== undefined && value !== '';
            });

            if (hasExistingData) {
                fieldsWithData.push(fieldMapping[field]);
            }
        });

        return fieldsWithData;
    }

    cancel() {
        this.ref.close();
    }

    formatCurrency(value) {
        return parseFloat(value).toFixed(0).replace(/\d(?=(\d{3})+\.)/g, '$&,').toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    }
}

