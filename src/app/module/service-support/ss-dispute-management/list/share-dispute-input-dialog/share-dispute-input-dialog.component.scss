.content{
  z-index: 600 !important;
  overflow: visible !important;
}

.modal-body {
  overflow: visible !important;
}

form {
  overflow: visible !important;
}

button {
  outline: none !important;
  font-size: 12px;
}
button:hover {
  cursor: pointer;
}

:host ::ng-deep p-calendar {
  width: 100%;
}

:host ::ng-deep p-calendar .p-calendar {
  width: 100%;
}

:host ::ng-deep .p-dropdown {
  width: 100%;
}

:host ::ng-deep .p-datatable .p-datatable-thead > tr > th {
  padding: 6px !important;
}

.tr_style {
  height: 30px !important;
}

.tr_body td {
  /* background-color: white !important; */

  border-bottom: 1px solid rgb(214, 214, 214) !important;
  border-left: 1px solid rgb(214, 214, 214) !important;
  border-right: 1px solid rgb(214, 214, 214) !important;
  font-size: 1rem !important;
}

.style-th {
  background-color: #f0f0f0 !important;
  color: black !important;
  /* border: 1px solid  rgb(155, 155, 155) !important; */
  border: 1px solid rgb(201, 201, 201) !important;
  padding-left: 2px !important;
  padding-right: 5px !important;
  font-size: 1rem !important;
}

.box-fiter button {
  width: 100%;
  border: 1px solid #ccc;
}

.page-right .head-right {
  padding-top: 15px;
}

.download_templ_xml {
  font-size: 12px !important;
  margin-top: 15px;
  display: inline-block;
}

.btn-down-ib {
  font-size: 12px !important;
  display: inline-block;
  background-color: #efefef;
  color: #000;
}

.btn-down-ib:hover {
  background-color: #fff;
  border: 1px solid #ccc;
}

::ng-deep .p-paginator-top {
  padding: 0;
}

::ng-deep .create_report {
  border: 1px solid #ccc !important;
  background-color: rgb(239, 239, 239) !important;
}

// ::ng-deep .p-paginator-right-content {
//   margin-left: auto;
//   width: 100% !important;
// }

::ng-deep .modal-footer {
  border-top: none;
}

.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-datepicker-toggle-default-icon,
.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-datepicker-toggle-default-icon {
  width: 2em !important;
}

#summary tr td {
  font-size: 1rem !important;
}

.text-muted span {
  width: 115px;
  float: left;
}

.text-muted label {
  float: left;
}

.tr_body:nth-child(odd) {
  background: #eaf3f7;
}

.color-link {
  color: #007ad9 !important;
}


// Dropdown styles
:host ::ng-deep .p-dropdown {
  width: 100%;
  border: 1px solid #ced4da;
  
  .p-dropdown-label {
    padding: 8px 12px;
  }
  
  &.p-focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

// Dropdown panel styles - critical for showing inside modal
:host ::ng-deep .p-dropdown-panel {
  max-height: 300px !important;
  width: 100% !important;
  min-width: 250px !important;
  z-index: 99999 !important;
  position: absolute !important;
  
  .p-dropdown-header {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
    
    .p-dropdown-filter {
      width: 100%;
      padding: 6px 10px;
      border: 1px solid #ced4da;
      border-radius: 4px;
    }
  }
  
  .p-dropdown-items-wrapper {
    max-height: 250px !important;
    overflow-y: auto !important;
  }
  
  .p-dropdown-items {
    padding: 4px 0;
    
    .p-dropdown-item {
      padding: 8px 12px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
      
      &:hover {
        background-color: #f0f0f0;
      }
      
      &.p-highlight {
        background-color: #007bff;
        color: white;
      }
    }
    
    .p-dropdown-empty-message {
      padding: 8px 12px;
      color: #6c757d;
    }
  }
}

// Fix for dropdown panel z-index in modal
:host ::ng-deep {
  .p-component-overlay {
    z-index: 99998 !important;
  }
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.row {
  align-items: center;
}

span {
  font-weight: 500;
  color: #495057;
}


