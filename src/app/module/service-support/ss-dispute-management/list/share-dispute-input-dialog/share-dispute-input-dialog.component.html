<div class="content">
    <div class="modal-body">
        <form>
            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Business Category</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Business Category" 
                        [(ngModel)]="businessCategory" 
                        [options]="businessCategoryList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="businessCategory"
                        [ngModelOptions]="{standalone: true}"
                        [virtualScroll]="true"
                        [itemSize]="38">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute Amount</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <input type="text" class="form-control" placeholder="Enter Dispute Amount" 
                        [(ngModel)]="disputeAmount" name="disputeAmount" />
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute CRR</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <input type="text" class="form-control" placeholder="Enter Dispute CRR" 
                        [(ngModel)]="disputeCrr" name="disputeCrr" />
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute Reason</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Dispute Reason" 
                        [(ngModel)]="disputeReason" 
                        [options]="disputeReasonList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="disputeReason"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute Code</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Dispute Code" 
                        [(ngModel)]="disputeCode" 
                        [options]="disputeCodeList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="disputeCode"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute Stage</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Dispute Stage" 
                        [(ngModel)]="disputeStage" 
                        [options]="disputeStageList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="disputeStage"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Outcome</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Outcome" 
                        [(ngModel)]="outcome" 
                        [options]="outcomeList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="outcome"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Fraud Investigation</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Fraud Investigation" 
                        [(ngModel)]="fraudInvestigation" 
                        [options]="fraudInvestigationList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="fraudInvestigation"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Internal Note</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <input type="text" class="form-control" placeholder="Enter Internal Note" 
                        [(ngModel)]="internalNote" name="internalNote" maxlength="1000" />
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Dispute File from Issuer</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <p-dropdown 
                        [style]="{'width':'100%'}" 
                        placeholder="Select Dispute File from Issuer" 
                        [(ngModel)]="disputeFileFromIssuer" 
                        [options]="disputeFileFromIssuerList" 
                        [filter]="true" 
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        filterBy="label" 
                        optionValue="value" 
                        optionLabel="label"
                        name="disputeFileFromIssuer"
                        [ngModelOptions]="{standalone: true}">
                    </p-dropdown>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-sm-4 col-md-4 col-lg-4">
                    <span>Evidence</span>
                </div>
                <div class="col-sm-8 col-md-8 col-lg-8">
                    <input type="text" class="form-control" placeholder="Enter Evidence" 
                        [(ngModel)]="evidence" name="evidence" />
                </div>
            </div>
        </form>

        <div class="modal-footer">
            <div class="container col-md-12">
                <div class="row">
                    <div class="col-md-3"></div>
                    <div class="col-md-3">
                        <button type="button" style="height: 35px !important; margin-top: 25px;"
                            class="btn btn-secondary w-100 text-center btn-modal"
                            data-dismiss="modal" (click)="cancel()">Cancel</button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" style="height: 35px !important; margin-top: 25px;"
                            class="btn btn-primary w-100 text-center btn-modal" (click)="onSubmit()">Update</button>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

