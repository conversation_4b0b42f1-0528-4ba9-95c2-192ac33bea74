<div id="risk-dispute-management-international" [ngClass]="showFormSearch ? 'wrapper' : 'wrapper hide-form-search'">
  <div class="row">

    <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12 ">
      <span class="title_menu" style="margin-left: 10px !important;">{{title}}</span>
    </div>
    <div class="padding-right-0 col-sm-12 col-md-12 col-lg-12" id="search-form">
      <ss-dispute-management-search (submitGeneralSearch)="onSubmit()" (clearFilters)="clearFilters()" [selectedRecord]="selectedDisputes?.length"></ss-dispute-management-search>
    </div>

  </div>

  <div class="container-fluid" style="margin-top: 10px !important;"
    [id]="showFormSearch ? 'show-form-search' : 'hide-form-search'">
    <div class="row">

      <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12 ">
        <div class="ui-helper-clearfix clearfix row-button" style="text-align: left; margin-bottom: 10px;">
          <div style="float:left; font-size: 14px;">
            <button type="button" pButton  icon="pi pi-times" iconPos="left" id="btn-delete"
              class="column-display-button p-button-danger" style="margin-right: 10px" id="btn-delete-dispute"
              [disabled]="!selectedDisputes || !selectedDisputes.length || !checkEnableDelete" label="Delete Dispute" (click)="deleteDispute()">
            </button>
            <button type="button" pButton  icon="pi pi-file" iconPos="left" id="btn-delete"
              [disabled]="!enableCreateCSV" class="column-display-button p-button-success" style="margin-right: 10px" id="btn-create-csv-file"
              label="Create CSV File" (click)="createCSVFile()">
            </button>
            <button type="button" pButton label="Share Dispute Input" (click)="openUpdateDisputeByBatch()" [disabled]="!showUpdateDispute"
                class="column-display-button p-button-success" id="btn-update-dispute" style="margin-right: 10px">
            </button>
          </div>
          <div style="float:right; font-size: 14px;">
            <span style="margin-right: 10px; margin-top: 5px;">
              <p-checkbox name="type_filter" inputId="type_filter" [binary]="true" [(ngModel)]="closeMatch"></p-checkbox>
              <label for="type_filter" style="margin-left: 5px; font-weight: bold; margin-bottom: 0rem !important; cursor: pointer;">Close match</label>
            </span>
            <button type="button" pButton label="Column Display" (click)="openColumnDisplay()"
              class="column-display-button p-button-success" id="btn-column" style="margin-right: 10px">
            </button>
            <button type="button" [disabled]="!checkEnableSendToBank" pButton label="Send to Bank" icon=""
              iconPos="left" style="margin-right: 0.5em;" class="" (click)="sendToBank()"></button>
            <button type="button" [disabled]="!checkWaitSendList" pButton label="Send to Merchant" icon=""
              iconPos="left" style="margin-right: 0.5em;" class="" (click)="sendDisputeHandler()"></button>
            <button type="button" [disabled]="!checkWaitReopenList" pButton label="Re-open" icon="" iconPos="left"
              style="margin-right: 0.5em;" class="" (click)="reopenHandler()"></button>
            <button type="button" [disabled]="!checkWaitRemindList" pButton label="Remind" icon="" iconPos="left"
              style="margin-right: 0.5em;" class="" (click)="remindHandler()"></button>
            <button type="button" pButton label="Download" icon="pi pi-download" iconPos="left" style="margin-right: 0.5em;"
              class="" (click)="download()"></button>
          </div>
        </div>

        <p-table class="sticky-headers-table google-table" [(selection)]="selectedDisputes" [value]="data" dataKey="id"
          [rows]="pageSize ? pageSize : '100' " [lazy]="true" (onLazyLoad)="loadLazy($event)"
          [totalRecords]="resultsLength" pageLinks="0" [paginator]="true" [scrollable]="true"
          [scrollHeight]="'calc(100vh - 310px)'" [(columns)]="cols" [resizableColumns]="true" paginatorPosition="bottom"
          columnResizeMode="expand">

          <ng-template pTemplate="paginatorleft" let-state>
            <div class="ui-helper-clearfix clearfix" style="text-align: left; margin-left: -10px; margin-top: -10px;">
              <label class="label-custom" for="">100 records per page | {{resultsLength}} records</label>
            </div>
          </ng-template>

          <ng-template pTemplate="paginatorright" let-state>
            <div class="ui-helper-clearfix clearfix " style="text-align: left">
              <div class="total-item" style="float:right; margin-right: -30px; margin-top: -5px;">
                <table-paginator [state]="state" [totalRecords]="resultsLength"></table-paginator>
              </div>

              <div style="float:right; margin-right: -20px; margin-left: 60px; margin-top: 5px;">
                <label class="label-custom" for="">of {{getTotalPage(state.totalRecords, state.rows)}}</label>
              </div>

              <div style="float:right; font-size: 14px; margin-right: -50px;">
                <div class="form-group">
                  <span class="input-group">
                    <p-dropdown appendTo="body" [style]="{'width':'100%'}" [options]="pageList" [(ngModel)]="page" [virtualScroll]="true" itemSize="10"
                      (onChange)="onChangePage()" dropdownIcon="pi pi-angle-down" #pageT="ngModel" name="page">
                    </p-dropdown>
                    <label class="label-custom" for=""></label>
                  </span>
                </div>
              </div>

              <div style="float:right; margin-top: 5px;">
                <label class="label-custom" for="">Page</label>
              </div>
            </div>
          </ng-template>

          <ng-template pTemplate="colgroup" let-columns>
            <colgroup>
              <col class="mat-column-number">
              <ng-container *ngFor="let col of cols">
                <col class="mat-column-100" *ngIf="col.code=='disputeStatus' && col.active"/>
                <col class="mat-column-140" *ngIf="col.code=='disputeDate' && col.active"/>
                <col class="mat-column-140" *ngIf="col.code=='dueDate' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='merchantChannel' && col.active"/>
                <col class="mat-column-150" *ngIf="col.code=='partnerName' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='MID' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='merchantId' && col.active"/>
                <col class="mat-column-190" *ngIf="col.code=='transId' && col.active"/>
                <col class="mat-column-160" *ngIf="col.code=='orderRef' && col.active"/>
                <col class="mat-column-160" *ngIf="col.code=='merchantTransRef' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='channel' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='acq' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='cardType' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='cardNumber' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='authCode' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='transactionAmount' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='transactionDate' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='transactionType' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='transactionState' && col.active"/>
                <col class="mat-column-100" *ngIf="col.code=='refundAmount' && col.active"/>
                <col class="mat-column-140" *ngIf="col.code=='disputeAmount' && col.active"/>
                <col class="mat-column-185" *ngIf="col.code=='disputeReason' && col.active"/>
                <col class="mat-column-150" *ngIf="col.code=='disputeCode' && col.active"/>
                <col class="mat-column-130" *ngIf="col.code=='disputeStage' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='outcome' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='fraudInves' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='caseId' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='file' && col.active"/>
                <col class="mat-column-120" *ngIf="col.code=='fileStatus' && col.active"/>
                <col class="mat-column-170" *ngIf="col.code=='onepayPic' && col.active"/>
                <col class="mat-column-150" *ngIf="col.code=='lastUpdate' && col.active"/>
                <col class="mat-column-150" *ngIf="col.code=='disputeFileFromIssuers' && col.active"/>
              </ng-container>
            </colgroup>
          </ng-template>
          <ng-template pTemplate="header">
            <tr class="tr_header tr_header1">
              <th class="text-center" pResizableColumn></th>
              <ng-container *ngFor="let col of cols">
                <th class="text-center" pResizableColumn *ngIf="col.active">{{col.name}}</th>
              </ng-container>
            </tr>
            <tr class="tr_header tr_header2">
              <th>
                <p-tableHeaderCheckbox (click)="selectAll()"></p-tableHeaderCheckbox>
              </th>

              <ng-container *ngFor="let col of cols">
              <th class="text-center" pResizableColumn *ngIf="col.code=='disputeStatus' && col.active">
                <!-- <p-columnFilter field="gate" matchMode="in" [showMenu]="false">
                  <ng-template pTemplate="filter"> -->
                <p-multiSelect [(ngModel)]="disputeStatusSelected" [options]="disputeStatusList" appendTo="body"
                  placeholder="All" maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-disputeStatus">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-disputeStatus-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
                <!-- </ng-template>
                </p-columnFilter> -->
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='onepayPic' && col.active">
                <p-multiSelect [(ngModel)]="onepayPicSelected" [options]="listOnepayPic" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="name" id="select-onepayPic">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-dropDown-option" class="txt-filter" id="{{'option-onepayPic-' + option.name}}">
                      <span class="p-ml-1">{{option.name}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='disputeDate' && col.active"></th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='dueDate' && col.active"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='merchantChannel' && col.active">
                <p-multiSelect [(ngModel)]="merchantChannelSelected" [options]="merchantChannelList" appendTo="body" placeholder="All"
                maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected" id="select-merchantChannel"
                optionLabel="label" *ngIf="col.code === ('merchantChannel') && col.active === true">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-merchantChannel-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='partnerName' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="partnerName" (ngModelChange)="filter()" maxlength="200" id="input-partnerName"/>
              </th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='lastUpdate' && col.active"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='MID' && col.active"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='merchantId' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="merchantId" (ngModelChange)="filter()"
                  maxlength="400" id="input-merchantId"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='transId' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="transId" (ngModelChange)="filter()"
                  maxlength="50" id="input-transId"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='orderRef' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="orderRef" (ngModelChange)="filter()"
                  maxlength="50" id="input-orderRef"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='merchantTransRef' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="merchantTransRef"
                  (ngModelChange)="filter()" maxlength="50" id="input-merchantTransRef"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='channel' && col.active" style="background-color: #eaeaea !important;"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='acq' && col.active">
                <!-- <p-columnFilter field="acquirer" matchMode="in" [showMenu]="false"> -->
                <!-- <ng-template pTemplate="filter"> -->
                <p-multiSelect [(ngModel)]="acquirerSelected" [options]="acquirerList" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-acq">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-dropDown-option" class="txt-filter" id="{{'option-acq-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
                <!-- </ng-template> -->
                <!-- </p-columnFilter> -->
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='cardType' && col.active">
                <p-multiSelect [(ngModel)]="cardTypeSelected" [options]="cardTypeList" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-cardType">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-dropDown-option" class="txt-filter" id="{{'option-cardType-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='cardNumber' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="cardNumber" (ngModelChange)="filter()"
                  maxlength="50" id="input-cardNumber"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='authCode' && col.active">
                <input pInputText type="text" class="txt-filter" [(ngModel)]="authCode" (ngModelChange)="filter()"
                  maxlength="50" id="input-authCode"/>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='transactionAmount' && col.active">
                <p-multiSelect [(ngModel)]="transCurrency" [options]="transCurrencyList" appendTo="body"
                      placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                      selectedItemsLabel="{0} values selected" optionLabel="label" id="select-transCurrency">
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-transCurrency-' + option.value}}">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                    </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='transactionDate' && col.active"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='transactionType' && col.active">
                <p-multiSelect [(ngModel)]="transTypeSelected" [options]="transTypeList" appendTo="body"
                      placeholder="All" maxSelectedLabels="1" (onChange)="filter()" id="select-transType"
                      selectedItemsLabel="{0} values selected" optionLabel="label">
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-transType-' + option.value}}">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='transactionState' && col.active">
                <p-multiSelect [(ngModel)]="transStateSelected" [options]="transStateList" appendTo="body"
                      placeholder="All" maxSelectedLabels="1" (onChange)="filter()" id="select-transState"
                      selectedItemsLabel="{0} values selected" optionLabel="label">
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-transState-' + option.value}}">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='refundAmount' && col.active"></th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='disputeAmount' && col.active">
                <p-multiSelect [(ngModel)]="disputeCurrency" [options]="transCurrencyList" appendTo="body"
                      placeholder="All" maxSelectedLabels="1" (onChange)="filter()"
                      selectedItemsLabel="{0} values selected" optionLabel="label" id="select-disputeCurrency">
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-disputeCurrency-' + option.value}}">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                    </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='disputeReason' && col.active">
                <!-- <input pInputText type="text" class="txt-filter" [(ngModel)]="disputeReason" (ngModelChange)="filter()"
                  maxlength="50" /> -->
                <p-multiSelect [(ngModel)]="disputeReason" [options]="listReason" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-disputeReason">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-disputeReason-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='disputeCode' && col.active">
                <!-- <input pInputText type="text" class="txt-filter" [(ngModel)]="disputeCode" (ngModelChange)="filter()"
                  maxlength="50" /> -->
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='disputeStage' && col.active">
                <!-- <input pInputText type="text" class="txt-filter" [(ngModel)]="disputeStage" (ngModelChange)="filter()"
                  maxlength="50" /> -->
                <p-multiSelect [(ngModel)]="disputeStage" [options]="listDisputeStageSearch" appendTo="body"
                  placeholder="All" maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-disputeStage">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-disputeStage-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='outcome' && col.active">
                <!-- <input pInputText type="text" class="txt-filter" [(ngModel)]="outcome" (ngModelChange)="filter()"
                  maxlength="50" /> -->
                <p-multiSelect [(ngModel)]="outcome" [options]="listOutCome" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-outcome">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-outcome-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn *ngIf="col.code=='fraudInves' && col.active">
                <p-multiSelect [(ngModel)]="fraudInvesSelected" [options]="fraudInvesList" appendTo="body" placeholder="All"
                  maxSelectedLabels="1" (onChange)="filter()" selectedItemsLabel="{0} values selected"
                  optionLabel="label" id="select-fraudInves">
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-fraudInves-' + option.value}}">
                      <span class="p-ml-1">{{option.label}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='caseId' && col.active"></th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='file' && col.active"></th>
              <th class="text-center" pResizableColumn style="background-color: #eaeaea !important;" *ngIf="col.code=='fileStatus' && col.active">
                <p-multiSelect 
                  appendTo="body"
                  [options]="fileStatusList"
                  [(ngModel)]="fileStatus"
                  [style]="{'width':'100%'}" 
                  defaultLabel="All" 
                  optionDisabled="inactive"
                  optionLabel="name" maxSelectedLabels="1"
                  selectedItemsLabel="{0} items selected">
                  id="select-fileStatus"
                  <ng-template let-option pTemplate="item">
                    <div class="p-multiselect-representative-option" class="txt-filter" id="{{'option-fileStatus-' + option.name}}">
                      <span class="p-ml-1">{{option.name}}</span>
                    </div>
                  </ng-template>
                </p-multiSelect>
              </th>
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-i="rowIndex" let-data>
            <tr class="tr_body" [class.save-failed]="data.saveFailed" [class.save-success]="data.saveSuccess" [class.missing-required]="data.missingRequired">
              <td>
                <p-tableCheckbox [value]="data" [disabled]="data.active == false" (click)="handleCheckbox(data)" id="{{'checkbox-transId-' + data.transactionId}}">
                </p-tableCheckbox>
              </td>
              <ng-container  *ngFor="let col of cols">
                <td *ngIf="col.code=='merchantChannel' && col.active">
                  {{data.merchantChannel}}
                </td>
                <td *ngIf="col.code=='partnerName' && col.active" style="overflow-wrap: anywhere;white-space: pre-line">
                  {{data.partnerName}}
                </td>
                <td *ngIf="col.code=='disputeStatus' && col.active" style="overflow-wrap: anywhere;white-space: pre-line"
                  [ngStyle]="{ 'color': (convertDisputeStatus(data.disputeStatus) == 'Resolved' ? '#70ad47' : (convertDisputeStatus(data.disputeStatus) == 'Created' ? '#000000' : '#ff9900')) }">
                  {{convertDisputeStatus(data.disputeStatus)}}
                </td>
                <td *ngIf="col.code=='onepayPic' && col.active">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.onepayPic" [options]="listOperator" appendTo="body" placeholder=""
                  (onChange)="editDropdown(data, 'onepayPic')" [filter]="true" filterBy="name" optionValue="id" optionLabel="name">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.name}}</span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </td>
                <td *ngIf="col.code=='disputeDate' && col.active" class="text-left ui-resizable-column">
                  <p-calendar #cal_disputeDate appendTo="body" [(ngModel)]="data.dDisputeDate" [showIcon]="false" [style]="{'width':'99%'}"
                  showTime="true" hourFormat="12" dateFormat="dd/mm/yy " inputStyleClass="input-dispute-date" readonlyInput="true"
                  hideOnDateTimeSelect="true" requiredhideOnDateTimeSelect="true" (onSelect)="editDispute(data, 'disputeDate', value)">
                  </p-calendar>
                  <img class="btn-edit" src="assets/img/pencil.svg" (click)="clickPCalendar(cal_disputeDate)">
                </td>
                <td *ngIf="col.code=='dueDate' && col.active" class="text-left ui-resizable-column">
                  <p-calendar #cal_dueDate appendTo="body" [(ngModel)]="data.dDueDate" [showIcon]="false" [style]="{'width':'99%'}"
                  showTime="true" hourFormat="12" dateFormat="dd/mm/yy " inputStyleClass="input-dispute-date" readonlyInput="true"
                  hideOnDateTimeSelect="true" requiredhideOnDateTimeSelect="true" (onSelect)="editDispute(data, 'dueDate', value)">
                  </p-calendar>
                  <img class="btn-edit" src="assets/img/pencil.svg" (click)="clickPCalendar(cal_dueDate)">
                </td>
                <td *ngIf="col.code=='lastUpdate' && col.active" class="text-left ui-resizable-column">
                  {{formatDate(data.lastUpdate, 'dd/MM/yyyy hh:mm a')}}
                </td>
                <td *ngIf="col.code=='MID' && col.active" class="text-left ui-resizable-column">
                  {{data.sMid}}
                </td>
                <td *ngIf="col.code=='merchantId' && col.active" class="text-left ui-resizable-column">
                  {{data.merchantId}}
                </td>
                <td *ngIf="col.code=='transId' && col.active" class="text-left ui-resizable-column" style="overflow-wrap: anywhere;white-space: pre-line">
                  <!-- <a [routerLink]="['/risk-dispute-management/dispute/', data.id]"
                    [queryParams]="generateParams()" id="{{'a-transId-' + data.transactionId}}">{{data.transactionId}}</a> -->
                  <a [routerLink]="" (click)="openDetailDialog(data.id)" style="color: blue">{{data.transactionId}}</a>
                </td>
                <td *ngIf="col.code=='orderRef' && col.active" class="text-left ui-resizable-column" style="overflow-wrap: anywhere;white-space: pre-line">
                  {{data.orderReference}}
                </td>
                <td *ngIf="col.code=='merchantTransRef' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  {{data.merchantTransactionReference}}
                </td>
                <td *ngIf="col.code=='channel' && col.active" class="text-left ui-resizable-column">
                  {{convertChannel(data.paygate)}}
                </td>
                <td *ngIf="col.code=='acq' && col.active" class="text-left ui-resizable-column">
                  {{data.acquirerDownload}}
                </td>
                <td *ngIf="col.code=='cardType' && col.active" class="text-left ui-resizable-column">
                  {{convertCardType(data.cardType, data.paygate)}}
                </td>
                <td *ngIf="col.code=='cardNumber' && col.active" class="text-left ui-resizable-column">
                  {{data.cardNumber}}
                </td>
                <td *ngIf="col.code=='authCode' && col.active" class="text-left ui-resizable-column">
                  {{data.authorisationCode}}
                </td>
                <td *ngIf="col.code=='transactionAmount' && col.active" class="text-right ui-resizable-column">
                  {{(data.transactionAmount | CurrencyPipe: data.transactionCurrency) + " " + data.transactionCurrency}}
                </td>
                <td *ngIf="col.code=='transactionDate' && col.active" class="text-left ui-resizable-column">
                  {{formatDate(data.transactionDate, 'dd/MM/yyyy hh:mm a')}}
                </td>
                <td *ngIf="col.code=='transactionType' && col.active" class="text-left ui-resizable-column">
                  {{data.transactionType}}
                </td>
                <td *ngIf="col.code=='transactionState' && col.active" class="text-left ui-resizable-column">
                  {{data.transactionStatus}}
                </td>
                <td *ngIf="col.code=='refundAmount' && col.active" class="text-right ui-resizable-column">
                  {{(data.refundAmount | CurrencyPipe: data.disputeCurrency) + " " + data.transactionCurrency}}
                </td>
                <td *ngIf="col.code=='disputeAmount' && col.active" class="text-right ui-resizable-column">
                  <span [class.hidden]="data.editDisputeAmount" (click)="clickInputText(data, 'disputeAmount')">
                    {{(data.disputeAmount | CurrencyPipe: data.disputeCurrency) + " " + data.disputeCurrency}}
                    <img class="btn-edit" src="assets/img/pencil.svg">
                  </span>
                  <span [class.hidden]="!data.editDisputeAmount">
                    <input #input_disputeAmount pInputText type="text" class="edit-text" 
                    currencyMask [options]="{ align: 'right', prefix: '', thousands: ',', precision: data.disputeCurrency=='VND'? 0: 2, decimal: '.' }"
                    [(ngModel)]="data.disputeAmountTemp" (blur)="blurInputText(data, 'disputeAmount')"
                    (keydown.enter)="blurInputText(data, 'disputeAmount')" (keydown.esc)="blurInputText(data, 'disputeAmount')"/>
                    {{' ' + data.disputeCurrency}}
                  </span>
                </td>
                <td *ngIf="col.code=='disputeReason' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.disputeReason" [options]="listReasonEdit" appendTo="body" placeholder=""
                      (onChange)="editDropdown(data, 'disputeReason')" [filter]="true" filterBy="label"
                      >
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                    </p-dropdown>
                </td>
                <td *ngIf="col.code=='disputeCode' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  <!-- {{data.codeDownload}} -->
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.disputeCode" [options]="data.disputeCodeList" appendTo="body" placeholder=""
                  (onChange)="editDropdown(data, 'disputeCode')" [filter]="true" filterBy="label" optionValue="value" optionLabel="label">
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </td>
                <td *ngIf="col.code=='disputeStage' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.disputeStage" [options]="listDisputeStage" appendTo="body" placeholder=""
                  (onChange)="editDropdown(data, 'disputeStage')" [filter]="true" filterBy="label" >
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </td>
                <td *ngIf="col.code=='outcome' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.outcome" [options]="listOutComeEdit" appendTo="body" placeholder=""
                  (onChange)="editDropdown(data, 'outcome')" [filter]="true" filterBy="label" >
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </td>
                <td *ngIf="col.code=='fraudInves' && col.active" class="text-left ui-resizable-column">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.fraudInves" [options]="fraudInvesListEdit" appendTo="body" placeholder=""
                  (onChange)="editDropdown(data, 'fraudInves')" [filter]="true" filterBy="label" >
                    <ng-template let-option pTemplate="item">
                      <div class="p-multiselect-representative-option" class="txt-filter">
                        <span class="p-ml-1">{{option.label}}</span>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </td>
                <td *ngIf="col.code=='caseId' && col.active" class="text-center ui-resizable-column td-case-id"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                    <div *ngIf="isKbank(data)">
                    <input class="input-caseId" id="input-caseId"
                        [(ngModel)]="data.sCaseID" />
                    <img class="action-icon" src="assets/img/pencil.svg"
                    (click)="editCaseId($event)">
                    </div>
                </td>
                <td *ngIf="col.code=='file' && col.active" class="text-left ui-resizable-column upload-file">
                    <div *ngIf="isKbank(data)" class="d-flex upload-file-container">
                        <input type="file" [id]="'upload'+i" hidden accept="{{allowedExtensionStr}}" (change)="onFilechange($event, data)"
                            (click)="clearFileAfterUpload($event)" />
                        <label [for]="'upload'+i">
                            <i class="pi pi-upload"></i>
                        </label>
                        <label for="" *ngIf="data.fileName && data.fileName != ''">
                            <i class="pi pi-trash" (click)="removeFile(data)">
                            </i>
                        </label>
                    </div>
                </td>
                <td *ngIf="col.code=='fileStatus' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                    <div *ngIf="isKbank(data)">
                    <div *ngIf="data?.nFileID != null && data?.nFileID != ''">
                        {{'Đã gửi'}}
                    </div>
                    <div *ngIf="(data.fileName != '') && !data?.nFileID">
                        {{'Chưa gửi'}}
                    </div>
                    <div *ngIf="(data.fileName == '') && !data?.nFileID">
                        {{'Chưa có file'}}
                    </div>
                    </div>
                </td>

                <td *ngIf="col.code=='disputeFileFromIssuers' && col.active" class="text-left ui-resizable-column"
                  style="overflow-wrap: anywhere;white-space: pre-line">
                  <p-dropdown class="dropdown-edit" [(ngModel)]="data.sftpAppleFileFromIssuers" [options]="listIssuers" appendTo="body"
                    (onChange)="editDropdown(data, 'sftpAppleFileFromIssuers')" [filter]="true" filterBy="label" placeholder=" ">
                      <ng-template let-option pTemplate="item">
                        <div class="p-multiselect-representative-option" class="txt-filter">
                          <span class="p-ml-1">{{option.label}}</span>
                        </div>
                      </ng-template>
                  </p-dropdown>
                </td>
                
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td [attr.colspan]="33" class="text-center empty_results">
                No Results Has Been Shown
              </td>
            </tr>
          </ng-template>
        </p-table>

      </div>
    </div>
  </div>


</div>