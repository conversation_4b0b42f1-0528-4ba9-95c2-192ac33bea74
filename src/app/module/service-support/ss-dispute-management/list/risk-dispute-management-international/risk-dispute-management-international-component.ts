import { DatePipe } from '@angular/common';
import { Component, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { Globals } from '@core/global';
import { DataService } from '@service/data.service';
import { InternationalService } from '@service/international.service';
import { LocalService } from '@service/local-service.service';
import { SSDisputeManagementService } from '@service/ss-dispute-management.service';
import { SSTransManagementService } from '@service/ss-trans-management.service';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { DisputeManagement } from 'app/model/dispute-management';
import { ToastrService } from 'ngx-toastr';
import { LazyLoadEvent } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { Observable, Subscription } from 'rxjs';
import 'rxjs/add/observable/zip';
import {
    disputeStatusList,
    fileStatusList,
    listDisputeStage,
    listFraudInvesSearch,
    listOutcomeSearchRiskINT,
    listReasonRiskQT,
    merchantChannelList,
    pageList,
    pageSizeList,
    transCurrencyList,
    TransSearchACQList,
    transStateList,
    transTypeList,
    disputeFromFileIssuers
} from '../../dispute-management-constants';
import { DisputeUtils } from '../../dispute-utils';
import { SSDisputeManagementSearch } from '../../search/ss-dispute-management-search.component';
import { DisputeColumnDisplayComponent } from '../column-display/dispute-column-display.component';
import { DisputeConfirmModalComponent } from '../dispute-detail/confirm-modal/confirm-modal.component';
import { DisputeDetailComponent } from '../dispute-detail/dispute-detail-component';
import { SendEmailDisputeComponent } from '../dispute-detail/send-email-modal/email-modal.component';
import { CreateCSVFileModalComponent } from './create-csv-file-dialog/create-csv-file-modal.component';
import { ShareDisputeInputDialogComponent } from '../share-dispute-input-dialog/share-dispute-input-dialog.component';

@Component({
    selector: 'risk-dispute-management-international-component',
    templateUrl: './risk-dispute-management-international-component.html',
    styleUrls: ['./risk-dispute-management-international-component.scss'],
    providers: [SSTransManagementService, DialogService, SSDisputeManagementService]
})

export class RiskDisputeInternationalManagementComponent implements OnInit, OnDestroy {
    mapTimeout: Map<number, any> = new Map(); // lưu timeout, delay rồi gọi api lưu dispute

    public title = "Risk - International Dispute";
    public pageSize: number;
    public sub: Subscription;
    public resultsLength = 0;
    public loading: boolean;
    public data: Array<any>;
    private offsetHeight = 120;
    public flexScrollHeight = '200px';
    public showFormSearch = true;
    public transectionIdDetail;
    // public disputeStatusList: Array<any> = [];
    public disputeStatusList = disputeStatusList;
    public disputeStatusSelected;
    public fraudInvesList = listFraudInvesSearch;
    public fraudInvesListEdit = [{label: '', value: ''}, ...listFraudInvesSearch.slice(1)];
    public fraudInvesSelected;
    public cardTypeList: Array<any> = [];
    public cardTypeSelected;
    public disputeReason: any;
    public disputeCode: string;
    public disputeAmount: any;
    public outcome: any;
    public disputeStage: any;
    listDisputeReason: any[];
    public partnerName: string;
    // public acquirerList = TransSearchACQList;
    public acquirerList: Array<any> = [];
    public acquirerSelected;
    public merchantChannelSelected;
    public merchantId: string;
    public invoiceId: string;
    public transId: string;
    public orderRef: string;
    public merchantTransRef: string;
    public qrId: string;
    public cardSelected;
    public cardNumber: string;
    public originalAmount: string;
    public transAmount: string;
    public authCode: string;
    private delayTimer: any;
    public selectedDisputes: any[];
    public checkWaitSendList = false;
    public checkWaitAdviseList = false;
    public checkWaitReopenList = false;
    public checkWaitRemindList = false;
    public checkEnableSendToBank = false;
    public checkEnableDelete = false;
    public channel;
    disputeDetail: DisputeManagement;
    public data_history = [];
    listDisputeStage: any[] = listDisputeStage;
    listDisputeCodeTotal: any[];
    public originalDispute: any;
    public dataEmail;
    private columnDisplayRef: any;
    public numberColumn: any;
    public coLumnSelected: any;
    public coLumnSelectedArray: Array<any> = [];
    public cols: any[];
    public closeMatch;
    public pageSizeList = pageSizeList;
    public pageList = pageList;
    public page = 1;
    public fileStatusList = fileStatusList;
    public fileStatus:any;
    public listReason = listReasonRiskQT;
    public listReasonEdit = [{label: '', value: ''}, ...listReasonRiskQT];
    public listIssuers = [...disputeFromFileIssuers];
    public listOutCome = listOutcomeSearchRiskINT;
    public listOutComeEdit = listOutcomeSearchRiskINT;
    public transStateList = transStateList;
    public merchantChannelList = merchantChannelList;
    public transTypeList: any[] = transTypeList;
    public transTypeSelected;
    public transStateSelected;
    
    // For ShareDisputeInputDialog
    public listBusinessCategory: any[] = [];


    public listDisputeStageSearch;
    public mapDisputeStage;
    public mapReason;
    public mapOutcome;
    public mapDisputeCode;
    @ViewChild(SSDisputeManagementSearch, { static: true }) searchForm: SSDisputeManagementSearch;
    channel1: any;
    public transCurrency: Array<any> = [];
    public disputeCurrency: Array<any> = [];
    public transCurrencyList = transCurrencyList;
    public listOnepayPic: any[] = [];
    public listOperator: any[] = [];
    public onepayPicSelected;
    public department;
    public navigateUrl;

    files: File[] = [];
    fileNames: string = "";
    allowedExtensionArr = ['.zip'];
    allowedExtensionStr = this.allowedExtensionArr.join(',');
    invalidExtArr = [];

    isBack:string="";
    disputeCodeResponse: any[] = [];
    public RISK_DISPUTE_INT_ROLE = 'risk_dispute_international';

    public enableCreateCSV: boolean = false;
    public appleMerchants: any;

    public showUpdateDispute: boolean = false;

    constructor(
        private ssTranService: SSTransManagementService
        , private ssDisputeService: SSDisputeManagementService
        , private confirmService: ConfirmService
        , private internationalService: InternationalService
        , public datepipe: DatePipe
        , public global: Globals
        , private route: ActivatedRoute
        , private router: Router
        , private toastr: ToastrService
        , public datePipe: DatePipe
        , public dialogService: MatDialog
        , public dialogService2: DialogService
        , public dialogEmailService: DialogService
        , private localService: LocalService
        , private disputeUtil: DisputeUtils
        , private sSDisputeManagementService: SSDisputeManagementService
        , private fileDataService: DataService
        , public dialogPrime: DialogService
    ) {
        this.disputeDetail = new DisputeManagement();
        this.internationalService.getListAcquirer().subscribe(data => {
            const acquirers = data ? data.map(m => ({
                label: `${m.acquirerName}`,
                value: `${m.acquirerId}`
            })) : [];

            const additional = [
                { label: 'Sacombank', value: 'Sacombank' },
                { label: 'KBank', value: 'KBank' },
                { label: 'OnePay', value: 'OnePay' }
            ];

            this.acquirerList = [
                ...acquirers,
                ...additional
            ];
        });

    }

    ngOnInit() {
        this.department = 'RISK_MANAGEMENT';
        this.navigateUrl = ['/risk-dispute-management/international'];

        this.cols = this.disputeUtil.getSavedColumns(this.department, 'QT');
        this.flexScrollHeight = (window.innerHeight - this.offsetHeight) + 'px';
        this.mapDisputeStage = new Map(listDisputeStage.map(item => [(Number)(item.value), item.label]));
        this.listDisputeStageSearch = listDisputeStage.filter(item => item.label && item.value);
        this.mapOutcome = new Map(this.listOutCome.map(item => [(Number)(item.value), item.label]));
        this.mapReason = new Map(this.listReason.map(item => [(Number)(item.value), item.label]));

        // khi openDetailDialog sau do luu dispute, cap nhat du lieu ra man list
        this.disputeUtil.saveDispute$.subscribe(dispute => {
            console.log('new value comming', dispute);
            this.setRow(dispute.id, dispute);
        });
    }

    setRow(id, dispute) {
        let row = this.data.find(e => e.id == id);
        if (!row) return;

        row.disputeDate = dispute.disputeDate || row.disputeDate;
        row.dueDate = dispute.dueDate || row.dueDate;
        row.disputeAmount = dispute.disputeAmount || row.disputeAmount;
        row.disputeCurrency = dispute.disputeCurrency || row.disputeCurrency;
        row.disputeStage = dispute.disputeStage || row.disputeStage;
        row.disputeCode = dispute.disputeCode || row.disputeCode;
        row.disputeReason = dispute.disputeReason || row.disputeReason;
        if(dispute.outcome) {
            row.disputeStatus = 'resolved';
            row.outcome = dispute.outcome
        }
        row.fraudInves = dispute.fraudInves || row.fraudInves;
        row.onepayPic = dispute.onepayPic || row.onepayPic;
        row.refundAmount = dispute.refundAmount || row.refundAmount; 
        row.sftpAppleFileFromIssuers = this.disputeUtil.formatDataFileFrom(dispute.sftpAppleFileFromIssuers) || this.disputeUtil.formatDataFileFrom(row.sftpAppleFileFromIssuers);
        this.setRowTempValues(row);
    }

    @HostListener('window:resize', ['$event'])
    onResize(event) {
        this.flexScrollHeight = (event.target.innerHeight - this.offsetHeight) + 'px';
    }

    ngOnDestroy() {
    }

    checkSelectedDisputesByAttribute(attribute: string): { isSame: boolean, value?: any } {
        if (!this.selectedDisputes || this.selectedDisputes.length === 0) return { isSame: false };

        let valueFirstItem = this.selectedDisputes[0][attribute] || '';

        for (let item of this.selectedDisputes) {
            if (item[attribute] !== valueFirstItem) {
                return { isSame: false };
            }
        }
        return { isSame: true, value: valueFirstItem };
    }

    checkSelectedDisputesByChannel(): { isSame: boolean, value?: any, isDOM_APP: boolean, isINT: boolean } {
        if (!this.selectedDisputes || this.selectedDisputes.length === 0) return { isSame: false, isDOM_APP: true, isINT: true };

        let valueFirstItem = this.convertChannel(this.selectedDisputes[0].paygate);
        let isDOM_APP = valueFirstItem === 'DOM' || valueFirstItem === 'APP';
        let isINT = valueFirstItem === 'INT';

        for (let item of this.selectedDisputes) {
            let value = this.convertChannel(item.paygate);
            if (value !== valueFirstItem) {
                if (valueFirstItem === 'DOM' || valueFirstItem === 'APP') {
                    isDOM_APP = (value === 'DOM' || value === 'APP');
                    isINT = !isDOM_APP;
                } else { // === 'INT'
                    isINT = value === 'INT';
                    isDOM_APP = !isINT;
                }
                return { isSame: false, isDOM_APP: isDOM_APP, isINT: isINT };
            }
        }
        return { isSame: true, value: valueFirstItem, isDOM_APP: isDOM_APP, isINT: isINT };
    }

    checkSelectedDisputesByAccquirer(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('acquirer');
    }

    checkSelectedDisputesByMerchantId(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('merchantId');
    }

    checkSelectedDisputesByTransactionType(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('transactionType');
    }

    checkSelectedDisputesByTransactionStatus(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('transactionStatus');
    }

    checkSelectedDisputesByDisputeStatus(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('disputeStatus');
    }

    checkSelectedDisputesByDisputeCode(): { isSame: boolean, value?: any } {
        return this.checkSelectedDisputesByAttribute('disputeCode');
    }

    handleCheckbox(dataItem: any) {
        let checkListMerchantId = this.checkSelectedDisputesByMerchantId();
        let checkListChannel = this.checkSelectedDisputesByChannel();
        let checkListTransactionType = this.checkSelectedDisputesByTransactionType();
        let checkListTransactionStatus = this.checkSelectedDisputesByTransactionStatus();
        let checkListDisputeStatus = this.checkSelectedDisputesByDisputeStatus();
        let checkListDisputeCode = this.checkSelectedDisputesByDisputeCode();
        console.log(this.selectedDisputes);
        console.log('checkListMerchantId', checkListMerchantId);
        console.log('checkListChannel', checkListChannel);
        console.log('checkListTransactionType', checkListTransactionType);
        console.log('checkListTransactionStatus', checkListTransactionStatus);
        console.log('checkListDisputeStatus', checkListDisputeStatus);
        console.log('checkListDisputeCode', checkListDisputeCode);

        this.checkWaitSendList =
            checkListDisputeStatus.isSame && checkListDisputeStatus.value === 'created'
            && checkListMerchantId.isSame
            && (checkListChannel.isDOM_APP ? checkListChannel.isSame : true)
            && checkListTransactionType.isSame
            && checkListTransactionStatus.isSame
            && (checkListChannel.isINT ? checkListDisputeCode.isSame : true);

        this.checkEnableDelete = 
            checkListDisputeStatus.isSame && checkListDisputeStatus.value === 'created';

        this.checkWaitRemindList =
            checkListDisputeStatus.isSame
            && checkListDisputeStatus.value === 'need_merchant_response';

        this.checkWaitReopenList =
            checkListDisputeStatus.isSame
            && checkListDisputeStatus.value === 'resolved';

        this.checkWaitAdviseList = false; // để lại sau

        this.checkEnableSendToBank = this.selectedDisputes?.length && this.selectedDisputes.every(row => this.disputeUtil.isKbank(row));
        
        if (this.selectedDisputes?.length > 0) {
            this.selectedDisputes.forEach(dispute => {
                if (!dispute.outcome || !dispute.note) {
                    this.enableCreateCSV = false;
                    return this.enableCreateCSV;
                }
            });
            this.enableCreateCSV = this.selectedDisputes.every(item => {
                return this.isMerchantApple(item.merchantId, this.appleMerchants?.merchant_id) 
                    && item.merchantId == this.selectedDisputes[0].merchantId && item.dDueDate 
                    && item.disputeCode && item.disputeStage
            });
            this.checkShowUpdateDisputeBtn();
        } else {
            this.enableCreateCSV = false;
            this.showUpdateDispute = false;
        }
    }

    isMerchantApple(merchant: string, merchantsApple: string) {
        if (!merchantsApple || merchantsApple.length === 0 || !merchant || merchant.length === 0) {
            return false;
        }

        return merchantsApple.indexOf(merchant) > -1;
    }

    adviseHandler() {
        const disputeIdsArray = [];
        const transactionIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
            transactionIdsArray.push(this.selectedDisputes[i].transactionId);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'transactionIds': transactionIdsArray.toString(),
            'disputeStatus': 'need_merchant_response'
        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {

        });
    }

    sendDisputeHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, false, true);
    }

    reopenHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, true, false);
    }

    reopenHandlerActiveButton() {
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'disputeStatus': 'need_merchant_response',

        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {
            if (res && res.code) {
                this.selectedDisputes = [];
                this.onSubmit();
                this.toastr.success('Successfully re-opened dispute', 'Successfully');
            }
        });
    }

    remindHandler() {
        this.transectionIdDetail = this.selectedDisputes.map(dispute => dispute.transactionId).join(',');
        this.dataContent(true, false, false);
    }

    remindHandlerActiveButton() {
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
            'disputeStatus': 'need_merchant_response'
        }
        this.ssDisputeService.updateDisputeStatus(disputeIdsArray.toString(), body).subscribe(res => {
            if (res && res.code) {
                this.selectedDisputes = [];
                this.onSubmit();
                this.toastr.success('Successfully reminded dispute', 'Successfully');
            }
        });
    }

    ////////
    approveAndSendToMerchant(reopen, send) {

        if (send) {
            if (this.disputeDetail && (this.disputeDetail.onepayPic == '' || this.disputeDetail.onepayPic == undefined)) {
                this.toastr.error('Please input required information.', 'Error');
                return;
            }
        }
        let body = {
            "dispute_ids": this.selectedDisputes.map(e => e.id).join(","),
            "transaction_id": this.disputeDetail.transactionId,
            "due_date": this.datePipe.transform(this.disputeDetail.dueDate, 'dd/MM/yyyy'),
            "merchant_name": this.disputeDetail.merchantName,
            "dispute_stage": this.disputeDetail.disputeStage,
            "paygate": this.disputeDetail.paygate,
            "dispute_code": this.disputeDetail.disputeCode,
            "order_ref": this.disputeDetail.orderReference ? this.disputeDetail.orderReference : '',
            "authorisation_code": this.disputeDetail.authorisationCode ? this.disputeDetail.authorisationCode : '',
            "merchant_id": this.disputeDetail.merchantId,
            "dispute_date": this.datePipe.transform(new Date(), 'dd/MM/yyyy'),
            "card_number": this.disputeDetail.cardNumber,
            "transaction_date": this.datePipe.transform(this.disputeDetail.transactionDate, 'dd/MM/yyyy HH:mm:ss'),
            "merchant_transaction_ref": this.disputeDetail.merchantTransactionReference,
            "transaction_amount": this.disputeDetail.transactionAmount,
            "dispute_amount": this.disputeDetail.disputeAmount

        };

        const countHistory = this.data_history.filter((item) => item.parentId !== 0).length;
        if (countHistory !== 0) {
            const ref = this.dialogService.open(DisputeConfirmModalComponent, {
                data: this.data_history,
            });
            ref.afterClosed().subscribe(result => {
                console.log('The dialog was closed');
                if (result) {
                    this.ssDisputeService.loadEmailContent(body).subscribe(resp => {
                        // if (resp && resp.nStatus === 200) {
                        let dataEmail = {
                            'subject': resp.email_subject,
                            'emailTo': this.disputeDetail.sendDisputeTo,
                            'emailContent': resp.email_content,
                            'management': false,
                        }
                        const emailRef = this.dialogEmailService.open(SendEmailDisputeComponent, {
                            header: 'Send email',
                            contentStyle: { "min-height": "500px", "overflow": "auto" },
                            baseZIndex: 10000,
                            data: JSON.stringify(dataEmail),
                        });


                        emailRef.onClose.subscribe((data: any) => {
                            if (data && data.management) {
                                if (data.management && !reopen) {
                                    this.remindHandlerActiveButton();
                                    this.sendEmailForm("sendDispute", data);
                                }
                                if (data.management && reopen) {
                                    this.reopenHandlerActiveButton();
                                    this.sendEmailForm("sendDispute", data);
                                }
                            }
                        });
                        return;
                    });

                }
            });
        } else {
            this.ssDisputeService.loadEmailContent(body).subscribe(resp => {
                // if (resp && resp.nStatus === 200) {
                let dataEmail = {
                    'subject': resp.email_subject,
                    'emailTo': this.disputeDetail.sendDisputeTo,
                    'emailContent': resp.email_content,
                    'management': false,
                }
                const emailRef = this.dialogEmailService.open(SendEmailDisputeComponent, {
                    header: 'Send email',
                    contentStyle: { "min-height": "500px", "overflow": "auto" },
                    baseZIndex: 10000,
                    data: JSON.stringify(dataEmail),
                });

                emailRef.onClose.subscribe((data: any) => {
                    if (data.management && !reopen) {
                        this.remindHandlerActiveButton();
                        this.sendEmailForm("sendDispute", data);
                    }
                    if (data.management && reopen) {
                        this.reopenHandlerActiveButton();
                        this.sendEmailForm("sendDispute", data);
                    }
                });

                return;
            });
        }
    }

    formatDate2(inputDate: string): string {
        var outputDate = '';
        if (inputDate) {
            outputDate = this.datePipe.transform(new Date(inputDate), 'dd/MM/yyyy hh:mm a');
        }
        return outputDate;
    }

    formatDate3(inputDate: string): string {
        var outputDate = '';
        if (inputDate) {
            outputDate = this.datePipe.transform(new Date(inputDate), 'dd/MM/yyyy');
        }
        return outputDate;
    }

    convertDisputeStatus(status) {
        if (status == 'created') return 'Created';
        else if (status == 'need_merchant_response') return 'Need Merchant Response';
        else if (status == 'waiting_for_onepay_review') return 'Waiting for OnePay review';
        else if (status == 'resolved') return 'Resolved';
        else if (status == 'dispute_reminded') return 'Dispute Reminded';
    }

    sendEmailForm(type, data) {
        let body = {
            dispute_ids: this.selectedDisputes.map(e => e.id).join(",")
        };
        this.ssDisputeService.sendDispute(body).subscribe(res => {
            const bodyEmail = {
                "email_to": data.to ? data.to : '',
                "email_cc": data.cc ? data.cc : '',
                "email_subject": data.subject ? data.subject : '',
                "email_content": data.content ? data.content : '',
            }
            this.ssDisputeService.sendEmailToMerchant(bodyEmail).subscribe(res => {
                if (res && res.nStatus === '200')
                    this.toastr.success("Successfully sent dispute", "Success");
                else
                    this.toastr.error("An internal server error", "Error");
            });
            // this.dataContent(false, true, false);
        });
    }

    dataContent(init, reopen, send) {
        this.data_history = [];
        let checkBeforeSend = true;
        let disputeIds = this.selectedDisputes.map(dispute => dispute.id.toString()).join(',');
        let body = {
            'dispute_ids': disputeIds
        }
        this.ssDisputeService.searchDisputeDetailByIds(body).subscribe(data => {
            if (data && data.list) {
                data.list.forEach(item => {
                    item['transactionDateLabel'] = item['transactionDate'] ? this.formatDate2(item['transactionDate']) : '';
                    item['disputeDateLabel'] = item['disputeDate'] ? this.formatDate2(item['disputeDate']) : '';
                    item['dueDateLabel'] = item['dueDate'] ? this.formatDate3(item['dueDate']) : '';
                    item['disputeStatus'] = item['disputeStatus'] ? this.convertDisputeStatus(item['disputeStatus']) : '';
                    this.data_history.push(item);
                });


                this.disputeDetail.transactionId = data.list.map(dispute => dispute.transactionId).join(',');

                if (init) {
                    data.list.forEach(item => {
                        if (item && item.parentId == 0) {
                            this.originalDispute = item;
                            this.disputeDetail.transactionDate = new Date(item.transactionDate);
                            this.disputeDetail.authorisationCode = item.authorisationCode;
                            this.disputeDetail.transactionAmount = item.transactionAmount;
                            this.disputeDetail.merchantName = item.merchantName;
                            this.disputeDetail.orderReference = item.orderReference;
                            this.disputeDetail.merchantTransactionReference = item.merchantTransactionReference;
                            this.disputeDetail.merchantId = item.merchantId;
                            this.disputeDetail.cardNumber = item.cardNumber;
                            this.disputeDetail.id = item.id;
                            this.disputeDetail.disputeSender = item.disputeSender ? item.disputeSender : item.acquirer + ' ' + this.convertChannel(item.paygate);
                            this.disputeDetail.onepayPic = item.onepayPic;
                            this.disputeDetail.paygate = item.paygate;
                            if (item.sendDisputeTo) {
                                this.disputeDetail.sendDisputeTo = item.sendDisputeTo;
                            } else {

                                this.sSDisputeManagementService.emailListByPartner(item.partnerId, item.paygate).subscribe(emailResponse => {
                                    this.disputeDetail.sendDisputeTo = emailResponse.emailList;
                                });

                            }
                            this.disputeDetail.merchantRespond = item.merchantRespond;
                            this.disputeDetail.businessCategory = item.businessCategory;
                            this.disputeDetail.merchantGroup = item.merchantGroup;
                            this.disputeDetail.disputeStage = item.disputeStage;
                            this.disputeDetail.disputeReason = item.disputeReason;
                            this.disputeDetail.disputeCode = item.disputeCode;

                            this.disputeDetail.disputeAmount = item.disputeAmount;
                            this.disputeDetail.disputeCurrency = item.disputeCurrency ? item.disputeCurrency : item.transactionCurrency;
                            this.disputeDetail.dueDate = item.dueDate;
                            this.disputeDetail.outcome = reopen ? '' : item.outcome;
                            this.disputeDetail.refNumber = item.refNumber;
                            this.disputeDetail.note = item.note;
                            this.disputeDetail.department = item.department;

                            if (!this.disputeUtil.checkRequiredFieldsBeforeSend(this.disputeDetail)) {
                                checkBeforeSend = false;
                            }
                        }
                    });

                    if (checkBeforeSend) {
                        this.approveAndSendToMerchant(reopen, send);
                    } else {
                        this.toastr.error('Please input required information.', 'Error');
                        return;
                    }
                }
            }
        });
    }

    deleteDispute() {
        console.log(this.selectedDisputes);
        const disputeIdsArray = [];
        for (let i = 0; i < this.selectedDisputes.length; i++) {
            disputeIdsArray.push(this.selectedDisputes[i].id);
        }
        const body = {
            'ids': disputeIdsArray.toString(),
        }
        this.confirmService.build()        
            .title('Warning')
            .message('Are you sure that you want to delete these disputes?')
            .no('No')
            .yes('Yes')
            .confirm()
            .subscribe(agree => {
                if (agree) {
                    this.ssDisputeService.deleteDispute(body).subscribe(res => {
                        console.log('deleteDispute res: ', res);
                        if (res && res.code) {
                            this.selectedDisputes = [];
                            this.checkEnableDelete = false;
                            this.toastr.success('Delete disputes successfully', 'Successfully');
                            this.onSubmit();
                        }
                    });
                }
            })
        ;

    }

    clearFilters(){
        this.disputeStatusSelected = [];
        this.fraudInvesSelected = [];
        this.merchantId = '';
        this.transId = '';
        this.orderRef = '';
        this.merchantTransRef = '';
        this.onepayPicSelected = [ {id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name}];
        this.cardTypeSelected = [];
        this.cardNumber = '';
        this.partnerName = '';
        this.page = 1;
        this.transCurrency = [];
        this.disputeCurrency = [];
        this.outcome = [];
        this.disputeReason = [];
        this.disputeStage = [];
        this.fileStatus = [];
        this.acquirerSelected = [];
        this.merchantChannelSelected = [];
        this.transStateSelected = [];
        this.transTypeSelected = [];
        this.authCode = '';
        this.filter();
    }

    filter() {
        var self = this;
        clearTimeout(this.delayTimer);
        this.delayTimer = setTimeout(function () {
            return self.searchData().subscribe(responses => {
                self.resultsLength = responses.totalItems;
                self.data = responses.list;
                self.data.forEach(row => self.setRowTempValues(row));
                self.initPage(self.resultsLength);

                //ver1: mỗi khi bấm filter 
                //thì phải upload lại file
                self.files = [];
            });
        }, 3000);
    }

    searchData() {
        this.router.navigate(this.navigateUrl, { queryParams: this.generateParams() });
        this.validateFilterColumn();
        return this.ssDisputeService.searchDisputeV2(this.generateParams());
    }

    getOnepayPicName(id) {
        let found;
        if (this.listOnepayPic) {
            found = this.listOnepayPic.find(e => e['id'] == id)
        }
        return found? found['name'] : id;
    }

    disputeStatus(status) {
        if (status == 'created') return 'Created';
        else if (status == 'need_merchant_response') return 'Need Merchant Response';
        else if (status == 'waiting_for_onepay_review') return 'Waiting for OnePay review';
        else if (status == 'resolved') return 'Resolved';
        else if (status == 'dispute_reminded') return 'Dispute Reminded';
    }

    download() {
        return this.ssDisputeService.downloadDispute(this.generateParamsDownload()).subscribe(response => {
            this.global.downloadEmit.emit(true);
        });
    }

    editCaseId(event: any) {
        event.target.previousElementSibling.focus();
    }

    validateFileUpload() {
        this.invalidExtArr = [];

        for (let i = 0; i < this.files.length; i++) {
            let file = this.files[i];
            let filename = file.name;
            let ext = this.getFileExtension(filename);
            if (!this.allowedExtensionArr.includes(ext)) {
                console.log('File extension not allowed: ', ext);
                this.invalidExtArr.push(ext);
            }
        }

        if (this.invalidExtArr.length == 0) {
            return true;
        }
        return false;
    }

    onFilechange(event: any, row: any) {
        console.log('onFilechange');
        if (event.target.files?.length) {
            //mỗi transaction chỉ đc upload 1 file
            let newFile: File = event.target.files[0];

            let valid = true;
            if (!this.validateFile(newFile)) {
                valid = false;
                return;
            }

            if (valid) {
                // this.files = this.files.concat(newFile);
                let fileName = newFile.name;
                let filePath = (window.URL || window.webkitURL).createObjectURL(newFile);

                console.log(fileName, filePath);
                row.fileName = fileName;
                row.filePath = filePath;
            }
        }
    }

    clearFileAfterUpload(event:any){
        const element = event.target as HTMLInputElement;
        element.value = '';
    }

    getFileExtension(filename: string) {
        let arr = filename.split('.');
        if (arr.length <= 1) {
            return "";
        }
        return "." + arr.pop();
    }

    validateFile(newFile) {
        let ext = this.getFileExtension(newFile.name);

        if (!this.allowedExtensionArr.includes(ext)) {
            let msg = 'File extension not allowed: ' + ext;
            this.toastr.error(msg);
            return false;
        }

        for (let oldFile of this.files) {
            if (oldFile.name == newFile.name) {
                let msg = 'Duplicate file name: ' + oldFile.name;
                this.toastr.error(msg);
                return false;
            }
        }

        return true;
    }

    removeFile(row: any) {
        row.fileName = undefined;
        row.filePath = undefined;
    }

    validateCaseId(){
        let valid = true;
        this.data.forEach(it=> {
            if(it.fileName && !it.sCaseID){
                this.toastr.warning("Case ID cannot be empty", "Error");
                valid = false;
            }
        })
        return valid;
    }

    validateEmptyFile(){
        if(this.files.length > 0){
            return true;
        }
        else{
            this.toastr.warning("Choose file upload", "Error");
            return false;
        }
    }

    async sendToBank() {
        this.files = [];
        for (let row of this.selectedDisputes) {
            let res = await this.fileDataService.getFileFromTmp(row.filePath).toPromise();
            console.log("logg promise getFileBlob", res);
            const fileFromBlob = new File([res], row.fileName, { type: res.type, });
            console.log("file from blob", fileFromBlob);
            this.files = this.files.concat(fileFromBlob);
        }
        console.log('files', this.files);
        //chạy sau
        if(this.validateCaseId() && this.validateEmptyFile()){
            let res:any = await this.sSDisputeManagementService.sendToBank(this.files, this.global.activeProfile.email, this.selectedDisputes).toPromise();
            if (res && res.status && res.status == 'success') {
                this.files = [];
                this.fileDataService.clearFileData();
                this.onSubmit();
                this.checkEnableSendToBank = false;
                this.selectedDisputes = [];
                this.toastr.success("Successfully sent file to bank", "Success");
            }
            else{
                this.toastr.error("An internal server error", "Error");
            }
        }
    }

    onSubmit() {
        return this.searchData().subscribe(responses => {
            this.resultsLength = responses.totalItems;
            this.initPage(this.resultsLength);
            if (responses.list && responses.list.length > 0) {
                const array = [];
                responses.list.forEach(item => {
                    this.setRowTempValues(item);
                    item.active = true;
                    if (!item.disputeCurrency) {
                        item.disputeCurrency = item.transactionCurrency;
                    }
                    //thêm trường fileName
                    item.fileName = '';
                    //thêm caseId
                    if(!item.sCaseID){
                        item.sCaseID = "";
                    }
                    //thêm noteDisputeCase
                    if(!item.noteDisputeCase){
                        item.noteDisputeCase = "";
                    }
                    //
                    if(this.addDataWhenFilterFile(item) != null){
                        array.push(item);
                    }
                    if (item.sftpAppleFileFromIssuers) {
                        item.sftpAppleFileFromIssuers = this.disputeUtil.formatDataFileFrom(item.sftpAppleFileFromIssuers)
                    }
                });
                this.data = array;
                // console.log(this.data);
                this.fileDataService.sendListDispute(this.data);
                this.checkShowUpdateDisputeBtn();
            }
        });
    }

    onChangePage() {
        return this.searchData().subscribe(responses => {
            this.resultsLength = responses.totalItems;
            this.data = responses.list;
            this.initPage(this.resultsLength);
        });
    }

    initPage(resultsLength: any) {
        if (resultsLength && resultsLength > 100) {
            let numberPage = Math.ceil(resultsLength / 100);
            this.pageList = [];
            for (let i = 1; i <= numberPage; i++) {
                this.pageList.push({
                    value: i,
                    label: i + ''
                });
            }
        } else {
            this.pageList = [];
            this.pageList.push({
                value: 1,
                label: '1'
            });
        }
    }

    addDataWhenFilterFile(itemAdd:any){
        //nếu có filter fileStatus = chưa gửi hoặc đã gửi
        if(this.fileStatus && this.fileStatus.length == 1){
            let fileStatus = this.fileStatus.map(x => x.name).join(",");
            if(fileStatus == 'Chưa gửi' && !itemAdd.nFileID){
                return itemAdd;
            }
            else if(fileStatus == 'Đã gửi' && itemAdd.nFileID && itemAdd.nFileID != ''){
                return itemAdd;
            }
            else {
                return null;
            }
        }
        else{
            return itemAdd;
        }
    }

    loadDataLocal(){
        console.log('loadDataLocal');
        let fileData = this.fileDataService.getFileData();
        console.log('fileData', fileData);
        if (!fileData) return;
        fileData.forEach(file => {
            let found = this.data.find(e => e.id == file.disputeId);
            if (found) {
                found.fileName = file.fileName;
                found.filePath = file.path;
            }
            console.log(found);
        });
        this.fileDataService.clearFileData();
    }

    initFilters(params) {
        this.acquirerSelected = params['acquirer_id'] ? this.acquirerList.filter(item => params['acquirer_id'].split(',').includes(item.value)) : [];
        this.disputeStatusSelected = params['dispute_status'] ? this.disputeStatusList.filter(item => params['dispute_status'].split(',').includes(item.value)) : [];
        this.fraudInvesSelected = params['fraud_inves'] ? this.disputeStatusList.filter(item => params['fraud_inves'].split(',').includes(item.value)) : [];
        this.merchantId = params['merchant_id'] || '';
        this.transId = params['transaction_id'] || '';
        this.orderRef = params['order_ref'] || '';
        this.partnerName = params['partner_name'] || '';
        this.merchantTransRef = params['merchant_transaction_ref'] || '';
        this.onepayPicSelected = params['pic'] ? this.listOnepayPic?.filter(item => params['pic'].split(',').includes(item.id)) : this.onepayPicSelected;
        this.cardTypeSelected = params['card_type'] ? this.cardTypeList.filter(item => params['card_type'].split(',').includes(item.value)) : [];
        this.cardNumber = params['card_number'] || '';
        this.page = params['page'] ? + params['page'] + 1 : 1;
        this.transCurrency = params['transaction_currency'] ? this.transCurrencyList.filter(item => params['transaction_currency'].split(',').includes(item.value)) : [];
        this.disputeCurrency = params['dispute_currency'] ? this.transCurrencyList.filter(item => params['dispute_currency'].split(',').includes(item.value)) : [];
        this.outcome = params['outcome'] ? this.listOutCome.filter(item => params['outcome'].split(',').includes(item.value)) : [];
        this.closeMatch = 'contains' == params["filter_type"];
        this.disputeReason = params['dispute_reason'] ? this.listReason.filter(item => params['dispute_reason'].split(',').includes(item.value)) : [];
        this.disputeStage = params['dispute_stage'] ? this.listDisputeStageSearch.filter(item => params['dispute_stage'].split(',').includes(item.value)) : [];
        this.isBack = params['isBack'];
    }

    loadLazy(event: LazyLoadEvent) {
        let params = this.route.snapshot.queryParams;
        Observable.zip(
            this.ssTranService.getDropdownCardListSS(),
            this.sSDisputeManagementService.getAllOnePayPics(this.RISK_DISPUTE_INT_ROLE),
            this.sSDisputeManagementService.getDisputeCode(),
            this.sSDisputeManagementService.getOperator(),
            this.ssDisputeService.getAppleMerchants(),
            this.sSDisputeManagementService.getBusinessCater()
        ).subscribe( arr => {
            let cardListRes = arr[0];
            let onepayPicRes = arr[1];
            let disputeCodeRes = arr[2];
            let operatorRes = arr[3];
            let businessCateRes = arr[5];

            this.listDisputeCodeTotal = this.disputeUtil.formatListDisputeCodeRisk(disputeCodeRes);
            this.mapDisputeCode = new Map(this.listDisputeCodeTotal.map(item => [(Number)(item.value), item.label]));
            this.cardTypeList = (cardListRes as Array<any>).find(item => item.value == 'QT')?.items;
            this.appleMerchants = arr[4];
            console.log('dispute cardTypeList: ', this.cardTypeList);
            
            // Load business category for ShareDisputeInputDialog
            this.listBusinessCategory = [{ label: 'Blank', value: '' }];
            if (businessCateRes.list) {
                for (let i = 0; i < businessCateRes.list.length; i++) {
                    this.listBusinessCategory.push({ 
                        label: businessCateRes.list[i].NAME, 
                        value: businessCateRes.list[i].N_ID.toString() 
                    });
                }
            }

            onepayPicRes.list.forEach(element => {
                if (element['id'] && element['name']) {
                element['id'] = element.id.toString();
                this.listOnepayPic.push(element);
                }
            });
            if (!this.listOnepayPic.find(element => element.id == this.global.activeProfile.n_id.toString())) {
                this.listOnepayPic.push({ id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name });
            }
            console.log('dispute listOnepayPic: ', this.listOnepayPic);

            operatorRes.list.forEach(element => {
                if (element['id'] && element['name']) {
                element['id'] = element.id.toString();
                this.listOperator.push(element);
                }
            })

            this.onepayPicSelected = [ {id: this.global.activeProfile.n_id.toString(), name: this.global.activeProfile.name}];
            this.searchForm.init(params, "QT");
            this.initFilters(params);
            this.loading = true;
            if (this.isBack && this.isBack == "true") {
                this.loadDataLocal();
                return;
            }
            this.searchData().subscribe(responses => {
                this.loading = false;
                this.resultsLength = responses.totalItems;
                this.initPage(this.resultsLength);
                if (responses.list && responses.list.length > 0) {
                    const array = [];
                    responses.list.forEach(item => {
                        item.active = true;
                        if (!item.disputeCurrency) {
                            item.disputeCurrency = item.transactionCurrency;
                        }
                        //thêm trường fileName
                        item.fileName = '';
                        //thêm caseId
                        if(!item.sCaseID){
                            item.sCaseID = "";
                        }
                        //thêm noteDisputeCase
                        if(!item.noteDisputeCase){
                            item.noteDisputeCase = "";
                        }
                        //
                        this.setRowTempValues(item);
                        item.sftpAppleFileFromIssuers = this.disputeUtil.formatDataFileFrom(item.sftpAppleFileFromIssuers);
                        array.push(item);
                    });
                    this.data = array;
                }
            });
        });
    }

    checkColumn(code: string): boolean {
        let col = this.cols.find(e => e.code == code);
        return col? col.active : false;
    }

    checkDuplicateList(checkArray: any, checkExists: boolean, value: any) {
        checkExists = false;
        if (checkArray.length > 0) {
            checkArray.forEach(dropdown => {
                if (dropdown.value === value) {
                    checkExists = true;
                    return checkExists;
                }
            });
        }

        return checkExists;
    }

    checkFormSearch(type: boolean) {
        this.showFormSearch = type;
    }

    generateParams() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');
        this.pageSize = this.pageSize == undefined ? 100 : this.pageSize;
        var params = {
            'back_location': 'risk-international',
            'from_date': from_date,
            'to_date': to_date,
            'dispute_status': this.disputeStatusSelected ? this.disputeStatusSelected.map(x => x.value).join(',') : '',
            'fraud_inves': this.fraudInvesSelected ? this.fraudInvesSelected.map(x => x.value).join(',') : '',
            'merchant_id': this.merchantId ? this.merchantId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchant_transaction_ref': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'channel': 'QT',
            'acquirer_id': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(',') : "",
            'pic': this.onepayPicSelected ? this.onepayPicSelected.map(x => x.id).join(',') : "",
            'card_type': this.cardTypeSelected ? this.cardTypeSelected.map(x => x.value).join(',') : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'authorisation_code': this.authCode ? this.authCode.trim() : "",
            'transaction_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'dispute_currency': this.disputeCurrency ? this.disputeCurrency.map(x => x.value).join(",") : "",
            'dispute_code': this.disputeCode ? this.disputeCode : '',
            'outcome': this.outcome ? this.outcome.map(x => x.value).join(',') : "",
            'page_size': this.pageSize == undefined ? 100 : this.pageSize,
            'page': (this.page - 1) + '',
            'filter_type': this.closeMatch? 'contains' : 'equals',
            'user_id': this.global.activeProfile.n_id,
            'department': this.department,
            'dispute_reason': this.disputeReason ? this.disputeReason.map(x => x.value).join(",") : "",
            'dispute_stage': this.disputeStage ? this.disputeStage.map(x => x.value).join(",") : "",
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'transactionState': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'partner_name': this.partnerName || '',
        }

        return params;
    }


    generateParamsDownload() {
        let from_date = this.datepipe.transform(this.searchForm.fromDate, 'dd/MM/yyyy HH:mm:ss');
        let to_date = this.datepipe.transform(this.searchForm.toDate, 'dd/MM/yyyy HH:mm:ss');

        var params = {
            'from_date': from_date,
            'to_date': to_date,
            'dispute_status': this.disputeStatusSelected ? this.disputeStatusSelected.map(x => x.value).join(',') : '',
            'fraud_inves': this.fraudInvesSelected ? this.fraudInvesSelected.map(x => x.value).join(',') : '',
            'merchant_id': this.merchantId ? this.merchantId.trim() : "",
            'transaction_id': this.transId ? this.transId.trim() : "",
            'order_ref': this.orderRef ? this.orderRef.trim() : "",
            'merchant_transaction_ref': this.merchantTransRef ? this.merchantTransRef.trim() : "",
            'channel': 'QT',
            'acquirer_id': this.acquirerSelected ? this.acquirerSelected.map(x => x.value).join(',') : "",
            'pic': this.onepayPicSelected ? this.onepayPicSelected.map(x => x.id).join(',') : "",
            'card_type': this.cardTypeSelected ? this.cardTypeSelected.map(x => x.value).join(',') : "",
            'card_number': this.cardNumber ? this.cardNumber.trim() : "",
            'authorisation_code': this.authCode ? this.authCode.trim() : "",
            'transaction_currency': this.transCurrency ? this.transCurrency.map(x => x.value).join(",") : "",
            'dispute_currency': this.disputeCurrency ? this.disputeCurrency.map(x => x.value).join(",") : "",
            'dispute_code': this.disputeCode ? this.disputeCode : '',
            'outcome': this.outcome ? this.outcome.map(x => x.value).join(',') : "",
            'page_size': this.pageSize == undefined ? 100 : this.pageSize,
            'page': (this.page - 1) + '',
            'filter_type': this.closeMatch? 'contains' : 'equals',
            'user_id': this.global.activeProfile.n_id,
            'department': this.department,
            'dispute_reason': this.disputeReason ? this.disputeReason.map(x => x.value).join(",") : "",
            'dispute_stage': this.disputeStage ? this.disputeStage.map(x => x.value).join(",") : "",
            'column_list': this.getColumnListDownload(),
            'column_active': this.getColumnActiveDownload(),
            'dispute_ids': (this.selectedDisputes && this.selectedDisputes.length)? this.selectedDisputes.map(e => e.id).join(",") : null,
            'merchantChannel': this.merchantChannelSelected ? this.merchantChannelSelected.map(x => x.value).join(",") : "",
            'transactionState': this.transStateSelected ? this.transStateSelected.map(x => x.value).join(",") : "",
            'transaction_type': this.transTypeSelected ? this.transTypeSelected.map(x => x.value).join(",") : "",
            'partner_name': this.partnerName || '',
            'file_type': 'risk-international',
            
        }

        return params;
    }

    getColumnListDownload() {
        return this.cols
            .map(e => e.code)
            .concat(['transactionCurrency', 'refundCurrency', 'disputeCurrency'])
            .concat(['channel']) /* vì dùng chung template với risk dom, thêm vào đây để backend tìm và xóa cột */
            .join(",");
    }

    getColumnActiveDownload() {
        let codeArr = this.cols.filter(e => e.active)
            .filter(e => !['channel'].includes(e.code)) /*bo cac cot nay*/
            .map(e => e.code);
        if (this.cols.find(item => item.code == 'transactionAmount' && item.active)) {
            codeArr.push('transactionCurrency');
        }
        if (this.cols.find(item => item.code == 'refundAmount' && item.active)) {
            codeArr.push('refundCurrency');
        }
        if (this.cols.find(item => item.code == 'disputeAmount' && item.active)) {
            codeArr.push('disputeCurrency');
        }
        return codeArr.join(",");
    }

    convertChannel(channel) {
        if (channel === 'QT') return 'INT';
        else if (channel === 'ND') return 'DOM';
        else return 'APP';
    }

    displayCardNo(cardNo: string, gate: string) {
        return cardNo?
            gate == 'QT'? cardNo.substring(0, 6) + "***" + cardNo.substring(cardNo.length - 4)
                : "***" + cardNo.substring(cardNo.length - 4)
            : '';
    }

    formatDate(value, format) {
        if (value && value != '') {
            return this.datepipe.transform(value.toString(), format);
        } else {
            return '';
        }
    }

    convertCardType(inputData: string, type: string): string {
        var outputData = '';
        // Client is MSB, display Mobile Banking / E-Wallet instead of QR
        if (type !== 'ND') {
            outputData = inputData;
        } else {
            if (inputData) {
                let acq = this.cardTypeList.find(obj => obj.value.split(',').includes(inputData));
                if (acq !== undefined)
                    outputData = acq.label;
                else
                    outputData = '';
            }
        }
        if (!outputData) {
            outputData = inputData;
        }
        return outputData;
    }

    selectAll() {
        if (this.selectedDisputes && this.selectedDisputes.length > 0) {
            this.selectedDisputes.forEach((element, index) => {
                this.handleCheckbox(element);
            });
        } else {
            this.checkWaitSendList = false;
            this.checkWaitAdviseList = false;
            this.checkWaitRemindList = false;
            this.checkWaitReopenList = false;
            const array = [];
            this.data.forEach(item => {
                item['active'] = true;
                array.push(item);
            });
            this.data = array;
            this.showUpdateDispute = false;
        }
    }

    getTotalPage(totalRecords: number, rows: number) {
        return Math.ceil(totalRecords / rows);
    }

    openColumnDisplay() {
        this.columnDisplayRef = this.dialogService2.open(DisputeColumnDisplayComponent, {
            header: 'Column Display ',
            contentStyle: { "max-height": "90%", "width": "400px", "overflow": "auto" },
            baseZIndex: 10000,
            data: {
                columnItems: this.cols,
            }
        });

        this.columnDisplayRef.onClose.subscribe(result => {
            if (result) {
                this.cols = result;
                this.cols.sort((a, b) => (a.order > b.order) ? 1 : -1);
                this.validateFilterColumn();
                this.disputeUtil.saveColumns(this.cols, this.department, 'QT');
            }
        });
    }

    isKbank(data: any) {
        return this.disputeUtil.isKbank(data);
    }

    validateFilterColumn() {
        for (let col of this.cols) {
            if (col.active) continue;

            if (col.code === 'disputeStatus') {
                this.disputeStatusSelected = undefined;
            }
            if (col.code === 'fraudInves') {
                this.fraudInvesSelected = undefined;
            }
            if (col.code === 'onepayPic') {
                this.onepayPicSelected = undefined;
            }
            if (col.code === 'merchantId') {
                this.merchantId = undefined;
            }
            if (col.code === 'transId') {
                this.transId = undefined;
            }
            if (col.code === 'orderRef') {
                this.orderRef = undefined;
            }
            if (col.code === 'merchantTransRef') {
                this.merchantTransRef = undefined;
            }
            if (col.code === 'channel') {
                this.channel = undefined;
            }
            if (col.code === 'acq') {
                this.acquirerSelected = undefined;
            }
            if (col.code === 'cardType') {
                this.cardTypeSelected = undefined;
            }
            if (col.code === 'cardNumber') {
                this.cardNumber = undefined;
            }
            if (col.code === 'authCode') {
                this.authCode = undefined;
            }
            if (col.code === 'transactionAmount') {
                this.transCurrency = undefined;
            }
            if (col.code === 'disputeAmount') {
                this.disputeCurrency = undefined;
            }
            if (col.code === 'disputeStage') {
                this.disputeStage = undefined;
            }
            if (col.code === 'disputeReason') {
                this.disputeReason = undefined;
            }
            if (col.code === 'outcome') {
                this.outcome = undefined;
            }
            if (col.code === 'merchantChannel') {
                this.merchantChannelSelected = undefined;
            }
            if (col.code === 'transactionType') {
                this.transTypeSelected = undefined;
            }
            if (col.code === 'transactionState') {
                this.transStateSelected = undefined;
            }
        }

    }

    openDetailDialog(disputeId: String) {
        const detailDialog = this.dialogPrime.open(DisputeDetailComponent, {
            contentStyle: { "max-height": "85%", "max-width": "100%"},
            data: {
                disputeId: disputeId,
                isPopup: true,
                queryParams: this.generateParams(),
                baseZIndex: 998
            },
            width: '100%'
        });
        detailDialog.onClose.subscribe(() => {
            console.log('close dialog');
            this.loadDataLocal();
        });
    }

    setRowTempValues(row) {
        row['dDisputeDate'] = row.disputeDate? new Date(row.disputeDate) : undefined;
        row['dDueDate'] = row.dueDate? new Date(row.dueDate) : undefined;
        row['disputeAmountTemp'] = row.disputeAmount;
        // Normalize outcome value: convert empty string, null, or undefined to 'blank' to match dropdown option
        if (row.outcome === '' || row.outcome === null || row.outcome === undefined) {
            row.outcome = 'blank';
        }
        this.setRowDisputeCodeList(row);
    }

    setRowDisputeCodeList(row) {
        row['disputeCodeList'] = this.getListCodeRisk(row.cardType, row.disputeStage);
    }

    editDispute(row, fieldName, value) {
        // console.log(data, fieldName, value);
        if (fieldName == 'disputeDate') {
            row.disputeDate = row.dDisputeDate;
        } else if (fieldName == 'dueDate') {
            row.dueDate = row.dDueDate;
        }
        this.setTimeoutSaveDispute(row);
    }

    setTimeoutSaveDispute(row) {
        let findTimeout = this.mapTimeout.get(row.id);
        if (findTimeout) {
            clearTimeout(findTimeout);
            this.mapTimeout.delete(row.id);
        }
        
        let timeout = setTimeout(() => this.saveDispute(row), 4000);
        this.mapTimeout.set(row.id, timeout);
    }

    clickInputText(row, fieldName) {
        if (fieldName == 'disputeAmount') {
            row.editDisputeAmount = true;
        }
    }
    blurInputText(row, fieldName) {
        if (fieldName == 'disputeAmount') {
            row.editDisputeAmount = false;
            if (row.disputeAmount != row.disputeAmountTemp) {
                row.disputeAmount = row.disputeAmountTemp;
                this.setTimeoutSaveDispute(row);
            }
        }
    }
    clickDropdown(row, fieldName) {
    }
    editDropdown(row, fieldName) {
        if (fieldName == 'disputeStage') {
            row.disputeCode = undefined;
            this.setRowDisputeCodeList(row); // update lai list dispute code
        }
        this.setTimeoutSaveDispute(row);
    }
    
    clickPCalendar(element) {
        if (element.containerViewChild.nativeElement.children[0]) {
            element.containerViewChild.nativeElement.children[0].click();
        } else {
            console.log('error');
        }
    }

    saveDispute(row) {
        console.log('saveDispute', row.id);
        let check = this.checkRequiredFields(row);
        if (!check.valid) {
            row.missingRequired = true;
            row.toarstMsg = check.msg;
            this.toastr.warning(row.toarstMsg);
            return;
        } else {
            row.missingRequired = false;
            row.toarstMsg = '';
        }

        let body = {
            'id': row.id,
            'onepayPic': row.onepayPic,
            'disputeStage': row.disputeStage || '',
            'disputeReason': row.disputeReason || '',
            'disputeCode': row.disputeCode || '',
            'disputedAmount': row.disputeAmount,
            'disputeCurrency': row.disputeCurrency,
            'dueDate': row.dueDate? this.formatDate(row.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss') : '',
            'disputeDate': row.disputeDate? this.formatDate(row.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss') : '',
            'outcome': row.outcome === 'blank' ? '' : row.outcome,
            'note': row.note,
            'fraudInves': row.fraudInves,
            'sftpAppleFileFromIssuers': this.disputeUtil.formatDataFileFrom(row.sftpAppleFileFromIssuers),
        };
        this.sSDisputeManagementService.updateDispute(row.id, body).subscribe(res => {
            if (res && res.code == 200) {
                this.toastr.success('Successfully saved dispute', 'Success');
                row.lastUpdate = new Date();
                row.saveSuccess = true;
                row.saveFailed = false;
                setTimeout(() => row['saveSuccess'] = false, 3000);
                if (row.outcome) row.disputeStatus = 'resolved';
                this.setRowTempValues(row);
            } else {
                row['saveFailed'] = true;
            }
        } 
        , err => { row['saveFailed'] = true; }
        );
    }

    checkRequiredFields(row) {
        let msg = '';
        let valid = true;
        if (!row.disputeDate) {
            msg = 'Dispute Date is required';
            valid = false;
        } else if (!row.dueDate) {
            msg = 'Due Date is required';
            valid = false;
        } else if (!row.disputeStage) {
            msg = 'Dispute Stage is required';
            valid = false;
        } else if (!row.disputeCode) {
            msg = 'Dispute Code is required';
            valid = false;
        }
        return {msg, valid};
    }

    getListCodeRisk(cardType, disputeStage) {
        // console.log('truyen vao', cardType, disputeStage);
        let res = this.listDisputeCodeTotal
            .filter(e => e.card == cardType)
            .filter(e => e.stage == disputeStage);
        return [{label: '', value: ''}, ...res];
    }
    convertCUP(id) {

        if (id) {
            if (id.toLowerCase() === "unionpay") {
                return "UNIONPAY";
            }
            else return id;
        }
    }

    createCSVFile() {
        const ref = this.dialogService.open(CreateCSVFileModalComponent, {
            data: { 
                'list': this.selectedDisputes, 
                'mapOutcome': this.mapOutcome,
                'mapDisputeStage': this.mapDisputeStage,
                'mapDisputeCode': this.mapDisputeCode
            },
            autoFocus: false,
            maxWidth: '90vw',
            maxHeight: '80vh',
        });

    }

    checkShowUpdateDisputeBtn() {
        if (this.selectedDisputes?.length > 1) {
            //check same dispute_status
            const mapDisputeStatus = this.selectedDisputes.map(item => item.disputeStatus);
            let isSameDisputeStatus = mapDisputeStatus?.length === 0 ? true :
                mapDisputeStatus.every(val => val === mapDisputeStatus[0]);
            //check same transaction_type
            const mapTransType = this.selectedDisputes.map(item => item.transactionType);
            let isSameTransType = mapTransType?.length === 0 ? true :
                mapTransType.every(val => val === mapTransType[0]);
            //check same transaction_status
            const mapTransStatus = this.selectedDisputes.map(item => item.transactionStatus);
            let isSameTransStatus = mapTransStatus?.length === 0 ? true :
                mapTransStatus.every(val => val === mapTransStatus[0]);
            this.showUpdateDispute = isSameTransType && isSameTransStatus && isSameDisputeStatus;
        } else {
            this.showUpdateDispute = false;
        }
    }

    openUpdateDisputeByBatch() {
        console.log('Opening Share Dispute Input Dialog with selected disputes: ', this.selectedDisputes);
        
        // Ensure disputes are selected
        if (!this.selectedDisputes || this.selectedDisputes.length === 0) {
            this.toastr.error('Please select at least one dispute');
            return;
        }

        // Prepare dropdown lists with safe defaults
        let disputeCodeList = [{ label: 'Blank', value: '' }];

        console.log('=======map dispute cpde: ',this.mapDisputeCode);
        
        // Only filter dispute codes if we have the data and valid cardType/disputeStage
        if (this.listDisputeCodeTotal && this.listDisputeCodeTotal.length > 0) {
            const firstDispute = this.selectedDisputes[0];
            if (firstDispute.cardType && firstDispute.disputeStage) {
                disputeCodeList = this.getListCodeRisk(
                    firstDispute.cardType, 
                    firstDispute.disputeStage
                );
            } else {
                // If no cardType or disputeStage, use all codes with blank option
                disputeCodeList = [
                    { label: 'Blank', value: '' },
                    ...this.listDisputeCodeTotal.map(item => ({ label: item.label, value: item.value }))
                ];
            }
        }

        console.log('=====Dispute Code List:', disputeCodeList);
        console.log('=====Dispute listBusinessCategory:', this.listBusinessCategory);

        // Open ShareDisputeInputDialog
        this.columnDisplayRef = this.dialogService2.open(ShareDisputeInputDialogComponent, {
            header: 'Share Dispute Input - Selected Disputes: ' + this.selectedDisputes.length,
            contentStyle: { "max-height": "80vh", "width": "600px", "overflow": "auto", "padding-bottom": "30px"},
            baseZIndex: 10000,
            data: {
                selectedDisputes: this.selectedDisputes,
                business_category_list: this.listBusinessCategory || [],
                dispute_reason_list: this.listReasonEdit || [],
                dispute_code_list: disputeCodeList,
                dispute_stage_list: [{ label: 'Blank', value: '' }, ...listDisputeStage.filter(item => item.label && item.value)],
                outcome_list: this.listOutComeEdit || [],
                fraud_investigation_list: this.fraudInvesListEdit || [],
                dispute_file_from_issuer_list: this.listIssuers || []
            }
        });

        // Handle dialog close
        this.columnDisplayRef.onClose.subscribe(result => {
            if (result) {
                this.updateDisputesBatch(result);
            }
        });
    }

    updateDisputesBatch(updatedFields: any) {
        console.log('Updating disputes with fields:', updatedFields);
        
        // Update local data - Check !== null
        // Because dialog returns ALL fields (including null), so they are never undefined
        this.selectedDisputes.forEach(dispute => {
            if (updatedFields.businessCategory !== null) {
                dispute.businessCategory = updatedFields.businessCategory;
            }
            if (updatedFields.disputeAmount !== null) {
                dispute.disputeAmount = updatedFields.disputeAmount;
            }
            if (updatedFields.disputeCrr !== null) {
                dispute.disputeCrr = updatedFields.disputeCrr;
            }
            if (updatedFields.disputeReason !== null) {
                dispute.disputeReason = updatedFields.disputeReason;
            }
            if (updatedFields.disputeCode !== null) {
                dispute.disputeCode = updatedFields.disputeCode;
            }
            if (updatedFields.disputeStage !== null) {
                dispute.disputeStage = updatedFields.disputeStage;
            }
            if (updatedFields.outcome !== null) {
                dispute.outcome = updatedFields.outcome;
                if (dispute.outcome) {
                    dispute.disputeStatus = 'resolved';
                }
            }
            if (updatedFields.fraudInvestigation !== null) {
                dispute.fraudInves = updatedFields.fraudInvestigation;
            }
            if (updatedFields.internalNote !== null) {
                dispute.note = updatedFields.internalNote;
            }
            if (updatedFields.disputeFileFromIssuer !== null) {
                dispute.sftpAppleFileFromIssuers = updatedFields.disputeFileFromIssuer;
            }
            if (updatedFields.evidence !== null) {
                dispute.evidence = updatedFields.evidence;
            }

            // Update data array for UI
            this.data.forEach(item => {
                if (item.id === dispute.id) {
                    Object.assign(item, dispute);
                }
            });
        });

        // Prepare API request body (original format - full dispute objects)
        let updateList = [];
        this.selectedDisputes.forEach(row => {
            // Ensure dueDate exists
            if (!row.dueDate) {
                var disputeDate = new Date(row.disputeDate);
                row.dueDate = new Date(disputeDate.setDate(disputeDate.getDate() + 5));
            }

            let dispute = {
                "id": row.id,
                "disputeSender": row.disputeSender,
                "onepayPic": row.onepayPic,
                "sendDisputeTo": row.sendDisputeTo || '',
                "merchantRespond": row.merchantRespond,
                "businessCategory": row.businessCategory || '',
                "merchantGroup": row.merchantGroup,
                "disputeStage": row.disputeStage || '',
                "disputeReason": row.disputeReason,
                "disputeCode": row.disputeCode,
                "disputedAmount": row.disputeAmount,
                "disputeCurrency": row.disputeCurrency,
                "dueDate": this.formatDate(row.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                "disputeDate": this.formatDate(row.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                "outcome": row.outcome === 'blank' ? '' : row.outcome,
                "refNumber": row.refNumber,
                "note": row.note,
                "merchantName": row.merchantName,
                "orderReference": row.orderReference,
                "merchantId": row.merchantId,
                "cardNumber": row.cardNumber,
                "merchantTransactionReference": row.merchantTransactionReference,
                "transactionAmount": row.transactionAmount,
                "authorisationCode": row.authorisationCode,
                "transactionDate": this.formatDate(row.transactionDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
                "operatorId": this.global.activeProfile.n_id,
                "operatorName": row.operatorName,
                "fraudInves": row.fraudInves,
                "sftpAppleFileFromIssuers": row.sftpAppleFileFromIssuers
            };
            updateList.push(dispute);
        });

        // Call API to update disputes
        let body = {
            'data': updateList
        };
        
        this.sSDisputeManagementService.updateByBatch(body).subscribe(res => {
            if (res && res.status == 200) {
                this.selectedDisputes = [];
                this.showUpdateDispute = false;
                this.toastr.success("Update disputes successfully");
            } else {
                this.toastr.error("Failed to update disputes");
            }
        }, error => {
            console.error('Error updating disputes:', error);
            this.toastr.error("Error updating disputes");
        });
    }
}
export class SelectOption {
    constructor(
        public label: string,
        public value: string
    ) { }
}
export interface DisputeCodeGroup {
    letter: string;
    type: string;
    stage: string
    names: SelectOption[];
}

