<div class="wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="padding-left-right-12 col-sm-12 col-md-12 col-lg-12 detail-info">
                <div class="padding-left-right-0 col-sm-12 col-md-12 col-lg-12">
                    <div style="text-align: right; float: left;">
                        <button *ngIf="!isPopup" type="button" pTooltip="Close" pButton label="Close" icon="pi pi-angle-left"
                            iconPos="left" (click)="backToPage()" class=" download-button p-button-success"></button>
                    </div>
                       <!-- <input style="text-align: right;" (change)="this.onFilechange($event)" class="form-control" type="file" id="formFile"> -->
                    <div style="text-align: right;" class="action">
                        <button *ngIf="false&&fildeDownloadExis == 'true'&&isActive('download_sftp_response_2')" pButton pRipple type="button" (click)="downloadFile()"
                            label="Download File" id="download_sftp_response"></button>
                        <button id="save_and_send_sftp" *ngIf="false && !isResolved && isActive('save_and_send_sftp_2')&& originalDispute && originalDispute.merchantId=='TESTONEPAY'" pButton pRipple type="button" 
                            [disabled]="isInvalid()" 
                            (click)="saveAndSendToSftp()"
                            label="Save and send to SFTP">
                        </button>
                        <button *ngIf="!(isRISK && isINT)" id="btn-save" class="p-button-success" pButton pRipple type="button" [disabled]="isInvalid()" 
                            (click)="saveDispute()" label="Save" ></button>
                        <button id="btn-new-tab" *ngIf="isPopup" style="float:left;margin-left:0" pButton pRipple type="button" 
                            (click)="openNewTab()" label="Open in new tab"></button>
                    </div>
                </div>
                <div class="row detail-box" *ngIf="originalDispute" id="frm_display" id="dispute-detail">
                    <div class="padding-left-right-10 col-md-6 col-xl-6 col-lg-6" style="">
                        <div class="padding-left-right-10 col-md-12 col-xl-12 col-lg-12 box-shadow border rounded">
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Dispute Status</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p [ngStyle]="{ 'color': (originalDispute.disputeStatus == 'resolved' ? '#70ad47' : (originalDispute.disputeStatus == 'created' ? '#000000' : '#ff9900')) }" class="text_right">
                                        {{originalDispute.disputeStatusConvert}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Create Date</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{formatDate(originalDispute.createDate, 'dd/MM/yyyy hh:mm a')}}</p>
                                </div>
                            </div>
                            <div *ngIf="originalDispute.sentToMerchantDate" class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Send to Merchant Date</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{formatDate(originalDispute.sentToMerchantDate, 'dd/MM/yyyy hh:mm a')}}</p>
                                </div>
                            </div>
                            <div *ngIf="originalDispute.lastUpdate" class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Last Update</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{formatDate(originalDispute.lastUpdate, 'dd/MM/yyyy hh:mm a')}}</p>
                                </div>
                            </div>
                            <div *ngIf="originalDispute.lastResponse" class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Last Response</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{formatDate(originalDispute.lastResponse, 'dd/MM/yyyy hh:mm a')}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Partner Name</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.partnerName}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Merchant ID</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.merchantId}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Merchant Name</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.merchantName}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">MCC</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.mcc}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Transaction ID</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.transactionId}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Order Reference</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.orderReference}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Merchant Transaction Reference</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.merchantTransactionReference}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Channel</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.channelConvert}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;"  *ngIf="!global.isActive('ss_outsource_user')">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Acquirer</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.acquirerDownload}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Card Type</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">
                                        {{originalDispute.cardTypeConvert}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Card Number</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.cardNumber}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Transaction Type</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.transactionType}}</p>
                                </div>
                            </div>
                            <div class="row" style="margfin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Transaction Amount</p>
                                </div>
                                <div
                                    class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.transactionCurrency}} {{originalDispute.transactionAmount | CurrencyPipe: originalDispute.transactionCurrency}}
                                    </p>
                                </div>
                            </div>
                            <div class="row" style="margfin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Refund Amount</p>
                                </div>
                                <div 
                                    class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.transactionCurrency}} {{originalDispute.refundAmount | CurrencyPipe: originalDispute.transactionCurrency}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Transaction Date</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{formatDate(originalDispute.transactionDate, 'dd/MM/yyyy hh:mm a')}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Authorisation Code</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.authorisationCode}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Card Issuer</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.cardIssure}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">BIN Country</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.binCountry}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">IP Address</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.ipAddress}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">IP Country</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.ipCountry}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">CSC Response</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.cscResponse}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Authentication State</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p *ngIf="originalDispute.authenticationState=='Y'" class="text_right">Y-Cardholder Verified</p>
                                    <p *ngIf="originalDispute.authenticationState=='N'" class="text_right">N-Cardholder Not Verified</p>
                                    <p *ngIf="originalDispute.authenticationState=='M'" class="text_right">M-Verification Attempted</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Response Code</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.responseCode}}</p>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="padding-left-right-10 col-md-5 col-xl-5 col-lg-5 text-left">
                                    <p class="text_left">Transaction Status</p>
                                </div>
                                <div class="padding-left-right-10 col-md-7 col-xl-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute.transactionStatus}}</p>
                                </div>
                            </div>

                            <div *ngIf="isRISK && isINT" class="row">
                                <div class="padding-left-right-10 col-sm-5 col-md-5 col-lg-5 text-left">
                                    <p class="text_left">Acquirer Transaction Reference Number</p>
                                </div>
                                <div class="padding-left-right-10 col-sm-7 col-md-7 col-lg-7 text-right">
                                    <p class="text_right">{{originalDispute?.acquirerTransRef}}</p>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="padding-left-right-10 col-md-6 col-xl-6 col-lg-6">
                        <div class="padding-left-right-10 col-md-12 col-xl-12 col-lg-12 box-shadow border rounded">
                            <form [formGroup]="disputeDetailForm" class="dispute mt-3">
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <input matInput placeholder="Dispute Sender" maxlength="200"
                                                [(ngModel)]="disputeDetail.disputeSender"
                                                formControlName="disputeSender" />
                                            <mat-error
                                                *ngIf="disputeDetailForm.controls['disputeSender'].invalid && (disputeDetailForm.controls['disputeSender'].dirty || disputeDetailForm.controls['disputeSender'].touched)">
                                                <span
                                                    *ngIf="disputeDetailForm.controls['disputeSender'].errors.required">Emails
                                                    not valid</span>
                                            </mat-error>
                                        </mat-form-field>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- p-dropdown for isRISK && isINT -->
                                        <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT" 
                                            [class.has-value]="hasValue('onepayPic')"
                                            [class.is-focused]="dropdownFocusStates.onepayPic">
                                        
                                            <label class="mat-form-field-label">
                                                {{getDropdownLabel('onepayPic', 'OnePay PIC')}}
                                            </label>
                                        
                                            <p-dropdown 
                                                [options]="listOnepayPic" 
                                                formControlName="onepayPic" 
                                                [filter]="true" 
                                                [showClear]="true"
                                                [autoDisplayFirst]="false" 
                                                filterBy="name" 
                                                optionLabel="name" 
                                                optionValue="id"
                                                [placeholder]="getDropdownLabel('onepayPic', 'OnePay PIC')" 
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel" 
                                                appendTo="body" 
                                                (onChange)="OnSelected('onepayPic', $event.value)"
                                                (onFocus)="dropdownFocusStates.onepayPic = true" 
                                                (onBlur)="dropdownFocusStates.onepayPic = false"
                                                (onClear)="onDropdownClear('onepayPic')" 
                                                [virtualScroll]="true" 
                                                [itemSize]="36">
                                        
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-onepayPic-' + option.name">
                                                        {{option.name}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                        
                                            <div class="mat-form-field-underline"></div>
                                        
                                            <div class="error-message"
                                                *ngIf="disputeDetailForm.get('onepayPic').invalid && (disputeDetailForm.get('onepayPic').dirty || disputeDetailForm.get('onepayPic').touched)">
                                                <span *ngIf="disputeDetailForm.get('onepayPic').errors?.required">
                                                    Onepay Pic is required
                                                </span>
                                            </div>
                                        </div>

                                        <!-- mat-select for non-RISK or non-INT -->
                                        <div *ngIf="!(isRISK && isINT)">
                                            <mat-form-field class="col-md-12 custom-form-item">
                                                <mat-select [placeholder]="getRequiredLabel('OnePay PIC')" [(ngModel)]="disputeDetail.onepayPic" id="select-onepayPic"
                                                    name="onepayPic" formControlName="onepayPic" required>
                                                    <mat-option (click)="OnSelected('onepayPic', option)" [value]="option.id" name="{{'option-onepayPic-' + option.name}}"
                                                        *ngFor="let option of listOnepayPic; index as i">
                                                        {{option.name}}
                                                    </mat-option>
                                                </mat-select>
                                                <mat-error name="error-onepayPic"
                                                    *ngIf="disputeDetailForm.controls['onepayPic'].invalid && (disputeDetailForm.controls['onepayPic'].dirty || disputeDetailForm.controls['onepayPic'].touched)">
                                                    <span
                                                        *ngIf="disputeDetailForm.controls['onepayPic'].errors.required">Onepay
                                                        Pic is required</span>
                                                </mat-error>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <input type="text" matInput placeholder="Send Dispute To" maxlength="500" name="input-sendDisputeTo"
                                                [(ngModel)]="disputeDetail.sendDisputeTo"
                                                [matAutocomplete]="auto" (ngModelChange)="testEmailFormat(disputeDetail.sendDisputeTo)" formControlName="sendDisputeTo" />
                                                <mat-autocomplete #auto="matAutocomplete">
                                                    <mat-option *ngFor="let option of filteredOptions | async" [value]="selectOptionSendTo(disputeDetail.sendDisputeTo,option)">
                                                      {{option}}
                                                    </mat-option>
                                                  </mat-autocomplete>
                                            </mat-form-field>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <input matInput placeholder="Merchant PIC" name="input-merchantRespond"
                                                [(ngModel)]="disputeDetail.merchantRespond" maxlength="200"
                                                formControlName="merchantRespond" />
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- p-dropdown for isRISK && isINT -->
                                        <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT"
                                            [class.has-value]="hasValue('businessCategory')"
                                            [class.is-focused]="dropdownFocusStates.businessCategory">
                                            
                                            <label class="mat-form-field-label">Business Category</label>
                                            
                                            <p-dropdown
                                                [options]="listBusinessCategory"
                                                formControlName="businessCategory"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                placeholder="Business Category"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('businessCategory', $event.value)"
                                                (onFocus)="dropdownFocusStates.businessCategory = true"
                                                (onBlur)="dropdownFocusStates.businessCategory = false"
                                                (onClear)="onDropdownClear('businessCategory')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-businessCategory-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                        </div>
                                        <!-- mat-select for non-RISK or non-INT -->
                                        <div *ngIf="!(isRISK && isINT)" class="mat-form-field-wrapper">
                                            <mat-form-field class="col-md-12 custom-form-item">
                                                <mat-select placeholder="Business Category" [(ngModel)]="disputeDetail.businessCategory"
                                                    name="businessCategory" formControlName="businessCategory" name="select-businessCategory">
                                                    <mat-option (click)="OnSelected('businessCategory', option)"
                                                        [value]="option.value" data-name="{{'option-businessCategory-' + option.label}}"
                                                        *ngFor="let option of listBusinessCategory; index as i">
                                                        {{option.label}}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="isRISK && isINT" class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <div class="mat-form-field-wrapper"
                                            [class.has-value]="hasValue('disputeStage')"
                                            [class.is-focused]="dropdownFocusStates.disputeStage">
                                            
                                            <label class="mat-form-field-label">{{getDropdownLabel('disputeStage', 'Dispute Stage')}}</label>
                                            
                                            <p-dropdown
                                                [options]="listDisputeStage"
                                                formControlName="disputeStage"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                [placeholder]="getDropdownLabel('disputeStage', 'Dispute Stage')"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('disputeStage', $event.value)"
                                                (onFocus)="dropdownFocusStates.disputeStage = true"
                                                (onBlur)="dropdownFocusStates.disputeStage = false"
                                                (onClear)="onDropdownClear('disputeStage')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeStage-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                            
                                            <div class="error-message"
                                                *ngIf="disputeDetailForm.get('disputeStage').invalid && (disputeDetailForm.get('disputeStage').dirty || disputeDetailForm.get('disputeStage').touched)">
                                                <span *ngIf="disputeDetailForm.get('disputeStage').errors?.required">
                                                    Dispute Stage is required
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="isRISK" class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- p-dropdown for isRISK && isINT -->
                                        <div class="mat-form-field-wrapper" *ngIf="isINT"
                                            [class.has-value]="hasValue('disputeReason')"
                                            [class.is-focused]="dropdownFocusStates.disputeReason">
                                            
                                            <label class="mat-form-field-label">Dispute Reason</label>
                                            
                                            <p-dropdown
                                                [options]="listDisputeReason"
                                                formControlName="disputeReason"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                placeholder="Dispute Reason"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('disputeReason', $event.value)"
                                                (onFocus)="dropdownFocusStates.disputeReason = true"
                                                (onBlur)="dropdownFocusStates.disputeReason = false"
                                                (onClear)="onDropdownClear('disputeReason')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeReason-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                        </div>
                                        <!-- mat-select for isRISK && !isINT (DOM) -->
                                        <div *ngIf="!isINT" class="mat-form-field-wrapper">
                                            <p-dropdown
                                                [options]="listDisputeReason"
                                                [(ngModel)]="disputeDetail.disputeReason"
                                                [filter]="true"
                                                [showClear]="true"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                placeholder="Dispute Reason"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                name="disputeReason"
                                                formControlName="disputeReason"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeReason-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            <div class="mat-form-field-underline"></div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="isRISK && isINT" class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <div class="mat-form-field-wrapper"
                                            [class.has-value]="hasValue('disputeCode')"
                                            [class.is-focused]="dropdownFocusStates.disputeCode">
                                            
                                            <label class="mat-form-field-label">{{getDropdownLabel('disputeCode', 'Dispute Code')}}</label>
                                            
                                            <p-dropdown
                                                [options]="listDisputeCodeRiskFiltered"
                                                formControlName="disputeCode"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                [disabled]="!disputeDetail.disputeStage"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                [placeholder]="getDropdownLabel('disputeCode', 'Dispute Code')"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('disputeCode', $event.value)"
                                                (onFocus)="dropdownFocusStates.disputeCode = true"
                                                (onBlur)="dropdownFocusStates.disputeCode = false"
                                                (onClear)="onDropdownClear('disputeCode')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeCode-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                            
                                            <div class="error-message"
                                                *ngIf="disputeDetailForm.get('disputeCode').invalid && (disputeDetailForm.get('disputeCode').dirty || disputeDetailForm.get('disputeCode').touched)">
                                                <span *ngIf="disputeDetailForm.get('disputeCode').errors?.required">
                                                    Dispute Code is required
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="isSS" class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- <div class="mat-form-field-wrapper">
                                            <p-dropdown
                                                [options]="listDisputeCodeSS"
                                                [(ngModel)]="disputeDetail.disputeCode"
                                                [filter]="true"
                                                [showClear]="true"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                [placeholder]="getDropdownLabel('disputeCode', 'Dispute Code')"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                name="disputeCode"
                                                formControlName="disputeCode"
                                                (onChange)="OnSelected('disputeCode', $event.value)"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeCode-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            <div class="mat-form-field-underline"></div>
                                            <div class="error-message"
                                                *ngIf="disputeDetailForm.controls['disputeCode'].invalid && (disputeDetailForm.controls['disputeCode'].dirty || disputeDetailForm.controls['disputeCode'].touched)">
                                                <span *ngIf="disputeDetailForm.controls['disputeCode'].errors.required">
                                                    Dispute Code is required
                                                </span>
                                            </div>
                                        </div> -->
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <mat-select [placeholder]="getRequiredLabel('Dispute Code')"
                                                [(ngModel)]="disputeDetail.disputeCode" name="disputeCode" name="select-disputeCode"
                                                formControlName="disputeCode" required>
                                                <mat-option (click)="OnSelected('disputeCode', option.value)" name="{{'option-disputeCode-' + option.label}}"
                                                    *ngFor="let option of listDisputeCodeSS" [value]="option.value">
                                                    {{option.label}}
                                                </mat-option>
                                            </mat-select>
                                            <mat-error name="error-disputeCode"
                                                *ngIf="disputeDetailForm.controls['disputeCode'].invalid && (disputeDetailForm.controls['disputeCode'].dirty || disputeDetailForm.controls['disputeCode'].touched)">
                                                <span
                                                    *ngIf="disputeDetailForm.controls['disputeCode'].errors.required">Dispute
                                                    Code is required</span>
                                            </mat-error>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">

                                            <input matInput [placeholder]="getRequiredLabel('Dispute Amount')" name="input-disputeAmount"
                                                [(ngModel)]="disputeDetail.disputeAmount" maxlength="15" type="tel"
                                                formControlName="disputeAmount"
                                                [options]="{ align: 'left', prefix: '', thousands: ',', precision: disputeDetail.disputeCurrency=='VND'? 0: 2, decimal: '.' }"
                                                currencyMask required />
                                        </mat-form-field>
                                        <mat-error  name="error-disputeAmount"
                                            *ngIf="disputeDetailForm.controls['disputeAmount'].invalid && (disputeDetailForm.controls['disputeAmount'].dirty || disputeDetailForm.controls['disputeAmount'].touched)">
                                            <span
                                                *ngIf="disputeDetailForm.controls['disputeAmount'].errors.pattern">Dispute
                                                amount is number</span>
                                        </mat-error>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- p-dropdown for isRISK && isINT -->
                                        <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT"
                                            [class.has-value]="hasValue('disputeCurrency')"
                                            [class.is-focused]="dropdownFocusStates.disputeCurrency">
                                            
                                            <label class="mat-form-field-label">{{getDropdownLabel('disputeCurrency', 'Dispute Currency')}}</label>
                                            
                                            <p-dropdown
                                                [options]="listDisputeCurrency"
                                                formControlName="disputeCurrency"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                [placeholder]="getDropdownLabel('disputeCurrency', 'Dispute Currency')"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('disputeCurrency', $event.value)"
                                                (onFocus)="dropdownFocusStates.disputeCurrency = true"
                                                (onBlur)="dropdownFocusStates.disputeCurrency = false"
                                                (onClear)="onDropdownClear('disputeCurrency')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-disputeCurrency-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                            
                                            <div class="error-message"
                                                *ngIf="disputeDetailForm.get('disputeCurrency').invalid && (disputeDetailForm.get('disputeCurrency').dirty || disputeDetailForm.get('disputeCurrency').touched)">
                                                <span *ngIf="disputeDetailForm.get('disputeCurrency').errors?.required">
                                                    Dispute currency is required
                                                </span>
                                            </div>
                                        </div>
                                        <!-- mat-select for non-RISK or non-INT -->
                                        <div *ngIf="!(isRISK && isINT)" class="mat-form-field-wrapper">
                                            <mat-form-field class="col-md-12 custom-form-item">
                                                <mat-select [placeholder]="getRequiredLabel('Dispute Currency')"
                                                    [(ngModel)]="disputeDetail.disputeCurrency" name="select-disputeCurrency"
                                                    formControlName="disputeCurrency" required>
                                                    <mat-option [value]="option.value" name="{{'option-disputeCurrency-' + option.label}}"
                                                        *ngFor="let option of listDisputeCurrency; index as i">
                                                        {{option.label}}
                                                    </mat-option>
                                                    <mat-error name="error-disputeCurrency"
                                                        *ngIf="disputeDetailForm.controls['disputeCurrency'].invalid && (disputeDetailForm.controls['disputeCurrency'].dirty || disputeDetailForm.controls['disputeCurrency'].touched)">
                                                        <span
                                                            *ngIf="disputeDetailForm.controls['disputeCurrency'].errors.required">Dispute
                                                            currency is required</span>
                                                    </mat-error>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="d-flex col-sm-12 col-md-12 col-lg-12">
                                        <div class="input-date-group col-md-3">
                                            <span class="label-input">Dispute Date</span>
                                            <p-calendar appendTo="body" [(ngModel)]="disputeDetail.disputeDate" [showIcon]="false" [style]="{'width':'100%'}"
                                            showTime="true" hourFormat="12" dateFormat="dd/mm/yy" inputStyleClass="form-control"
                                            hideOnDateTimeSelect="true" requiredhideOnDateTimeSelect="true" formControlName="disputeDate">
                                            </p-calendar>
                                            <span class="error-msg" *ngIf="disputeDetailForm.controls['disputeDate'].invalid">Dispute Date is required</span>
                                        </div>
                                        <div class="input-date-group col-md-3">
                                            <span class="label-input">Due Date</span>
                                            <p-calendar appendTo="body" [(ngModel)]="disputeDetail.dueDate" [showIcon]="false" [style]="{'width':'100%'}"
                                            showTime="true" hourFormat="12" dateFormat="dd/mm/yy" inputStyleClass="form-control"
                                            hideOnDateTimeSelect="true" requiredhideOnDateTimeSelect="true" formControlName="dueDate">
                                            </p-calendar>
                                            <span class="error-msg" *ngIf="disputeDetailForm.controls['dueDate'].invalid">Due Date is required</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <!-- p-dropdown for isRISK && isINT -->
                                        <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT"
                                            [class.has-value]="hasValue('outcome')"
                                            [class.is-focused]="dropdownFocusStates.outcome">
                                            
                                            <label class="mat-form-field-label">Outcome</label>
                                            
                                            <p-dropdown
                                                [options]="listOutcome"
                                                formControlName="outcome"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                [disabled]="disableOutCome"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                placeholder="Outcome"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('outcome', $event.value)"
                                                (onFocus)="dropdownFocusStates.outcome = true"
                                                (onBlur)="dropdownFocusStates.outcome = false"
                                                (onClear)="onDropdownClear('outcome')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-outcome-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                        </div>
                                        <!-- mat-select for non-RISK or non-INT -->
                                        <div *ngIf="!(isRISK && isINT)" class="mat-form-field-wrapper">
                                            <mat-form-field class="col-md-12 custom-form-item">
                                                <mat-select placeholder="Outcome" [(ngModel)]="disputeDetail.outcome" 
                                                    name="select-outcome" formControlName="outcome" [disabled]="disableOutCome">
                                                    <mat-option [value]="option.value" name="{{'option-outcome-' + option.label}}"
                                                        *ngFor="let option of listOutcome; index as i">
                                                        {{option.label}}
                                                    </mat-option>
                                                </mat-select>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                                <div class="row" *ngIf="isRISK && isINT">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <div class="mat-form-field-wrapper"
                                            [class.has-value]="hasValue('fraudInves')"
                                            [class.is-focused]="dropdownFocusStates.fraudInves">
                                            
                                            <label class="mat-form-field-label">Fraud Investigation</label>
                                            
                                            <p-dropdown
                                                [options]="listFraudInves"
                                                formControlName="fraudInves"
                                                [filter]="true"
                                                [showClear]="true"
                                                [autoDisplayFirst]="false"
                                                filterBy="label"
                                                optionLabel="label"
                                                optionValue="value"
                                                placeholder="Fraud Investigation"
                                                styleClass="mat-dropdown"
                                                panelStyleClass="mat-dropdown-panel"
                                                appendTo="body"
                                                (onChange)="OnSelected('fraudInves', $event.value)"
                                                (onFocus)="dropdownFocusStates.fraudInves = true"
                                                (onBlur)="dropdownFocusStates.fraudInves = false"
                                                (onClear)="onDropdownClear('fraudInves')"
                                                [virtualScroll]="true"
                                                [itemSize]="36">
                                                
                                                <ng-template let-option pTemplate="item">
                                                    <div class="dropdown-option" [attr.data-name]="'option-fraudInves-' + option.label">
                                                        {{option.label}}
                                                    </div>
                                                </ng-template>
                                            </p-dropdown>
                                            
                                            <div class="mat-form-field-underline"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <input type="text" matInput placeholder="Acq Case ID" maxlength="100" name="input-refNumber"
                                                [(ngModel)]="disputeDetail.refNumber"
                                                [matAutocomplete]="autoRefNumber" 
                                                [matAutocompleteDisabled]="false"
                                                formControlName="refNumber" />
                                            <mat-autocomplete #autoRefNumber="matAutocomplete" 
                                                [autoActiveFirstOption]="false"
                                                [displayWith]="displayRefNumber"
                                                class="refnumber-autocomplete">
                                                <mat-option *ngFor="let option of filteredRefNumbers | async" [value]="option">
                                                  {{option}}
                                                </mat-option>
                                            </mat-autocomplete>
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-12 col-md-12 col-lg-12">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <input matInput placeholder="Internal note" [(ngModel)]="disputeDetail.note" name="input-note"
                                                maxlength="1000" formControlName="note" />
                                        </mat-form-field>
                                    </div>
                                </div>

                                <div class="col-sm-12 col-md-12 col-lg-12" style="padding: 0px !important">
                                    <!-- p-dropdown for isRISK && isINT -->
                                    <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT"
                                        [class.has-value]="hasValue('fileFromIssuers')"
                                        [class.is-focused]="dropdownFocusStates.fileFromIssuers">
                                        
                                        <label class="mat-form-field-label">Dispute file from Issuers</label>
                                        
                                        <p-dropdown
                                            [options]="listIssuersFromFile"
                                            formControlName="fileFromIssuers"
                                            [filter]="true"
                                            [showClear]="true"
                                            [autoDisplayFirst]="false"
                                            filterBy="label"
                                            optionLabel="label"
                                            optionValue="value"
                                            placeholder="Dispute file from Issuers"
                                            styleClass="mat-dropdown"
                                            panelStyleClass="mat-dropdown-panel"
                                            appendTo="body"
                                            (onChange)="OnSelected('fileFromIssuers', $event.value)"
                                            (onFocus)="dropdownFocusStates.fileFromIssuers = true"
                                            (onBlur)="dropdownFocusStates.fileFromIssuers = false"
                                            (onClear)="onDropdownClear('fileFromIssuers')"
                                            [virtualScroll]="true"
                                            [itemSize]="36">
                                            
                                            <ng-template let-option pTemplate="item">
                                                <div class="dropdown-option" [attr.data-name]="'option-issuer-' + option.label">
                                                    {{option.label}}
                                                </div>
                                            </ng-template>
                                        </p-dropdown>
                                        
                                        <div class="mat-form-field-underline"></div>
                                    </div>
                                    <!-- mat-select for non-RISK or non-INT -->
                                    <div *ngIf="!(isRISK && isINT)" class="mat-form-field-wrapper">
                                        <mat-form-field class="col-md-12 custom-form-item">
                                            <mat-select placeholder="Dispute file from Issuers" name="select-issuers"
                                                formControlName="fileFromIssuers" [(ngModel)]="disputeDetail.sftpAppleFileFromIssuers">
                                                <mat-option [value]="option.value" name="{{'option-issuer-' + option.label}}"
                                                    *ngFor="let option of listIssuersFromFile; index as i">
                                                    {{option.label}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                </div>
                                
                                <!-- <div class="" *ngIf="(disputeDetail.disputeSender.includes('KBank')) && !isResolved && isActive('dispute_attach_files')">
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <label for="" class="text_action">Attach Files</label>
                                        </div>
                                    </div>
                                    <div class="row">
                                      <div class="col-12">
                                        <div class="mb-3">
                                            <div class="filename-container">
                                                <div *ngIf="files.length; else uploadFilePlaceholder">
                                                    <div *ngFor="let file of files; let i = index">
                                                        <span class="filename">{{ displayFilename(file.name) }}</span>
                                                        <i class="btn-remove-file pi pi-times item-upload-icon" (click)="removeFile(i)"></i>
                                                    </div>
                                                </div>
                                                <ng-template #uploadFilePlaceholder>
                                                    <div><span class="filename">File Name (.zip)</span></div>
                                                </ng-template>
                                            </div>
                                            <input #fileInput hidden="true" type="file" multiple
                                                (input)="onFilechange()" accept="{{allowedExtensionStr}}"/>
                                            <button mat-flat-button 
                                                (click)="fileInput.click()"
                                                class="ml-3 btn-attach">
                                                {{files.length? "Add file" : "Choose File"}}
                                            </button>

                                        </div>
                                      </div>
                                    </div>
                                </div> -->
                                   
                            </form>
                            <div class="tool-box">
                                <div class="padding-left-right-10 mt-4 col-sm-6 col-md-6 col-lg-6 refund-box"
                                    *ngIf="canRefund && !isResolved && global.isActive('refund_dispute_2')">
                                    <div class="row">
                                        <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12">
                                            <p [pTooltip]="isRISK && isINT ? 'Đây là non-financial action nhằm đánh dấu GD đã được Risk nhận nợ với Acquirer bank.\nPayment không đẩy lệnh hoàn thực tế, chỉ báo nợ merchant.' : null"
                                                tooltipPosition="left" tooltipStyleClass="custom-tooltip" class="mb-2 text_action">
                                                Refund Dispute
                                            </p>
                                        </div>
                                    </div>
                                    <form>
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="padding-left-right-10 col-sm-12 col-md-12 col-lg-12 form-row"
                                                    style="align-self: center">
                                                    <input currencyMask class="refund-input" id="refund_amount" autocomplete="off"
                                                        name="input-refundAmount"
                                                        *ngIf="originalDispute.transactionCurrency === 'USD'"
                                                        [(ngModel)]="refund_amount" style="background-color: #fff;"
                                                        placeholder="Refund amount" 
                                                        [options]="{ prefix: '',thousands: ',', precision: 2, align: 'left'}"
                                                        [ngModelOptions]="{standalone: true}" />
                                                    <input currencyMask class="refund-input" id="refund_amount"
                                                        autocomplete="off" name="input-refundAmount"
                                                        *ngIf="originalDispute.transactionCurrency === 'VND'"
                                                        [(ngModel)]="refund_amount" style="background-color: #fff;"
                                                        placeholder="Refund amount"
                                                        [options]="{ prefix: '',thousands: ',', precision: 0, align: 'left'}"
                                                        [ngModelOptions]="{standalone: true}" />
                                                </div>
                                                <div class="padding-left-right-10 mt-3 col-sm-12 col-md-12 col-lg-12">
                                                    <div class="mat-form-field-wrapper" *ngIf="isRISK && isINT"
                                                         [class.has-value]="disputeDesc != null && disputeDesc !== ''"
                                                         [class.is-focused]="dropdownFocusStates.disputeDesc">
                                                        <label class="mat-form-field-label">{{getDropdownLabel('disputeDesc', 'Description')}}</label>
                                                        <p-dropdown
                                                            [options]="disputeDescList"
                                                            [(ngModel)]="disputeDesc"
                                                            [filter]="true"
                                                            [showClear]="disputeDesc != null && disputeDesc !== ''"
                                                            filterBy="label"
                                                            optionLabel="label"
                                                            optionValue="value"
                                                            [placeholder]="(disputeDesc == null || disputeDesc === '') ? getDropdownLabel('disputeDesc', 'Description') : ''"
                                                            styleClass="mat-dropdown"
                                                            panelStyleClass="mat-dropdown-panel"
                                                            appendTo="body"
                                                            name="refundDesc"
                                                            (onChange)="OnSelected('disputeDesc', $event.value)"
                                                            (onFocus)="dropdownFocusStates.disputeDesc = true"
                                                            (onBlur)="dropdownFocusStates.disputeDesc = false"
                                                            (onClear)="onDropdownClear('disputeDesc', 'disputeDesc', refundDescDropdown, 'Description')"
                                                            (ngModelChange)="onDropdownChange('disputeDesc', $event)"
                                                            #refundDescDropdown
                                                            [virtualScroll]="true"
                                                            [itemSize]="36">
                                                            <ng-template let-option pTemplate="item">
                                                                <div class="dropdown-option" [attr.data-name]="'option-refundDesc-' + option.label">
                                                                    {{option.label}}
                                                                </div>
                                                            </ng-template>
                                                        </p-dropdown>
                                                        <div class="mat-form-field-underline"></div>
                                                    </div>
                                                    <div *ngIf="!(isRISK && isINT)" class="mat-form-field-wrapper">
                                                        <p-dropdown
                                                            [options]="disputeDescList"
                                                            [(ngModel)]="disputeDesc"
                                                            [filter]="true"
                                                            [showClear]="true"
                                                            filterBy="label"
                                                            optionLabel="label"
                                                            optionValue="value"
                                                            [placeholder]="getDropdownLabel('disputeDesc', 'Description')"
                                                            styleClass="mat-dropdown"
                                                            panelStyleClass="mat-dropdown-panel"
                                                            appendTo="body"
                                                            name="refundDesc"
                                                            (onChange)="OnSelected('disputeDesc', $event.value)"
                                                            [virtualScroll]="true"
                                                            [itemSize]="36">
                                                            <ng-template let-option pTemplate="item">
                                                                <div class="dropdown-option" [attr.data-name]="'option-refundDesc-' + option.label">
                                                                    {{option.label}}
                                                                </div>
                                                            </ng-template>
                                                        </p-dropdown>
                                                        <div class="mat-form-field-underline"></div>
                                                    </div>
                                                </div>
                                                <div class="padding-left-right-10 mt-2 col-sm-12 col-md-12 col-lg-12">
                                                    <button class="refund-button" mat-raised-button color="primary" id="btn-refund"
                                                        (click)="refund()" [disabled]="!enableDoDispute">
                                                        {{ isRISK && isINT ? 'CREATE REVERSAL' : 'DO REFUND' }}
                                                    </button>
                                                </div>
    
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="upload-box">
                                    <div class="" *ngIf="isKbank(originalDispute) && !isResolved && isActive('dispute_attach_files_2')">
                                        <label class="text_action">File</label>
                                        <div class="upload-container">
                                            <input #fileInput hidden="true" type="file" name="input-uploadFile"
                                                (change)="onFilechange()" accept="{{allowedExtensionStr}}"/>
                                            <button mat-flat-button id="btn-uploadFile"
                                                (click)="fileInput.click()"
                                                class="btn-attach-new">
                                                {{files.length? "Add file" : "Choose File"}}
                                            </button>
                                            <div class="filename-container">
                                                <div *ngIf="files.length; else uploadFilePlaceholder">
                                                    <div *ngFor="let file of files; let i = index">
                                                        <span class="filename">{{ displayFilename(file.name) }}</span>
                                                        <i class="btn-remove-file pi pi-trash item-upload-icon" (click)="removeFile(i)"></i>
                                                    </div>
                                                </div>
                                                <ng-template #uploadFilePlaceholder>
                                                    <div><span class="filename">File Name (.zip)</span></div>
                                                </ng-template>
                                            </div>
                                        </div>
                                        <div class="response-note">
                                            <label class="text_action" for="" (click)="addNote()" id="label-addNote">
                                                Add a response note
                                                <i class="btn-remove-file pi pi-pencil item-upload-icon"></i>
                                            </label>
                                            <!-- <textarea [(ngModel)]="dataEditer" 
                                                *ngIf="switchEditer" 
                                                style="width: 320px; height: 100px;">
                                            </textarea> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button *ngIf="isRISK && isINT" id="btn-save" class="p-button-success col-md-3 col-xl-3 col-lg-3 float-right mt-3 p-3" pButton pRipple type="button" [disabled]="isInvalid()" 
                            (click)="saveDispute()" label="Save" ></button>
                    </div>
                </div>
                <br>

                <app-dispute-history *ngIf="disputeHistory.length"
                    [isDOM]="isDOM"
                    [disputeHistory]="disputeHistory"
                    [transactionHistory]="transactionHistory">
                </app-dispute-history>

                <app-file-history *ngIf="fileHistory.length && disputeDetail.disputeSender?.includes('KBank')"
                    [isDOM]="isDOM"
                    [fileHistory]="fileHistory">
                </app-file-history>
                
                </div>
        </div>
    </div>

</div>