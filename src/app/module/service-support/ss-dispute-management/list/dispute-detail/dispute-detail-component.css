.text_blue {
  color: #00529c;
}

::ng-deep .mat-input-element {
  color: inherit !important;
}

::ng-deep .dispute .mat-form-field-appearance-fill .mat-form-field-flex {
  background: inherit;
  padding: 0.75em 0 0 0;
}

::ng-deep #dispute-detail span, #dispute-detail input{
  font-size: 14px;
}

.detail-info {
  margin-top: 10px;
  max-height: unset;
  overflow: unset;
}

.detail-info .action {
  margin-bottom: 10px;
}

.detail-info .action button {
  margin-left: 10px;
}

.tr_body td {
  border-width: 1px;
}

.tr_body td tr {
  float: left;
  width: 100%;
}

.tr_body td tr td:first-child {
  float: left;
  width: 40%;
  max-width: 105px;
}

.tr_body .td_body {
  padding: 0 !important;
}

.tr_body .td_body tr {
  border-bottom: 1px solid #ddd;
  padding: 1rem 1rem;
}

.tr_body .td_body:last-child tr {
  border-bottom: none;
  padding: 5px 0;
}

.tr_body .td_body tr:last-child {
  border-bottom: none;
}
.no_padding{
  padding: 0px !important;
}



.text_link{
  color: #0089D0;
  font-size: 10px;
  text-align: right;
}

.refund-box {
  border: none !important;
  padding: 0px !important;
}


.refund-box .refund-input {
  background-color: #fff;
    width: 100%;
    display: inline-block;
    height: 36px;
    border-radius: .25rem;
    padding-left: 10px;
    border: 1px solid #dee2e6;
}

.refund-box .refund-button {
    width: 100%;
    font-size: 12px;
}

.text_action {
  font-size: 18px;
  color: #00529C
}

@media (max-width: 480px) {
  .split_text {
    word-break: break-all;
  }

  .tr_table {
    font-size: 14px;
    color: #333333;
  }

  .td_table {
    font-size: 14px;
    color: #333333;
  }

  .title {
    font-weight: bold;
    color: #888888;
    font-size: 16px;
    padding-top: 14px;
  }

  .mat-raised-button {
    width: 250px;
  }

  /* .mat-form-field {
    width: 250px;
  } */

  .mat-input-element {
    color: #bbbbbb;
  }

  .mat-form-field-label {
    color: #bbbbbb !important;
  }

  .input.mat-input-element {
    color: #bbbbbb;
  }
}

@media (max-width: 992px) and (min-width: 480px) {
  .split_text {
    word-break: break-all;
  }

  .tr_table {
    font-size: 12px;
    color: #333333;
  }

  .td_table {
    font-size: 12px;
    color: #333333;
  }

  .title {
    font-weight: bold;
    color: #888888;
    font-size: 16px;
    padding-top: 14px;
  }

  .mat-raised-button {
    width: 250px;
  }

  /* .mat-form-field {
    width: 250px;
  } */

  .mat-input-element {
    color: #bbbbbb;
  }

  .mat-form-field-label {
    color: #bbbbbb !important;
  }

  .input.mat-input-element {
    color: #bbbbbb;
  }
}

@media (min-width: 992px) {
  .split_text {
    word-break: break-all;
  }

  .tr_table {
    font-size: 12px;
    color: #333333;
  }

  .td_table {
    font-size: 12px;
    color: #333333;
  }

  .title {
    font-weight: bold;
    color: #888888;
    font-size: 16px;
    padding-top: 14px;
  }

  .mat-raised-button {
    width: 250px;
  }

  /* .mat-form-field {
    width: 250px;
  } */

  .mat-input-element {
    color: #bbbbbb;
  }

  .mat-form-field-label {
    color: #bbbbbb !important;
  }

  .input.mat-input-element {
    color: #bbbbbb;
  }
}

.mat-dialog-container {
  max-height: 800px;
}

.mat-ff-attachment-file {
  min-width: 500px;
  position: relative;
}

.attchment-filename-wrapper {
  display: none;
  min-width: 150px;
  /* max-width: 800px; */
  padding: 14px;
  border-radius: 6px;
  z-index: 1000;
  /* background: rgba(80, 80, 80, 0.75); */
  background: #eff7fc;
  border: 1px solid grey;
  position: absolute;
  bottom: 70px;
  left: 125px;
}

::ng-deep .mat-form-field:hover .attchment-filename-wrapper {
  display: block;
}

.attachment-filename-label:not(:last-child) {
  margin-bottom: .65rem;
}

.btn-remove-file {
  margin-left: 6px;
  cursor: pointer;
}

@media (max-width: 1420px) {
  .btn-add-file {
    margin-top: 8px;
  }
}

::ng-deep .input-date-group .p-datepicker-trigger {
  height: 32px;
}

.input-date-group {
  display: inline-block;
  margin-bottom: 10px;
}

::ng-deep .input-date-group input {
  font-size: 14px;
  font-family: Roboto, "Helvetica Neue", sans-serif !important;
}

.input-date-group .label-input {
  display: inline-block;
  color: #7a7f81;
  margin-bottom: 2px;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.error-msg {
	color: #f44336;
  font-size: 14px;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

/* Material Design styled wrapper for PrimeNG dropdowns */
.mat-form-field-wrapper {
    position: relative;
    display: block;
    margin-bottom: 1.25em;
    width: 100%;
    padding-bottom: 0;
    padding-top: 20px;
}

/* Reduce padding when has value */
.mat-form-field-wrapper.has-value {
    padding-top: 12px;
}

.mat-form-field-label {
    position: absolute;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    font-family: Roboto, "Helvetica Neue", sans-serif;
    font-weight: 400;
    line-height: 1;
    padding-left: 10px;
    top: 32px;
    left: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    transform-origin: left top;
    z-index: 1;
}

/* Label moves up when dropdown has value or is focused */
.mat-form-field-wrapper.has-value .mat-form-field-label,
.mat-form-field-wrapper.is-focused .mat-form-field-label {
    top: 0px !important;
    font-size: 12px !important;
    transform: scale(0.85) !important;
}

/* Color only blue when focused */
.mat-form-field-wrapper.is-focused .mat-form-field-label {
    color: #3f51b5 !important;
}

/* Color gray when has value but not focused */
.mat-form-field-wrapper.has-value:not(.is-focused) .mat-form-field-label {
    color: #bbbbbb !important;
}

/* Label when focused but no value - same as has-value */
.mat-form-field-wrapper.is-focused:not(.has-value) .mat-form-field-label {
    top: 0px !important;
    font-size: 12px !important;
    color: #3f51b5 !important;
    transform: scale(0.85) !important;
    display: block !important;
    visibility: visible !important;
}

/* Label stays in normal position when no value and not focused */
.mat-form-field-wrapper:not(.has-value):not(.is-focused) .mat-form-field-label {
    top: 32px;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    transform: scale(1);
    display: none !important;
    visibility: hidden !important;
}

/* Underline animation when focused */
.mat-form-field-wrapper.is-focused .mat-form-field-underline {
    background-color: #3f51b5;
    height: 2px;
}

/* Underline animation when has error */
.mat-form-field-wrapper:has(.error-message) .mat-form-field-underline {
    background-color: #f44336;
    height: 2px;
}

.mat-form-field-underline {
    position: absolute;
    bottom: 0;
    left: 10px;
    width: calc(100% - 20px);
    height: 1px;
    background-color: rgba(0, 0, 0, 0.42);
    transition: background-color 0.3s ease, bottom 0.3s ease;
}

/* Move underline up when error message is present */
.mat-form-field-wrapper:has(.error-message) .mat-form-field-underline {
    bottom: 20px;
}

.mat-form-field-wrapper:hover .mat-form-field-underline {
    background-color: rgba(0, 0, 0, 0.87);
}

.mat-form-field-wrapper:focus-within .mat-form-field-underline {
    background-color: #3f51b5;
    height: 2px;
}

.error-message {
    color: #f44336;
    font-size: 12px;
    font-family: Roboto, "Helvetica Neue", sans-serif;
    margin-top: 4px;
    line-height: 1.2;
    padding-left: 10px;
}

/* PrimeNG dropdown styling to match Material Design */
::ng-deep .mat-dropdown {
    width: 100% !important;
    border: none !important;
    border-radius: 0 !important;
    background: transparent !important;
    font-size: 12px !important;
    font-family: Roboto, "Helvetica Neue", sans-serif !important;
    padding: 4px 50px 6px 10px !important;
    box-shadow: none !important;
    min-height: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
}

::ng-deep .mat-dropdown .p-dropdown-label {
    padding: 0 !important;
    color: rgba(0, 0, 0, 0.87) !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
}

::ng-deep .mat-dropdown .p-dropdown-label.p-placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Show placeholder when no value is selected */
::ng-deep .mat-dropdown .p-dropdown-label.p-placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
    font-style: normal !important;
}

/* Force placeholder styling when manually added */
::ng-deep .mat-dropdown .p-dropdown-label.p-placeholder,
::ng-deep .mat-form-field-wrapper:not(.has-value) .mat-dropdown .p-dropdown-label {
    color: rgba(0, 0, 0, 0.6) !important;
    font-style: normal !important;
}

/* Show selected value when value exists */
::ng-deep .mat-dropdown .p-dropdown-label:not(.p-placeholder) {
    color: rgba(0, 0, 0, 0.87) !important;
}

/* Force placeholder when no value */
::ng-deep .mat-dropdown:not(.p-dropdown-filled) .p-dropdown-label,
::ng-deep .mat-dropdown .p-dropdown-label:empty {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Force clear state styling */
::ng-deep .mat-dropdown .p-dropdown-label {
    display: block !important;
}

/* When dropdown is cleared, ensure placeholder shows */
::ng-deep .mat-dropdown[ng-reflect-model=""] .p-dropdown-label,
::ng-deep .mat-dropdown[ng-reflect-model="null"] .p-dropdown-label,
::ng-deep .mat-dropdown .p-dropdown-label:empty::before {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Force show placeholder when no value */
::ng-deep .mat-form-field-wrapper:not(.has-value) .mat-dropdown .p-dropdown-label {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Ensure placeholder shows when dropdown is empty */
::ng-deep .mat-dropdown .p-dropdown-label:empty,
::ng-deep .mat-dropdown .p-dropdown-label[aria-label=""] {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Force placeholder display when value is null */
::ng-deep .mat-dropdown[ng-reflect-ng-model="null"] .p-dropdown-label,
::ng-deep .mat-dropdown[ng-reflect-ng-model=""] .p-dropdown-label {
    color: rgba(0, 0, 0, 0.6) !important;
}

/* Force show label when has value or focused */
.mat-form-field-wrapper.has-value .mat-form-field-label,
.mat-form-field-wrapper.is-focused .mat-form-field-label {
    display: block !important;
    visibility: visible !important;
}

/* Override global #bbbbbb color for dropdown labels */
.mat-form-field-wrapper .mat-form-field-label {
    color: #bbbbbb !important;
}

/* Only blue when focused, not when has value */
.mat-form-field-wrapper.is-focused .mat-form-field-label {
    color: #3f51b5 !important;
}

/* Keep gray color when has value but not focused */
.mat-form-field-wrapper.has-value:not(.is-focused) .mat-form-field-label {
    color: #bbbbbb !important;
}

::ng-deep .mat-dropdown .p-dropdown-trigger,
::ng-deep .mat-dropdown .p-dropdown-clear-icon {
    visibility: visible !important;
    display: flex !important;
    opacity: 1 !important;
}

::ng-deep .mat-dropdown .p-dropdown-trigger {
    color: rgba(0, 0, 0, 0.54) !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute !important;
    right: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

::ng-deep .mat-dropdown .p-dropdown-clear-icon,
::ng-deep .mat-dropdown .p-element .p-icon,
::ng-deep .mat-dropdown .p-button-icon {
    color: rgba(0, 0, 0, 0.54) !important;
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute !important;
    right: 35px !important;
    top: 70% !important;
    transform: translateY(-50%) !important;
    cursor: pointer !important;
    z-index: 10 !important;
}

/* Force show clear icon when dropdown has value - try multiple selectors */
::ng-deep .mat-dropdown[aria-expanded="false"] .p-dropdown-clear-icon,
::ng-deep .mat-dropdown .p-dropdown-clear-icon,
::ng-deep .mat-dropdown .p-element .p-icon {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Show dropdown trigger always */
::ng-deep .mat-dropdown .p-dropdown-trigger {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Dropdown panel styling */
::ng-deep .mat-dropdown-panel {
    border: none !important;
    border-radius: 4px !important;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
                0px 8px 10px 1px rgba(0, 0, 0, 0.14),
                0px 3px 14px 2px rgba(0, 0, 0, 0.12) !important;
    margin-top: 8px !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-items {
    padding: 8px 0 !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-item {
    padding: 10px 16px !important;
    font-size: 12px !important;
    font-family: Roboto, "Helvetica Neue", sans-serif !important;
    color: rgba(0, 0, 0, 0.87) !important;
    min-height: 36px !important;
    display: flex !important;
    align-items: center !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-item:hover {
    background: rgba(0, 0, 0, 0.04) !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-item.p-highlight {
    background: rgba(63, 81, 181, 0.12) !important;
    color: #3f51b5 !important;
}

/* Filter input styling */
::ng-deep .mat-dropdown-panel .p-dropdown-filter-container {
    padding: 6px 12px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12) !important;
    position: relative !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-filter {
    width: 100% !important;
    padding: 6px 30px 6px 10px !important;
    border: 1px solid rgba(0, 0, 0, 0.42) !important;
    border-radius: 3px !important;
    font-size: 13px !important;
    font-family: Roboto, "Helvetica Neue", sans-serif !important;
    outline: none !important;
    height: 32px !important;
    box-sizing: border-box !important;
}

::ng-deep .mat-dropdown-panel .p-dropdown-filter:focus {
    border-color: #3f51b5 !important;
    box-shadow: 0 0 0 1px rgba(63, 81, 181, 0.2) !important;
}

/* Position filter icon properly */
::ng-deep .mat-dropdown-panel .p-dropdown-filter-icon {
    position: absolute !important;
    right: 20px !important;
    top: 65% !important;
    transform: translateY(-50%) !important;
    color: rgba(0, 0, 0, 0.54) !important;
    font-size: 14px !important;
    pointer-events: none !important;
}

.dropdown-option {
    width: 100%;
    display: flex;
    align-items: center;
}

/* Autocomplete panel styling - ensure it follows the input and not fixed */
::ng-deep .mat-autocomplete-panel {
    max-height: 256px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    border-radius: 4px !important;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
                0px 8px 10px 1px rgba(0, 0, 0, 0.14),
                0px 3px 14px 2px rgba(0, 0, 0, 0.12) !important;
    background: white !important;
    position: absolute !important;
    z-index: 1000 !important;
}

::ng-deep .mat-autocomplete-panel .mat-option {
    padding: 10px 16px !important;
    font-size: 14px !important;
    font-family: Roboto, "Helvetica Neue", sans-serif !important;
    color: rgba(0, 0, 0, 0.87) !important;
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    line-height: 48px !important;
}

::ng-deep .mat-autocomplete-panel .mat-option:hover {
    background: rgba(0, 0, 0, 0.04) !important;
}

::ng-deep .mat-autocomplete-panel .mat-option.mat-selected,
::ng-deep .mat-autocomplete-panel .mat-option.mat-active {
    background: rgba(63, 81, 181, 0.12) !important;
    color: #3f51b5 !important;
}

/* CDK Overlay - ensure it repositions on scroll */
::ng-deep .cdk-overlay-pane {
    position: absolute !important;
}

::ng-deep .cdk-overlay-connected-position-bounding-box {
    position: absolute !important;
}

/* Ensure autocomplete overlay follows scroll */
::ng-deep .cdk-overlay-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100% !important;
    width: 100% !important;
    pointer-events: none !important;
    z-index: 1000 !important;
}

::ng-deep .cdk-overlay-backdrop {
    position: absolute !important;
}

::ng-deep .cdk-overlay-pane {
    pointer-events: auto !important;
}

/* Specific styling for refNumber autocomplete */
::ng-deep .refnumber-autocomplete .mat-autocomplete-panel {
    max-height: 256px !important;
}

