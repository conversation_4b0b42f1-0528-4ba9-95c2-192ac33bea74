import { Component, On<PERSON><PERSON><PERSON>, On<PERSON><PERSON><PERSON>, ViewChild, ElementRef, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Observable, Subscription, BehaviorSubject } from 'rxjs';
import { DatePipe, Location, DecimalPipe } from '@angular/common';
import { constants } from '@shared/utils/constants';
import { HttpParams } from '@angular/common/http';
import { ConfirmService } from '@shared/confirm/confirm-dialogs.service';
import { ToastrService } from 'ngx-toastr';
import { Globals } from '@core/global';
import { DisputeManagement } from 'app/model/dispute-management';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { listDisputeCurrency_1, listDisputeStage_1, listDisputeCodeSS, listFraudInves as listFraudInvesConst } from '../../dispute-management-constants';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { SSDisputeManagementService } from '@service/ss-dispute-management.service';
import { DialogService, DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MatDialog } from '@angular/material/dialog';
import { MatAutocomplete } from '@angular/material/autocomplete';
import * as _moment from 'moment';
import { SSTransManagementService } from '@service/ss-trans-management.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { DataService } from '@service/data.service';
import { NoteModalComponent } from './note-modal/note-modal';
import { DisputeUtils } from '../../dispute-utils';
import 'rxjs/add/observable/zip';
import { MatSelectChange } from '@angular/material/select';

const addBlankLabel = (options: Array<{ label: string; value: any }> = []) =>
    options.map((option, index) => index === 0 ? { ...option, label: 'Blank' } : option);
export const MY_FORMATS = {
    parse: {
        dateInput: 'LL',
    },
    display: {
        dateInput: 'DD-MM-YYYY',
        monthYearLabel: 'YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'YYYY',
    },
};

@Component({
    selector: 'dispute-detail',
    templateUrl: './dispute-detail-component.html',
    styleUrls: ['./dispute-detail-component.css', './dispute-detail-component.scss'],
    providers: [DialogService, SSDisputeManagementService,
        { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
        { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
    ],
})

export class DisputeDetailComponent implements OnInit, OnDestroy, AfterViewInit {
    public back = 'Dispute Search';
    public title = 'Dispute Details';
    public readonly constants = constants;

    // Focus states for dropdown labels
    public dropdownFocusStates = {
        onepayPic: false,
        disputeStage: false,
        disputeCode: false,
        disputeCurrency: false,
        fraudInves: false,
        businessCategory: false,
        disputeReason: false,
        outcome: false,
        disputeDesc: false,
        fileFromIssuers: false
    };

    /* hien thi du lieu chi tiet giao dich */
    public _disputeId;
    public _transactionId: string;
    public _paygate: string;
    public subscription: Subscription;
    public GetTransactionID: any;
    public disputeHistory: any[] = [];
    public transactionHistory: any[] = [];
    public fileHistory: any[] = [];
    public cardTypeList: Array<any> = [];
    public loading: boolean;
    public originalDispute: any;
    filteredOptions: Observable<string[]>;
    filteredRefNumbers: Observable<string[]>;
    filteredRefNumbersSubject = new BehaviorSubject<string[]>([]);
    refNumbers: string[] = [];

    public fildeDownloadExis = ''

    /* end hien thi chi tiet giao dich */
    files: File[] = [];
    fileNames:string = "";
    isResolved: boolean;
    canRefund: boolean;

    MAX_FILENAME_LENGTH_DISPLAY = 64;

    @ViewChild("fileInput")
    fileInputRef: any;

    @ViewChild('autoRefNumber', { read: MatAutocomplete })
    autoRefNumber: MatAutocomplete;

    // Infinite scroll variables for refNumber autocomplete
    refNumberDisplayLimit = 5; // Initial number of items to display
    refNumberPageSize = 3; // Number of items to load per scroll
    refNumberInitialLimit = 5; // Store initial limit for reset
    filteredRefNumbersList: string[] = []; // Current filtered list (all matches)

    allowedExtensionArr = ['.zip'];
    allowedExtensionStr = this.allowedExtensionArr.join(',');
    invalidExtArr = [];


    listOutCome = [
        { label: 'Blank', value: '' },
        { label: 'Submit evidence', value: '1' },
        { label: 'Lost due to Overdue', value: '3' },
        { label: 'Lost at Arbitration', value: '4' },
        { label: 'Won at Arbitration', value: '5' },
        { label: 'Dispute is canceled', value: '6' },
        { label: 'Funds back to merchant', value: '7' },
        { label: 'Order confirmed', value: '8' }
    ];
    InterReason = [
        { label: 'Blank', value: '' },
        { label: 'Merchandise or Service not received', value: '1' },
        { label: 'Cancelled Merchandise/Services', value: '2' },
        { label: 'Merchandise or Service not as described', value: '3' },
        { label: 'Fraud', value: '4' },
        { label: 'Duplicate Processing/ Paid by other means', value: '5' },
        { label: 'Incorrect Amount', value: '6' },
        { label: 'Canceled recurring billing', value: '7' },
        { label: 'Request a copy of transaction receipt', value: '8' },
        { label: 'Not recognize transaction', value: '9' },
        { label: 'Others', value: '10' },
        { label: 'Credit not processed', value: '11' }
    ];
    InterOutCome = [
        { label: 'Blank', value: '' },
        { label: 'Submit evidence', value: '1' },
        { label: 'Lost due to Overdue', value: '3' },
        { label: 'Lost at Arbitration', value: '4' },
        { label: 'Won at Arbitration', value: '5' },
        { label: 'Dispute is canceled', value: '6' },
        { label: 'Funds back to merchant', value: '7' },
        { label: 'Refund', value: 'Refund' }
    ]
    // data init
    disableOutCome: boolean = false;
    disputeDetail: any;
    disputeDetailForm: FormGroup;
    listOnepayPic: any[];
    listBusinessCategory: any[] = [];
    listDisputeStage: any[] = addBlankLabel(listDisputeStage_1);
    listDisputeReason: any[];
    listDisputeCodeRiskTotal: any[] = [];
    listDisputeCodeRiskFiltered: any[] = [];
    listDisputeCodeSS: any[] = listDisputeCodeSS;
    disputeCodeResponse;
    listDisputeCurrency: any[] = listDisputeCurrency_1;
    listOutcome: any[];
    listFraudInves = addBlankLabel(listFraudInvesConst);
    businessCategoryID: any;
    options: string[] = [];
    isDOM: boolean = false;
    isINT: boolean = false;
    isRISK: boolean = false;
    isSS: boolean = false;
    refund_amount: number;
    disputeDescList = [
        { label: "DPR-01: Refund at merchant's request", value: "DPR-01" },
        { label: "DPR-02: Lost due to Overdue", value: "DPR-02" },
        { label: "DPR-03: Lost at arbitration", value: "DPR-03" },
    ];
    disputeDesc: string;
    dataEditer:string = "";
    isPopup: boolean = false;
    public DISPUTE_ROLE = '';
    listIssuersFromFile: any[] = [
        { label: 'No', value: 'No' },
        { label: 'Yes', value: 'Yes' }
    ];
    issuersFromFile: string = 'no';
    enableDoDispute = true;

    constructor(private router: Router, private activatedRouter: ActivatedRoute, private toastr: ToastrService, public global: Globals,
        private confirmService: ConfirmService, private location: Location,
        private disputeService: SSDisputeManagementService,
        private ssTranService: SSTransManagementService,
        public datePipe: DatePipe,
        private decimalPipe: DecimalPipe,
        public dialogService: MatDialog,
        public dialogEmailService: DialogService,
        public popupData: DynamicDialogConfig,
        public disputeUtil: DisputeUtils,
        public fileDataService: DataService,
        private cdr: ChangeDetectorRef) {
        // Sync initial limit
        this.refNumberInitialLimit = this.refNumberDisplayLimit;
        
        this.disputeDetail = new DisputeManagement();
        this.disputeDetailForm = new FormGroup({
            'disputeSender': new FormControl('', []),
            'onepayPic': new FormControl('', [Validators.required]),
            'sendDisputeTo': new FormControl('',[]),
            'merchantRespond': new FormControl('', []),
            'businessCategory': new FormControl('', []),
            'disputeStage': new FormControl('', []),
            'disputeReason': new FormControl('', []),
            'disputeCode': new FormControl('', []),
            'disputeAmount': new FormControl('', [Validators.required]),
            'disputeCurrency': new FormControl('', [Validators.required]),
            'dueDate': new FormControl('', [Validators.required]),
            'outcome': new FormControl('', []),
            'fraudInves': new FormControl('', []),
            'refNumber': new FormControl('', []),
            'note': new FormControl('', []),
            'disputeDate': new FormControl('', [Validators.required]),
            'fileFromIssuers': new FormControl('', []),
        });
    }

    ngOnInit() {
        this.subscription = this.activatedRouter.params.subscribe(params => {
            if (this.popupData?.data?.isPopup) {
                console.log('popupData', this.popupData);
                this._disputeId = this.popupData.data.disputeId;
                this.isPopup = true;
            } else {
                this._disputeId = params['disputeId']
            }
            let backLocation = params['back_location'];
            if (backLocation === 'risk_domestic') {
                this.DISPUTE_ROLE = 'risk_dispute_domestic';
            } else if (backLocation === 'risk-international') {
                this.DISPUTE_ROLE = 'risk_dispute_international';
            } else {
                this.DISPUTE_ROLE = 'ss_dispute_domestic';
            }

            Observable.zip(
                this.ssTranService.getDropdownCardList()
                , this.disputeService.getDisputeCode()
                , this.disputeService.getAllOnePayPics(this.DISPUTE_ROLE)
                , this.disputeService.getBusinessCater()
                , this.disputeService.searchDisputeDetailById(this._disputeId)
            ).subscribe(arr => {
                let cardListRes = arr[0];
                let disputeCodeRes = arr[1];
                let operatorRes = arr[2];
                let businessCateRes = arr[3];
                let disputeDetailRes = arr[4];

                const arrAcquires = cardListRes.acquirers;
                this.cardTypeList = arrAcquires instanceof Array ? arrAcquires.map(m => {
                    return { value: `${m.acquirer_id.split('|').join(',')}`, label: m.acquirer_short_name };
                }) : [];
                this.cardTypeList.push({ value: "0,QR", label: "N/A" });

                this.listDisputeCodeRiskTotal = this.disputeUtil.formatListDisputeCodeRisk(disputeCodeRes);
                
                this.listOnepayPic = [];
                operatorRes.list?.forEach(element => {
                    if (element['id']  && element['name'] ) {
                        element['id'] = element.id.toString();
                        this.listOnepayPic.push(element)
                    }
                });

                this.listBusinessCategory = [{ label: 'Blank', value: '' }];
                if (businessCateRes.list) {
                    for (let i = 0; i < businessCateRes.list.length; i++) {
                        this.listBusinessCategory.push({ label: businessCateRes.list[i].NAME, value: businessCateRes.list[i].N_ID.toString() });
                    }
                }
                this.processDisputeDetail(disputeDetailRes);
            });
        });



        this.filteredOptions = this.disputeDetailForm.controls.sendDisputeTo.valueChanges.pipe(
            startWith(''),
            map(value => this._filter(value || '')),
        );

        // Setup autocomplete for refNumber field with debounce using BehaviorSubject
        this.filteredRefNumbers = this.filteredRefNumbersSubject.asObservable();

        this.disputeDetailForm.controls.refNumber.valueChanges.pipe(
            startWith(''),
            debounceTime(300),
            distinctUntilChanged()
        ).subscribe(value => {
            console.log('RefNumber value changed:', value);
            this._filterRefNumbers(value || '');
        });
    }

    getListCodeSS() {
        console.log('getListCodeSS', 1, this.listDisputeCodeSS, this.originalDispute.transactionType, this.originalDispute.transactionStatus);
        this.listDisputeCodeSS.sort((a,b) => a.value > b.value? 1 : -1);
        if (this.originalDispute.transactionType == "Purchase" && this.originalDispute.transactionStatus == "Successful") {
            this.listDisputeCodeSS = this.listDisputeCodeSS
                .filter(item => ['', '10001', '10002'].includes(item.value))
        }
        if (this.originalDispute.transactionType == "Purchase" && this.originalDispute.transactionStatus == "Failed") {
            this.listDisputeCodeSS = this.listDisputeCodeSS
                .filter(item => ['', '10003'].includes(item.value))
        }
        if (this.originalDispute.transactionType == "Refund") {
            this.listDisputeCodeSS = this.listDisputeCodeSS
                .filter(item => ['', '10004'].includes(item.value))
        }
        console.log('getListCodeSS', 2, this.listDisputeCodeSS, this.originalDispute.transactionType, this.originalDispute.transactionStatus);
    }

    filterListDisputeCodeRisk() {
        this.listDisputeCodeRiskFiltered = this.listDisputeCodeRiskTotal
            .filter(e => e.card == this.disputeDetail.cardType)
            .filter(e => e.stage == this.disputeDetail.disputeStage);
        console.log('listDisputeCodeRiskTotal', this.listDisputeCodeRiskTotal);
        console.log('cardType', this.disputeDetail.cardType);
        console.log('stage', this.disputeDetail.disputeStage);
        console.log('listDisputeCodeRiskFiltered', this.listDisputeCodeRiskFiltered);
    }

    private selectOptionSendTo(sendTo, Option) {
        var filterValue: any[] = sendTo.split(";");
        var returnString = "";
        filterValue.pop();
        filterValue.push(Option);
        filterValue.forEach(element => {
            returnString += element + ";"
        });
        return returnString.slice(0, -1);
    }
    private _filter(value: string): string[] {
        const filterValue = value.toLowerCase().split(";")[value.split(";").length - 1];
        return this.options.filter(option => option.toLowerCase().includes(filterValue));
    }

    private _filterRefNumbers(value: string): string[] {
        if (!value || value.trim() === '') {
            this.filteredRefNumbersList = [...this.refNumbers];
        } else {
            const filterValue = value.toLowerCase().trim();
            this.filteredRefNumbersList = this.refNumbers.filter(refNum =>
                refNum.toLowerCase().includes(filterValue)
            );
        }
        // Reset display limit to initial limit when filter changes
        this.refNumberDisplayLimit = this.refNumberInitialLimit;
        // Return limited results for display and update the subject
        const limitedResults = this.filteredRefNumbersList.slice(0, this.refNumberDisplayLimit);
        // Update the BehaviorSubject to trigger UI update
        setTimeout(() => {
            this.filteredRefNumbersSubject.next(limitedResults);
        });
        return limitedResults;
    }

    convertCardType(inputData: string, type: string): string {
        var outputData = '';
        // Client is MSB, display Mobile Banking / E-Wallet instead of QR
        if (type != 'ND' && type != 'APP') {
            outputData = inputData;
        } else {
            if (inputData) {
                let acq = this.cardTypeList.find(obj => obj.value.split(',').includes(inputData));
                if (acq !== undefined)
                    outputData = acq.label;
                else
                    outputData = '';
            }
        }
        if (!outputData) {
            outputData = inputData;
        }
        console.log('convertCardType', inputData, type, outputData);
        return outputData;
    }

    reloadDetail() {
        this.disputeService.searchDisputeDetailById(this._disputeId).subscribe(data => {
            this.processDisputeDetail(data);
        });
    }

    processDisputeDetail(data: any) {
        if (!data || !data.list) return;
        
        this.disputeHistory = [];
        data.list.forEach(item => {
            item.sftpAppleFileFromIssuers = this.disputeUtil.formatDataFileFrom(item.sftpAppleFileFromIssuers)
            this.disputeHistory.push(item);
        });
        
        if(data.transactionHistory){
            this.transactionHistory = [];
            data.transactionHistory.forEach(item => {
                this.transactionHistory.push(item);
            }); 
        }

        if(data.fileHistory){
            this.fileHistory = [];
            data.fileHistory.forEach(item => {
                this.fileHistory.push(item);
            });
        }

        // Parse refNumbers from API response for RISK && INT disputes
        if(data.refNumbers && Array.isArray(data.refNumbers)){
            this.refNumbers = data.refNumbers;
        } else {
            this.refNumbers = [];
        }

        this.originalDispute = data.list.find(item => item && item.parentId == 0);
        if (!this.originalDispute) {
            console.log('Error finding original dispute!', 'No data with parentId==0');
            return;
        }

        this.isResolved = this.originalDispute.disputeStatus?.toLowerCase() == 'resolved';
        // this.canRefund = ['purchase', 'capture'].includes(this.originalDispute.transactionType?.toLowerCase());
        this.canRefund = ['purchase','capture'].includes(this.originalDispute.transactionType?.toLowerCase())
                        && !['failed'].includes(this.originalDispute.transactionStatus?.toLowerCase()) // GD failed thì ko có refund nữa
                        // && this.originalDispute.paygate==='QT'
                        ;

        this.isSS = this.originalDispute.department == 'SERVICE_SUPPORT';
        this.isRISK = !this.isSS;
        console.log(this.isSS? 'SS Dispute' : 'Risk Dispute');

        this.isINT = this.disputeUtil.isINT(this.originalDispute);
        this.isDOM = !this.isINT;
        console.log('isINT', this.isINT, 'isDOM', this.isDOM);
        // update theo y/c: https://10.36.36.63:8618/op_pm/Project/Detail/019a0638-e4f3-7d7a-a5ae-0a5235aa4138
        if (this.isRISK && this.isINT) {
            this.disputeDescList.push({ label: "DPR-05: Others", value: "DPR-05" });
        }

        // clone sang disputeDetail va convert mot so truong
        this.disputeDetail = JSON.parse(JSON.stringify(this.originalDispute));
        this.disputeDetail.operatorId = this.global.activeProfile.n_id;
        this.disputeDetail.operatorName = this.global.activeProfile.name;
        this.disputeDetail.transactionDate = new Date(this.originalDispute.transactionDate);
        
        // OnePay PIC: Only set default if it's a required field AND empty
        // For isRISK && isINT, let it be null to show placeholder
        if (this.originalDispute.onepayPic) {
            this.disputeDetail.onepayPic = this.originalDispute.onepayPic;
        } else if (!(this.isRISK && this.isINT)) {
            // Only set default for non-RISK or non-INT cases
            this.disputeDetail.onepayPic = this.global.activeProfile.n_id.toString();
        }
        
        // Normalize values: convert empty string to null to avoid matching "Blank" option
        this.disputeDetail.onepayPic = this.normalizeValue(this.disputeDetail.onepayPic);
        this.disputeDetail.businessCategory = this.normalizeValue(this.disputeDetail.businessCategory);
        this.disputeDetail.disputeStage = this.normalizeValue(this.disputeDetail.disputeStage);
        this.disputeDetail.disputeReason = this.normalizeValue(this.disputeDetail.disputeReason);
        this.disputeDetail.disputeCode = this.normalizeValue(this.disputeDetail.disputeCode);
        this.disputeDetail.outcome = this.normalizeValue(this.disputeDetail.outcome);
        this.disputeDetail.fraudInves = this.normalizeValue(this.disputeDetail.fraudInves);
        this.disputeDetail.sftpAppleFileFromIssuers = this.normalizeValue(this.disputeDetail.sftpAppleFileFromIssuers);
        
        if (this.convertChannel(this.originalDispute.paygate) === 'INT') {
            this.disputeDetail.disputeSender = this.originalDispute.disputeSender ? this.originalDispute.disputeSender : this.originalDispute.acquirerDownload;
        } else {
            this.disputeDetail.disputeSender = this.originalDispute.disputeSender ? this.originalDispute.disputeSender : this.convertCardType(this.originalDispute.cardType, this.originalDispute.paygate);
        }
        
        this.listDisputeCurrency = listDisputeCurrency_1;
        
        // Dispute Currency: Keep original value or use transaction currency (but allow null)
        this.disputeDetail.disputeCurrency = this.normalizeValue(
            this.originalDispute.disputeCurrency || this.originalDispute.transactionCurrency
        );
        
        // Dispute Amount: Keep original or use transaction amount
        this.disputeDetail.disputeAmount = this.originalDispute.disputeAmount || this.originalDispute.transactionAmount;
        if (!this.originalDispute.sendDisputeTo) {
            this.disputeService.emailListByPartner(this.originalDispute.partnerId, this.originalDispute.paygate).subscribe(emailResponse => {
                this.disputeDetail.sendDisputeTo = emailResponse.emailList && emailResponse.emailList.includes(',') ? emailResponse.emailList.replaceAll(',', ';') : emailResponse.emailList;
            });
        }

        this.listDisputeStage = addBlankLabel(listDisputeStage_1);

        if (this.originalDispute.dueDate) {
            this.disputeDetail.dueDate = new Date(this.originalDispute.dueDate);
        } else {
            var dueDate = new Date(this.originalDispute.disputeDate);
            dueDate.setDate(dueDate.getDate() + 5);
            this.disputeDetail.dueDate = dueDate;
        }
        this.disputeDetail.disputeDate = new Date(this.originalDispute.disputeDate);
        this.originalDispute.cardTypeConvert = this.convertCardType(this.originalDispute.cardType, this.originalDispute.paygate);
        this.originalDispute.disputeStatusConvert = this.convertDisputeStatus(this.originalDispute.disputeStatus);
        this.originalDispute.channelConvert = this.convertChannel(this.originalDispute.paygate);

        if (this.isINT) {
            if (!this.listDisputeReason) {
                this.listDisputeReason = this.InterReason;
            }
            // Set required for International disputes
            this.disputeDetailForm.controls.disputeStage.setValidators(Validators.required);
            this.disputeDetailForm.controls.disputeCode.setValidators(Validators.required);
        } else {
            if (this.originalDispute.transactionStatus == 'Successful' && this.originalDispute.transactionType == 'Purchase') {
                if (!this.listDisputeReason) {
                    this.listDisputeReason = [];
                    this.listDisputeReason.push({
                        label: 'Blank',
                        value: ''
                    });
                    this.listDisputeReason.push({
                        label: 'Merchandise or Service not received',
                        value: '12'
                    });
                    this.listDisputeReason.push({
                        label: 'Fraud / Not recognize transaction',
                        value: '13'
                    });
                }
            }
            else if (this.originalDispute.transactionStatus == 'Failed' && this.originalDispute.transactionType == 'Purchase') {
                if (!this.listDisputeReason) {
                    this.listDisputeReason = [];
                    this.listDisputeReason.push({
                        label: 'Blank',
                        value: ''
                    });
                    this.listDisputeReason.push({
                        label: 'Transaction failed but got deducted',
                        value: '14'
                    });
                    this.disputeDetail.disputeReason = this.listDisputeReason[0].value
                }
            }
            else if (this.originalDispute.transactionStatus == 'Successful' && this.originalDispute.transactionType == 'Refund') {
                if (!this.listDisputeReason) {
                    this.listDisputeReason = [];
                    this.listDisputeReason.push({
                        label: 'Blank',
                        value: ''
                    });
                    this.listDisputeReason.push({
                        label: 'Refund not processed',
                        value: '15'
                    });
                    this.disputeDetail.disputeReason = this.listDisputeReason[0].value
                }
            } else {
                if (!this.listDisputeReason) {
                    this.listDisputeReason = [];
                    this.listDisputeReason.push({
                        label: 'Blank',
                        value: ''
                    });
                    this.listDisputeReason.push({
                        label: 'Merchandise or Service not received',
                        value: '12'
                    });
                    this.listDisputeReason.push({
                        label: 'Fraud / Not recognize transaction',
                        value: '13'
                    });
                }
            }

        } 
        
        this.getListOutcome();
        if (this.isSS) {
            this.getListCodeSS();
        } else {
            this.filterListDisputeCodeRisk();
        }

        this.disputeService.searchDisputeRefundAmount(this.originalDispute.transactionId,this.originalDispute.paygate).subscribe(e=>{
            console.log('search refund amount response', e);
            this.originalDispute.refundAmount = e?.refund_amount || 0;

            // gui cap nhat ra man list
            this.disputeDetail.refundAmount = this.originalDispute.refundAmount;
            const {id, refundAmount, ...otherFields} = this.disputeDetail;
            this.disputeUtil.notiSaveDispute({id, refundAmount});
        });
        
        console.log('originalDispute', this.originalDispute);
        console.log('disputeDetail', this.disputeDetail);
        
        // Patch all form values (convert empty string to null)
        this.patchFormValues();
        
        setTimeout(() => {
            if (this.isRISK && this.isINT) {
                this.triggerInitialValidation();
            }
        });
    }


    getListOutcome() {
        this.listOutcome = [];
        this.listOutcome.push({
            label: 'Blank',
            value: ''
        });
        if (this.disputeDetail.outcome === "Refund") {
            this.disableOutCome = true;
            this.listOutcome.push({
                label: 'Refund',
                value: 'Refund'
            });
        }
        if (this.isSS) {
            if (this.originalDispute.transactionType == 'Purchase') {
                if (this.originalDispute.transactionStatus == 'Failed') {
                    this.listOutcome.push({
                        label: 'Resolved',
                        value: 'Resolved'
                    });
                } else {
                    this.listOutcome.push({
                        label: 'Order confirmed',
                        value: '8'
                    });
                    this.listOutcome.push({
                        label: 'Refunded',
                        value: 'Refunded'
                    });
                }
            } else {
                this.listOutcome.push({
                    label: 'Resolved',
                    value: 'Resolved'
                });
            }
        } else if (this.isRISK) {
            if (this.isDOM) {
                if (this.originalDispute.transactionType == 'Purchase') {
                    if (this.originalDispute.transactionStatus == 'Failed') {
                        this.listOutcome.push({
                            label: 'Resolved',
                            value: 'Resolved'
                        });
                    } else {
                        this.listOutcome.push({
                            label: 'Submit evidence (Order confirmed)',
                            value: '8'
                        });
                        this.listOutcome.push({
                            label: 'Refunded',
                            value: 'Refunded'
                        });
                    }
                } else {
                    this.listOutcome.push({
                        label: 'Resolved',
                        value: 'Resolved'
                    });
                }
            } {
                this.listOutcome = this.InterOutCome;
            }
        }
    }

    AutoCompleteDisplay(item: any): string {
        if (item == undefined) { return }
        return item.label;
    }

    displayRefNumber(value: string): string {
        return value;
    }

    // Debug method to check infinite scroll state
    debugRefNumberState() {
        console.log('RefNumber Debug State:', {
            totalRefNumbers: this.refNumbers.length,
            filteredRefNumbersList: this.filteredRefNumbersList.length,
            refNumberDisplayLimit: this.refNumberDisplayLimit,
            refNumberPageSize: this.refNumberPageSize,
            refNumberInitialLimit: this.refNumberInitialLimit,
            currentFilteredSubjectValue: this.filteredRefNumbersSubject.value?.length || 0
        });
    }

    private triggerInitialValidation(): void {
        if (!this.disputeDetailForm) {
            return;
        }
        this.disputeDetailForm.markAllAsTouched();
        Object.values(this.disputeDetailForm.controls).forEach(control => control.updateValueAndValidity());
    }

    // Generic function to get dropdown label (with or without asterisk based on isRISK && isINT)
    getDropdownLabel(fieldName: string, label: string): string {
        return (this.isRISK && this.isINT) ? label : `${label} *`;
    }

    // Check if a form field has a valid value (not null, not undefined, not empty string)
    hasValue(controlName: string): boolean {
        const control = this.disputeDetailForm?.get(controlName);
        if (!control) return false;
        
        const value = control.value;
        return value !== null && value !== undefined && value !== '';
    }

    // Helper: Convert empty string to null to avoid matching "Blank" option
    private normalizeValue(value: any): any {
        // Convert empty string, undefined to null
        if (value === '' || value === undefined) {
            return null;
        }
        return value;
    }

    // Patch all form values from disputeDetail (after loading data)
    private patchFormValues() {
        if (!this.disputeDetailForm || !this.disputeDetail) return;

        // Patch all form values, converting empty string to null
        this.disputeDetailForm.patchValue({
            // Text inputs
            disputeSender: this.disputeDetail.disputeSender,
            sendDisputeTo: this.disputeDetail.sendDisputeTo,
            merchantRespond: this.disputeDetail.merchantRespond,
            disputeAmount: this.disputeDetail.disputeAmount,
            refNumber: this.disputeDetail.refNumber,
            note: this.disputeDetail.note,
            
            // Dates
            disputeDate: this.disputeDetail.disputeDate,
            dueDate: this.disputeDetail.dueDate,
            
            // Dropdowns - normalize to prevent empty string matching "Blank"
            onepayPic: this.normalizeValue(this.disputeDetail.onepayPic),
            businessCategory: this.normalizeValue(this.disputeDetail.businessCategory),
            disputeStage: this.normalizeValue(this.disputeDetail.disputeStage),
            disputeReason: this.normalizeValue(this.disputeDetail.disputeReason),
            disputeCode: this.normalizeValue(this.disputeDetail.disputeCode),
            disputeCurrency: this.normalizeValue(this.disputeDetail.disputeCurrency),
            outcome: this.normalizeValue(this.disputeDetail.outcome),
            fraudInves: this.normalizeValue(this.disputeDetail.fraudInves),
            fileFromIssuers: this.normalizeValue(this.disputeDetail.sftpAppleFileFromIssuers),
        }, { emitEvent: false });

        // console.log('Form values patched:', this.disputeDetailForm.value);
        // console.log('Dropdown values:', {
        //     onepayPic: this.disputeDetailForm.get('onepayPic').value,
        //     disputeStage: this.disputeDetailForm.get('disputeStage').value,
        //     disputeCode: this.disputeDetailForm.get('disputeCode').value,
        //     disputeCurrency: this.disputeDetailForm.get('disputeCurrency').value,
        // });
    }

    /**
     * Clear dropdown using Reactive Forms
     */
    onDropdownClear(controlName: string) {
        console.log(`Clear event triggered for ${controlName}`);

        // 1. Reset Form Control (Single Source of Truth)
        const control = this.disputeDetailForm.get(controlName);
        if (control) {
            control.setValue(null);
            control.markAsPristine();
            control.markAsUntouched();
        }

        // 2. Sync model object (for backward compatibility)
        if (controlName === 'fileFromIssuers') {
            this.disputeDetail.sftpAppleFileFromIssuers = null;
        } else if (controlName === 'disputeDesc') {
            this.disputeDesc = null;
        } else {
            this.disputeDetail[controlName] = null;
        }

        // 3. Reset focus state
        this.dropdownFocusStates[controlName] = false;

        console.log(`${controlName} cleared successfully`);
    }

    // Handle selection changes - includes business logic
    OnSelected(fieldName: string, value: any) {
        console.log('Selected:', fieldName, value);
        
        // Special business logic
        if (fieldName === 'disputeStage') {
            // Clear dispute code when stage changes
            this.disputeDetailForm.get('disputeCode')?.setValue(null);
            this.disputeDetail.disputeCode = null;
            this.filterListDisputeCodeRisk();
        }

        // Sync model object (for backward compatibility)
        if (fieldName === 'fileFromIssuers') {
            this.disputeDetail.sftpAppleFileFromIssuers = value;
        } else if (fieldName === 'disputeDesc') {
            this.disputeDesc = value;
        } else {
            this.disputeDetail[fieldName] = value;
        }
    }

    ngAfterViewInit() {
        // Setup infinite scroll for refNumber autocomplete
        if (this.autoRefNumber) {
            this.autoRefNumber.opened.subscribe(() => {
                setTimeout(() => {
                    if (this.autoRefNumber && this.autoRefNumber.panel) {
                        const panel = this.autoRefNumber.panel.nativeElement;
                        if (panel) {
                            console.log('Autocomplete panel opened, setting up scroll listener');
                            // Remove existing listener to avoid duplicates
                            panel.removeEventListener('scroll', this.onRefNumberScroll.bind(this));
                            // Add new listener
                            panel.addEventListener('scroll', this.onRefNumberScroll.bind(this));
                        }
                    }
                }, 100);
            });

            // Also listen for panel close to clean up
            this.autoRefNumber.closed.subscribe(() => {
                console.log('Autocomplete panel closed');
            });
        }
    }

    onRefNumberScroll(event: any) {
        const panel = event.target;
        const scrollTop = panel.scrollTop;
        const scrollHeight = panel.scrollHeight;
        const clientHeight = panel.clientHeight;

        console.log('Scroll event:', {
            scrollTop,
            scrollHeight,
            clientHeight,
            nearBottom: scrollTop + clientHeight >= scrollHeight - 50,
            currentDisplayLimit: this.refNumberDisplayLimit,
            totalFiltered: this.filteredRefNumbersList.length
        });

        // Check if scrolled near bottom (within 50px) and there are more items to load
        if (scrollTop + clientHeight >= scrollHeight - 50 &&
            this.refNumberDisplayLimit < this.filteredRefNumbersList.length) {

            // Load more items
            const currentLimit = this.refNumberDisplayLimit;
            const newLimit = Math.min(
                currentLimit + this.refNumberPageSize,
                this.filteredRefNumbersList.length
            );

            console.log('Loading more items:', {
                currentLimit,
                newLimit,
                totalFiltered: this.filteredRefNumbersList.length,
                pageSize: this.refNumberPageSize
            });

            if (newLimit > currentLimit) {
                this.refNumberDisplayLimit = newLimit;
                // Update the BehaviorSubject with more items
                const updatedList = this.filteredRefNumbersList.slice(0, this.refNumberDisplayLimit);
                this.filteredRefNumbersSubject.next(updatedList);
                console.log('Updated list with', updatedList.length, 'items out of', this.filteredRefNumbersList.length, 'total');

                // Trigger change detection
                this.cdr.detectChanges();
            }
        }
    }

    ngOnDestroy() {
        if (this.subscription)
            this.subscription.unsubscribe();
    }

    isActive(functionName: string) {
        return this.global.isActive(functionName);
    }


    backToPage() {
        let params = this.activatedRouter.snapshot.queryParams;
        let backURL =['/ss-dispute-management/international'];
        if ('risk-domestic' == params['back_location']) {
            backURL =['/risk-dispute-management/domestic'];
        } else if ('risk-international' == params['back_location']) {
            backURL =['/risk-dispute-management/international'];
        } else {
            backURL =['/ss-dispute-management/list'];
        }
        let {back_location, ...params2} = params;
        console.log('params', params);
        console.log('params2', params2);
        if(localStorage.getItem("listDispute")){
            let lstDispute = JSON.parse(localStorage.getItem("listDispute"));
            lstDispute.forEach(el => {
                if(el.transactionId == this.originalDispute.transactionId){
                    el.noteDisputeCase = this.dataEditer;
                }
                else{
                    el.noteDisputeCase = "";
                }
            });
            localStorage.setItem("listDispute", JSON.stringify(lstDispute));
        }
        //khi bấm back thì ra màn list ko load data từ api
        this.router.navigate(backURL, { queryParams: { ...params2, isBack:'true' }});
    };

    cancelDispute() {

    }

    refund(): void {
        if (this.enableDoDispute) {
            this.enableDoDispute = false;
            // validate disputeDesc
            if (!this.disputeDesc || !this.disputeDesc.length) {
                this.toastr.error('Please select Description from dropdown list!');
                return;
            }
            let check = this.validateRefundAmount();
            if (!check.valid) {
                this.toastr.error(check.reason || "Invalid amount!");
                return;
            }

            let body = {
                "merchant_trans_ref": this.originalDispute.merchantTransactionReference || "",
                "merchant_id": this.originalDispute.merchantId || "",
                "amount": this.refund_amount || 0,
                "command": this.getRefundCommand(this.originalDispute.transactionType),
                "note": "",
                "userId": this.global.activeProfile['n_id'] || "",
                "dispute_reason": this.disputeDesc || "",
            } ;
            // console.log(this.originalDispute);
            // console.log("postRefundDispute", body);
            this.confirmService.build().message(`Are you sure you want to Refund ${this.originalDispute.transactionCurrency} ${this.decimalPipe.transform(this.refund_amount, '1.0-2')}?`)
            .title('Notice!')
            .no('No')
            .yes('Yes')
            .confirm().subscribe(
                accept => {
                    if (!accept) return;
                    this.disputeDesc = "";
                    this.refund_amount = undefined;

                    this.disputeService.postRefundDispute(body).subscribe(
                        res => {
                            if (res.code == '200' || res.code == '201') {
                                this.toastr.success('Dispute Refund Successfully');
                                this.enableDoDispute = true;
                                this.syncGeneralTransaction(this.originalDispute.paygate);
                            }
                        }
                    );
                });
        }
    }

    validateRefundAmount() {
        // Không validate amount đối với refund dispute gd QT của RISK (xem /issues/59806)
        // update ngày 06/11/2025: https://10.36.36.63:8618/op_pm/Project/Detail/019a0638-e4f3-7d7a-a5ae-0a5235aa4138]
        if (this.isRISK && this.isINT) {
            
            if (!this.refund_amount || this.refund_amount <= 0) {
                return {
                    valid: false,
                    reason: 'Invalid refund amount.'
                };
            }

            const totalRefund = this.originalDispute.refundAmount + this.refund_amount;
            const maxAllowed = this.originalDispute.transactionAmount * 1.5;

            if (totalRefund > maxAllowed) {
                return {
                    valid: false,
                    reason: 'Total refund amount cannot exceed 150% of the transaction amount.'
                };
            }

            return { valid: true };
        }

        // validate refund_amount vs transactionAmount
        if (this.refund_amount > parseFloat(this.originalDispute.transactionAmount) || this.refund_amount <= 0
            || this.refund_amount === undefined || this.refund_amount === null) {
            return {
                valid: false,
                reason: 'Invalid refund amount.'
            };
        }
        // validate total refund amount vs transactionAmount
        if (this.originalDispute.refundAmount + this.refund_amount > this.originalDispute.transactionAmount) {
            console.log(`${this.originalDispute.refundAmount} + ${this.refund_amount} vs ${this.originalDispute.transactionAmount}`);
            return {
                valid: false,
                reason: 'Total Refund amount cannot exceed Transaction amount.'
            };
        }
        return {valid: true};
    }

    syncGeneralTransaction(paygate: string) {
        this.disputeService.syncGeneralTxn(paygate).subscribe(res => {
            // console.log(res);
            if (res.code && res.code == "200" && res.message && res.message.includes('last_update')) {
                this.toastr.success('Transaction history synced successfully!');   
            } else {
                console.log('syncGeneralTransaction failed');
            }

            this.reloadDetail();
        })
    }

    getRefundCommand(transType: string) {
        if (transType == 'Purchase') {
            return 'refundPurchase';
        } else if (transType == 'Capture') {
            return 'refundCapture';
        } else {
            return 'refund';
        }
    }

    downloadFile(){
        this.disputeService.getFileNameDispute(this.disputeDetail.transactionId).subscribe(res=>{
            if(res && res.fileName && res.fileName != 'File Not found'){
                this.disputeService.downloadFileDispute(this.disputeDetail.transactionId).subscribe(
                    resp => {
                        if(resp.fileName == 'File Not found'){
                        }else {
                        var downloadURL = window.URL.createObjectURL(resp);
                        var link = document.createElement('a');
                        link.href = downloadURL;
                        link.download = res.fileName;
                        link.click();
                        }
                    })
            }else {
                this.toastr.error('File Not Found!');
            }
        })
     
    }

    saveAndSendToSftp(){
        if (!this.validateFileUpload()) {
            let msg = 'Invalid attachment files. ';

            if (this.invalidExtArr.length > 0) {
                let exts = this.invalidExtArr.join(', ');
                msg += 'File extension not allowed: '+ exts;
            }
            this.toastr.error(msg);
            return;
        }
        
        if (this.disputeDetail.outcome && this.disputeDetail.outcome.length) {
            let msg = 'Can not send to merchant! Dispute already has outcome.';
            this.toastr.error(msg);
            return;
        }

        let body = {
            "id": this.disputeDetail.id,
            "disputeSender": this.disputeDetail.disputeSender,
            "onepayPic": this.disputeDetail.onepayPic,
            "sendDisputeTo": this.disputeDetail.sendDisputeTo ? this.disputeDetail.sendDisputeTo : "",
            "merchantRespond": this.disputeDetail.merchantRespond,
            "businessCategory": this.disputeDetail && this.disputeDetail.businessCategory && this.disputeDetail.businessCategory.value ? this.disputeDetail.businessCategory.value : '',
            "merchantGroup": this.disputeDetail.merchantGroup,
            "disputeStage": this.disputeDetail.disputeStage ? this.disputeDetail.disputeStage : "",
            "disputeReason": this.disputeDetail.disputeReason,
            "disputeCode": this.disputeDetail.disputeCode,
            "disputedAmount": this.disputeDetail.disputeAmount,
            "disputeCurrency": this.disputeDetail.disputeCurrency,
            "dueDate": this.formatDate(this.disputeDetail.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "disputeDate": this.formatDate(this.disputeDetail.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "outcome": this.disputeDetail.outcome,
            "refNumber": this.disputeDetail.refNumber,
            "note": this.disputeDetail.note,
            "merchantName": this.disputeDetail.merchantName,
            "orderReference": this.disputeDetail.orderReference,
            "merchantId": this.disputeDetail.merchantId,
            "cardNumber": this.disputeDetail.cardNumber,
            "merchantTransactionReference": this.disputeDetail.merchantTransactionReference,
            "transactionAmount": this.disputeDetail.transactionAmount,
            "authorisationCode": this.disputeDetail.authorisationCode,
            "transactionDate": this.formatDate(this.disputeDetail.transactionDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "operatorId": this.disputeDetail.operatorId,
            "operatorName": this.disputeDetail.operatorName
        };
        this.disputeService.updateDispute(this.disputeDetail.id, body).subscribe(res => {
            if (res && res.code == 200) {
                this.reloadDetail();

                this.disputeService.sendToSftp(this.disputeDetail.id.toString(),this.files,this.global.activeProfile.email).subscribe(res => {
                    if (res) {
                        this.toastr.success('Successfully sent to SFPT', 'Success');
                    }
                });
            }
        });
    }

    saveDispute() {
        let body = {
            "id": this.disputeDetail.id,
            "disputeSender": this.disputeDetail.disputeSender,
            "onepayPic": this.disputeDetail.onepayPic,
            "sendDisputeTo": this.disputeDetail.sendDisputeTo || '',
            "merchantRespond": this.disputeDetail.merchantRespond,
            "businessCategory": this.disputeDetail.businessCategory || '',
            "merchantGroup": this.disputeDetail.merchantGroup,
            "disputeStage": this.disputeDetail.disputeStage || '',
            "disputeReason": this.disputeDetail.disputeReason,
            "disputeCode": this.disputeDetail.disputeCode,
            "disputedAmount": this.disputeDetail.disputeAmount,
            "disputeCurrency": this.disputeDetail.disputeCurrency,
            "dueDate": this.formatDate(this.disputeDetail.dueDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "disputeDate": this.formatDate(this.disputeDetail.disputeDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "outcome": this.disputeDetail.outcome,
            "refNumber": this.disputeDetail.refNumber,
            "note": this.disputeDetail.note,
            "merchantName": this.disputeDetail.merchantName,
            "orderReference": this.disputeDetail.orderReference,
            "merchantId": this.disputeDetail.merchantId,
            "cardNumber": this.disputeDetail.cardNumber,
            "merchantTransactionReference": this.disputeDetail.merchantTransactionReference,
            "transactionAmount": this.disputeDetail.transactionAmount,
            "authorisationCode": this.disputeDetail.authorisationCode,
            "transactionDate": this.formatDate(this.disputeDetail.transactionDate.toString(), 'dd/MM/yyyy HH:mm:ss'),
            "operatorId": this.disputeDetail.operatorId,
            "operatorName": this.disputeDetail.operatorName,
            "fraudInves": this.disputeDetail.fraudInves,
            "sftpAppleFileFromIssuers": this.disputeDetail.sftpAppleFileFromIssuers,
        };
        this.disputeService.updateDispute(this.disputeDetail.id, body).subscribe(res => {
            if (res && res.code == 200) {
                this.disputeUtil.notiSaveDispute(this.disputeDetail);
                this.reloadDetail();
                this.toastr.success('Successfully saved information', 'Success');
            }
        });
    }

    isInvalid(): boolean {
        return this.disputeDetailForm.invalid;
    }

    formatDate(inputDate: string, format: string): string {
        var outputDate = '';
        if (inputDate) {
            outputDate = this.datePipe.transform(new Date(inputDate), format);
        }
        return outputDate;
    }

    convertDisputeStage(disputeStage) {
        let disputeStageLabel = '';
        for (let i = 0; i < this.listDisputeStage.length; i++) {
            if (disputeStage == this.listDisputeStage[i].value) {
                disputeStageLabel = this.listDisputeStage[i].label;
            }
        }
        return disputeStageLabel;
    }

    convertDisputeStatus(status) {
        if (status == 'created') return 'Created';
        else if (status == 'need_merchant_response') return 'Need Merchant Response';
        else if (status == 'waiting_for_onepay_review') return 'Waiting for OnePay review';
        else if (status == 'resolved') return 'Resolved';
        else if (status == 'dispute_reminded') return 'Dispute Reminded';
        else return status;
    }

    convertChannel(channel) {
        if (channel === 'QT') return 'INT';
        else if (channel === 'ND') return 'DOM';
        else return 'APP';
    }

    getRequiredLabel(label: string) {
        return (this.isRISK && this.isINT) ? label : `${label} *`;
    }

    validateFileUpload() {
        this.invalidExtArr = [];

        for (let i = 0; i < this.files.length; i++) {
            let file = this.files[i];
            let filename = file.name;
            let ext = this.getFileExtension(filename);
            if (!this.allowedExtensionArr.includes(ext)) {
                console.log('File extension not allowed: ', ext);
                this.invalidExtArr.push(ext);
            }
        }

        if (this.invalidExtArr.length == 0) {
            return true;
        } 
        return false;
    }

    getFileExtension(filename: string) {
        let arr = filename.split('.');
        if (arr.length <= 1) {
            return "";
        }
        return "." + arr.pop();
    }

    
    onFilechange() {
        if (this.fileInputRef?.nativeElement?.files?.length) {
            // let newFiles: File[] = Array.from(this.fileInputRef.nativeElement.files);
            
            //chỉ cho upload 1 file
            let newFile: File = this.fileInputRef.nativeElement.files[0];
            let valid = true;
            if (!this.validateFile(newFile)) {
                valid = false;
                return;
            }

            if (valid) {
                // this.files = this.files.concat(newFile);
                this.files = [newFile];
                // lưu vào local để khi back ra màn list thì lấy được thông tin này
                this.fileDataService.sendFileData(this.files[0], this.disputeDetail.id.toString());
            }
        }
        this.fileInputRef.nativeElement.value = "";
    }

    validateFile(newFile) {
        let ext = this.getFileExtension(newFile.name);

        if (!this.allowedExtensionArr.includes(ext)) {
            let msg = 'File extension not allowed: '+ ext;
            this.toastr.error(msg);
            return false;
        }

        for (let oldFile of this.files) {
            if (oldFile.name == newFile.name) {
                let msg = 'Duplicate file name: '+ oldFile.name;
                this.toastr.error(msg);
                return false;
            }
        }

        return true;
    }
    
    removeFile(index) {
        this.fileDataService.removeFileDataByName(this.files[index].name);
        this.files.splice(index, 1);
    }

    addNote(){
        const dialogRef: DynamicDialogRef = this.dialogEmailService.open(NoteModalComponent, {
            data:{dataEditer: this.dataEditer},
        });
        dialogRef.onClose.subscribe((data: any) => {
            this.dataEditer = data.note;
        });
    }

    testEmailFormat(emails: string) {
        const regularExpression = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (!emails) {
            return;
        }
        let emailArr = emails.split(';');
        for (var i = 0; i < emailArr.length; i++) {
            if (emailArr[i].trim() && !regularExpression.test(emailArr[i].trim().toLocaleLowerCase())) {
                this.disputeDetailForm.controls['sendDisputeTo'].setErrors({ 'pattern': true });
                return;
            }
        }
    }

    isKbank(data: any) {
        return this.disputeUtil.isKbank(data);
    }

    displayFilename(filename: string) {
        if (filename.length <= this.MAX_FILENAME_LENGTH_DISPLAY) {
            return filename;
        }
        return filename.substring(0, this.MAX_FILENAME_LENGTH_DISPLAY) + "...";
    }

    openNewTab(): void {
        const queryParams = this.popupData.data.queryParams;
        let url = this.router.createUrlTree(['/iportal/risk-dispute-management/dispute', this._disputeId], { queryParams }).toString();
        if (queryParams['back_location'] === 'ss-list') {
            url = this.router.createUrlTree(['/iportal/ss-dispute-management/dispute', this._disputeId], { queryParams }).toString();
        }
        window.open(url, '_blank');
    }

}