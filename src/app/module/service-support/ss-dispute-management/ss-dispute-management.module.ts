import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SSTransManagementService } from '@service/ss-trans-management.service';
import { SSDisputeManagementSearch } from './search/ss-dispute-management-search.component';
import { SharedModule } from '@shared/shared.module';
import { SSMerchantManagementRoutingModule } from './ss-dispute-management-routing.module';
import { InternationalService } from '@service/international.service';
import { SSDisputeManagementService } from '@service/ss-dispute-management.service';
import { DisputeDetailComponent } from './list/dispute-detail/dispute-detail-component';
import { DisputeDetailSearchForm } from './list/dispute-detail/search/search.component';
import { FileOnePayForm } from './list/sftp-files-onepay-response/search_file_op/search.component';
import { DisputeConfirmModalComponent } from './list/dispute-detail/confirm-modal/confirm-modal.component';
import { SendEmailDisputeComponent } from './list/dispute-detail/send-email-modal/email-modal.component';
import { CurrencyMaskModule } from "ng2-currency-mask";
import { SSDisputeManagementComponent } from './list/ss-dispute-management/ss-dispute-management-component';
import { RiskDisputeDomesticManagementComponent}from './list/risk-dispute-management-domestic/risk-dispute-management-domestic-component';
import { RiskDisputeInternationalManagementComponent}from './list/risk-dispute-management-international/risk-dispute-management-international-component';
import { DisputeHistoryComponent } from './list/dispute-detail/dispute-history/dispute-history.component';
import { FileHistoryComponent } from './list/dispute-detail/file-history/file-history.component';
import { RiskDisputeInternationalOnepayResponseFilesComponent } from './list/sftp-files-onepay-response/sftp-onepay-response-files-component';
import { RiskDisputeInternationalAccquirerRequestFilesComponent } from './list/sftp-files-accquirer-request/sftp-accquirer-request-files-component';
import { SearchFileAccquirer } from './list/sftp-files-accquirer-request/search_file_accquirer/search-file-accquirer.component';
import { NoteModalComponent } from './list/dispute-detail/note-modal/note-modal';
import { DisputeColumnDisplayComponent } from './list/column-display/dispute-column-display.component';
import { OrderListModule } from 'primeng/orderlist';
// import { DPCurrencyPipe } from '../pipes/DPCurrencyPipe';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { LocalService } from '@service/local-service.service';
import { DisputeUtils } from './dispute-utils';
import { CreateCSVFileModalComponent } from './list/risk-dispute-management-international/create-csv-file-dialog/create-csv-file-modal.component';
import { ChipModule } from 'primeng/chip';
import { UpdateDisputeByBatchComponent } from './list/update-dispute-dialog/update-by-batch.component';
import { ShareDisputeInputDialogComponent } from './list/share-dispute-input-dialog/share-dispute-input-dialog.component';
import { FilesDisputeManagementComponent } from './list/files-dispute-management/files-dispute-management.component';
import { FilesSearchComponent } from './list/files-dispute-management/files-search/files-search.component';
import { FileDetailComponent } from './list/files-dispute-management/file-detail/file-detail.component';

// const DP_PIPES = [
//   DPCurrencyPipe
// ]
@NgModule({
  declarations: [
    SSDisputeManagementComponent,
    RiskDisputeDomesticManagementComponent,
    RiskDisputeInternationalManagementComponent,
    SSDisputeManagementSearch,
    DisputeDetailComponent,
    DisputeConfirmModalComponent,
    DisputeDetailSearchForm,
    FileOnePayForm,
    SearchFileAccquirer,
    SendEmailDisputeComponent,
    DisputeHistoryComponent,
    FileHistoryComponent,
    RiskDisputeInternationalOnepayResponseFilesComponent,
    RiskDisputeInternationalAccquirerRequestFilesComponent,
    NoteModalComponent,
    DisputeColumnDisplayComponent,
    UpdateDisputeByBatchComponent,
    ShareDisputeInputDialogComponent,
    CreateCSVFileModalComponent,
    FilesDisputeManagementComponent,
    FilesSearchComponent,
    FileDetailComponent,
  ],
  imports: [
    SSMerchantManagementRoutingModule,
    CommonModule,
    SharedModule,
    OrderListModule,
    CurrencyMaskModule,
    ChipModule
  ],
  providers: [
    SSTransManagementService,
    InternationalService,
    SSDisputeManagementService,
    DynamicDialogConfig,
    DisputeUtils,
    LocalService
  ],
})
export class SSDisputeManagementModule { }
