export const listBusinessCategory = [
  {
    label: '',
    value: ''
  }
];
export const listMerchantGroup = [
  {
    label: 'merchant group 1',
    value: '1'
  }
];
export const listOnepayPic = [
];

export const listDisputeStage = [
  {
    label: '',
    value: ''
  },
  {
    label: 'Retrieval Request',
    value: '1'
  },
  {
    label: 'Pre-Arbitration',
    value: '2'
  },
  {
    label: 'Chargeback',
    value: '3'
  },
  {
    label: 'Arbitration',
    value: '4'
  },
  {
    label: 'Chargeback reversal',
    value: '5'
  },
  {
    label: 'Credit chargeback',
    value: '6'
  },
  {
    label: 'Fraud Report',
    value: '7'
  }
];
export const listDisputeStage_1 = [
  {
    label: '',
    value: ''
  },
  {
    label: 'Retrieval Request',
    value: '1'
  },
  {
    label: 'Pre-Arbitration',
    value: '2'
  },
  {
    label: 'Chargeback',
    value: '3'
  },
  {
    label: 'Arbitration',
    value: '4'
  },
  {
    label: 'Chargeback reversal',
    value: '5'
  },
  {
    label: 'Credit chargeback',
    value: '6'
  },
  {
    label: 'Fraud Report',
    value: '7'
  }
];
export const listDisputeReason = [
];

export const listDisputeCode = [
];

export const listDisputeCodeSS = [
  { "label": "", "value": "" },
  { "label": "ATM01: Merchandise or Service not received", "value": "10001" },
  { "label": "ATM02: Fraud / Not recognize transaction", "value": "10002" },
  { "label": "ATM03: Transaction failed but got deducted", "value": "10003" },
  { "label": "ATM04: Refund not processed", "value": "10004" }
];

export const listDisputeCurrency = [
  { label: '', value: '' },
  { label: 'VND', value: 'VND' },
  { label: 'USD', value: 'USD' }
];
export const listDisputeCurrency_1 = [
  { label: 'VND', value: 'VND' },
  { label: 'USD', value: 'USD' }
];
export const disputeStatusList = [
  {
      label: 'Created',
      value: 'created'
  }, {
      label: 'Need Merchant Response',
      value: 'need_merchant_response'
  }, {
      label: 'Resolved',
      value: 'resolved'
  }
];
export const transCurrencyList = [
  { label: 'VND', value: 'VND' },
  { label: 'USD', value: 'USD' },
  { value: 'THB', label: 'THB' },
  { value: 'SGD', label: 'SGD' },
  { value: 'MYR', label: 'MYR' },
  { value: 'IDR', label: 'IDR' },
  { value: 'JPY', label: 'JPY' },
  { value: 'KRW', label: 'KRW' },
  { value: 'TWD', label: 'TWD' },
  { value: 'CNY', label: 'CNY' }
];

export const transTypeList = [
  {
    label: 'Authorize',
    value: 'Authorize'
  },
  {
    label: 'Capture',
    value: 'Capture'
  },
  {
    label: 'Purchase',
    value: 'Purchase'
  },
  {
    label: 'Refund',
    value: 'Refund'
  },
  {
    label: 'Refund Capture',
    value: 'Refund capture'
  },
  {
    label: 'Refund Dispute',
    value: 'Refund Dispute'
  },
  {
    label: 'Request Refund',
    value: 'Request Refund'
  },
  {
    label: 'Void Authorize',
    value: 'Void authorize'
  },
  {
    label: 'Void',
    value: 'Void'
  },
  {
    label: 'Void Capture',
    value: 'Void capture'
  },
  {
    label: 'Void Purchase',
    value: 'Void purchase'
  },
  {
    label: 'Void Refund',
    value: 'Void refund'
  },
  {
    label: 'Void Refund Capture',
    value: 'Void refund capture'
  }
];

export const pageSizeList = [
  { label: '100', value: '100' },
  { label: '150', value: '150' },
  { label: '200', value: '200' }
];

export const pageList = [
  { label: '1', value: 1 },
  { label: '2', value: 2 },
  { label: '3', value: 3 },
  { label: '4', value: 4 },
  { label: '5', value: 5 }
];

export const fileStatusList = [
  { id:1, name:'Chưa gửi' }, 
  { id:2, name:'Đã gửi' }
];

export const listReasonRiskQT = [
  {
      label: 'Merchandise or Service not received',
      value: '1'
  },
  {
      label: 'Cancelled Merchandise/Services',
      value: '2'
  },
  {
      label: 'Merchandise or Service not as described',
      value: '3'
  },
  {
      label: 'Fraud',
      value: '4'
  },
  {
      label: 'Duplicate Processing/ Paid by other means',
      value: '5'
  },
  {
      label: 'Incorrect Amount',
      value: '6'
  },
  {
      label: 'Canceled recurring billing',
      value: '7'
  },
  {
      label: 'Request a copy of transaction receipt',
      value: '8'
  },
  {
      label: 'Not recognize transaction',
      value: '9'
  },
  {
      label: 'Others',
      value: '10'
  },
  {
      label: 'Credit not processed',
      value: '11'
  }
];

export const listReasonRiskND = [
  {
      label: 'Merchandise or Service not received',
      value: '12'
  },
  {
      label: 'Fraud / Not recognize transaction',
      value: '13'
  },
  {
      label: 'Transaction failed but got deducted',
      value: '14'
  },
  {
      label: 'Refund not processed',
      value: '15'
  }
];

export const listOutcomeSearchSS = [
  {
    label: 'Blank',
    value: 'blank'
  },
  {
    label: 'Order confirmed',
    value: '8'
  },
  {
    label: 'Refunded',
    value: 'Refunded'
  },
  {
    label: 'Resolved',
    value: 'Resolved'
  },
];
export const listOutcomeSearchRiskINT = [
  {
    label: 'Blank',
    value: 'blank'
  },
  {
    label: 'Submit evidence',
    value: '1'
  },
  {
    label: 'Lost due to Overdue',
    value: '3'
  },
  {
    label: 'Lost at Arbitration',
    value: '4'
  },
  {
    label: 'Won at Arbitration',
    value: '5'
  },
  {
    label: 'Dispute is canceled',
    value: '6'
  },
  {
    label: 'Funds back to merchant',
    value: '7'
  },
];
export const listOutcomeSearchRiskDomes = [
  {
    label: 'Blank',
    value: 'blank'
  },
  {
    label: 'Order confirmed',
    value: '8'
  },
  {
    label: 'Refunded',
    value: 'Refunded'
  },
  {
    label: 'Resolved',
    value: 'Resolved'
  },
];

export const gateList = [
  {'value': 'QT', 'label': 'International'},
  {'value': 'ND', 'label': 'Domestic'},
  {'value': 'QR', 'label': 'Mobile App'},
];

export const merchantChannelList = [
  { label: 'Ecom', value: 'ecom' },
  { label: 'Upos', value: 'upos' },
];

export const transStateList = [
  {
      label: 'Successful',
      value: 'Successful'
  },
  {
      label: 'Failed',
      value: 'Failed'
  },
  {
      label: 'Processing',
      value: 'Processing'
  },
  {
      label: 'Waiting for authentication',
      value: 'Waiting for authentication'
  },
  {
      label: 'Incomplete',
      value: 'Incomplete'
  },
  {
      label: 'Fraud',
      value: 'Fraud'
  },
  {
      label: 'Pending',
      value: 'pending'
  },
  {
      label: 'Waiting for Approval',
      value: 'Waiting for approval'
  },
  {
      label: "Waiting for OnePay's Approval",
      value: "Waiting for OnePays Approval"
  }
];

export const SSDisputeCodeList = [
  {
    label: 'Blank',
    value: 'blank',
  },
  {
    label: 'ATM01: Merchandise or Service not received',
    value: '10001',
  },
  {
    label: 'ATM02: Fraud / Not recognize transaction',
    value: '10002',
  },
  {
    label: 'ATM03: Transaction failed but got deducted',
    value: '10003',
  },
  {
    label: 'ATM04: Refund not processed',
    value: '10004',
  }
];
export const RiskDisputeInterColumns = [
  { name: 'Dispute Status', code: 'disputeStatus', order: 0, active: true },
  { name: 'Dispute Date', code: 'disputeDate', order: 1, active: true },
  { name: 'Due Date', code: 'dueDate', order: 2, active: true },
  { name: 'Merchant Channel', code: 'merchantChannel', order: 3, active: true },
  { name: 'Partner Name', code: 'partnerName', order: 4, active: true },
  { name: 'Merchant ID', code: 'merchantId', order: 5, active: true },
  { name: 'Trans. ID', code: 'transId', order: 6, active: true },
  { name: 'Order Ref.', code: 'orderRef', order: 7, active: true },
  { name: 'Merchant Trans. Ref.', code: 'merchantTransRef', order: 8, active: true },
  { name: 'ACQ', code: 'acq', order: 9, active: true },
  { name: 'MID', code: 'MID', order: 10, active: true },
  { name: 'Card List', code: 'cardType', order: 11, active: true },
  { name: 'Card Number', code: 'cardNumber', order: 12, active: true },
  { name: 'Auth. Code', code: 'authCode', order: 13, active: true },
  { name: 'Trans. Amount', code: 'transactionAmount', order: 14, active: true },
  { name: 'Trans. Date', code: 'transactionDate', order: 15, active: true },
  { name: 'Trans. Type', code: 'transactionType', order: 16, active: true },
  { name: 'Trans. State', code: 'transactionState', order: 17, active: true },
  { name: 'Refunded', code: 'refundAmount', order: 18, active: true },
  { name: 'Dispute Amount', code: 'disputeAmount', order: 19, active: true },
  { name: 'Dispute Reason', code: 'disputeReason', order: 20, active: true },
  { name: 'Dispute Code', code: 'disputeCode', order: 21, active: true },
  { name: 'Dispute Stage', code: 'disputeStage', order: 22, active: true },
  { name: 'Outcome', code: 'outcome', order: 23, active: true },
  { name: 'Fraud Investigation', code: 'fraudInves', order: 24, active: true },
  { name: 'Case ID', code: 'caseId', order: 25, active: true },
  { name: 'File', code: 'file', order: 26, active: true },
  { name: 'File Status', code: 'fileStatus', order: 27, active: true },
  { name: 'OnePay PIC', code: 'onepayPic', order: 28, active: true },
  { name: 'Last Update', code: 'lastUpdate', order: 29, active: true },
  { name: 'Dispute file from Issuers', code: 'disputeFileFromIssuers', order: 30, active: true },
];
export const RiskDisputeDomesColumns = [
  { name: 'Dispute Status', code: 'disputeStatus', order: 0, active: true },
  { name: 'OnePay PIC', code: 'onepayPic', order: 1, active: true },
  { name: 'Dispute Date', code: 'disputeDate', order: 2, active: true },
  { name: 'Due Date', code: 'dueDate', order: 3, active: true },
  { name: 'Last Update', code: 'lastUpdate', order: 4, active: true },
  { name: 'Merchant ID', code: 'merchantId', order: 5, active: true },
  { name: 'Trans. ID', code: 'transId', order: 6, active: true },
  { name: 'Order Ref.', code: 'orderRef', order: 7, active: true },
  { name: 'Merchant Trans. Ref.', code: 'merchantTransRef', order: 8, active: true },
  { name: 'Channel', code: 'channel', order: 9, active: true },
  { name: 'ACQ', code: 'acq', order: 10, active: true },
  { name: 'Card List', code: 'cardType', order: 11, active: true },
  { name: 'Card Number', code: 'cardNumber', order: 12, active: true },
  { name: 'Auth. Code', code: 'authCode', order: 13, active: true },
  { name: 'Transaction Amount', code: 'transactionAmount', order: 14, active: true },
  { name: 'Refund Amount', code: 'refundAmount', order: 15, active: true },
  { name: 'Dispute Amount', code: 'disputeAmount', order: 16, active: true },
  { name: 'Dispute Reason', code: 'disputeReason', order: 17, active: true },
  { name: 'Outcome', code: 'outcome', order: 18, active: true }
];
export const SSDisputeColumns = [
  { name: 'Dispute Status', code: 'disputeStatus', order: 0, active: true },
  { name: 'Dispute Date', code: 'disputeDate', order: 1, active: true },
  { name: 'Merchant Channel', code: 'merchantChannel', order: 2, active: true },
  { name: 'Gate', code: 'channel', order: 3, active: true },
  { name: 'Partner Name', code: 'partnerName', order: 4, active: true },
  { name: 'Merchant ID', code: 'merchantId', order: 5, active: true },
  { name: 'Trans. ID', code: 'transId', order: 6, active: true },
  { name: 'Order Ref.', code: 'orderRef', order: 7, active: true },
  { name: 'Merchant Trans. Ref.', code: 'merchantTransRef', order: 8, active: true },
  { name: 'Card List', code: 'cardType', order: 9, active: true },
  { name: 'Card Number', code: 'cardNumber', order: 10, active: true },
  { name: 'Trans. Amount', code: 'transactionAmount', order: 11, active: true },
  { name: 'Trans. Date', code: 'transactionDate', order: 12, active: true },
  { name: 'Trans. Type', code: 'transactionType', order: 13, active: true },
  { name: 'Trans. State', code: 'transactionState', order: 14, active: true },
  { name: 'Refunded', code: 'refundAmount', order: 15, active: true },
  { name: 'Dispute Amount', code: 'disputeAmount', order: 16, active: true },
  { name: 'Dispute Code', code: 'disputeCode', order: 17, active: true },
  { name: 'Outcome', code: 'outcome', order: 18, active: true },
  { name: 'OnePay PIC', code: 'onepayPic', order: 19, active: true },
  { name: 'Due Date', code: 'dueDate', order: 20, active: true },
  { name: 'Last Update', code: 'lastUpdate', order: 21, active: true },
];
export const TransSearchACQList = [
  { label: 'Vietcombank - MIGS', value: '1' },
  { label: 'Vietinbank - CYBS', value: '2' },
  { label: 'CUP', value: '3' },
  { label: 'Vietcombank - CYBS', value: '4' },
  { label: 'Vietcombank - MPGS', value: '5' },
  { label: 'PayPal', value: '6' },
  { label: 'Sacombank - CYBS', value: '7' },
  { label: 'BIDV - CYBS', value: '8' },
  { label: 'Sacombank - MPGS', value: '9' },
  { label: 'Techcombank - CYBS', value: '10' },
  { label: 'VPB - MPGS', value: '11' },
  { label: 'KBank - CYBS', value: '12' },
  { label: 'VPB - CYBS', value: '13' },
  { label: 'Sacombank', value: 'Sacombank' },
  { label: 'KBank', value: 'KBank' },
  { label: 'OnePay', value: 'OnePay' },
  { label: 'Vietinbank - MPGS', value: '17' }
];
export const listFraudInves = [
  { label: '', value: '' },
  { label: 'Friendly fraud', value: 'Friendly fraud' },
  { label: 'True fraud', value: 'True fraud' },
  { label: 'Undefined', value: 'Undefined' },
];
export const listFraudInvesSearch = [
  { label: 'Blank', value: 'blank' },
  { label: 'Friendly fraud', value: 'Friendly fraud' },
  { label: 'True fraud', value: 'True fraud' },
  { label: 'Undefined', value: 'Undefined' },
];
export const disputeFromFileIssuers = [
  { label: 'Yes', value: 'Yes' },
  { label: 'No', value: 'No' },
];